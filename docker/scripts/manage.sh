#!/bin/bash
# Docker环境管理脚本

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DOCKER_DIR="$PROJECT_ROOT/docker"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker环境管理脚本

用法: $0 [命令] [选项]

命令:
    init            初始化Docker环境
    clean           清理Docker资源
    backup          备份数据
    restore         恢复数据
    update          更新服务
    monitor         监控服务状态
    setup-secrets   设置生产环境密钥

选项:
    -h, --help      显示此帮助信息
    -e, --env       指定环境 (dev|test|prod)
    -f, --force     强制执行
    -v, --verbose   详细输出

示例:
    $0 init -e dev              # 初始化开发环境
    $0 clean -f                 # 强制清理所有Docker资源
    $0 backup -e prod           # 备份生产环境数据
    $0 setup-secrets            # 设置生产环境密钥

EOF
}

# 默认参数
COMMAND=""
ENVIRONMENT=""
FORCE=false
VERBOSE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        init|clean|backup|restore|update|monitor|setup-secrets)
            COMMAND="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查命令参数
if [[ -z "$COMMAND" ]]; then
    log_error "请指定命令"
    show_help
    exit 1
fi

# 初始化环境
init_environment() {
    local env=${1:-dev}
    
    log_info "初始化 $env 环境..."
    
    # 创建必要的目录
    log_info "创建必要的目录..."
    mkdir -p "$PROJECT_ROOT/uploads"
    mkdir -p "$PROJECT_ROOT/temp"
    mkdir -p "$PROJECT_ROOT/logs"
    
    # 复制环境配置文件
    local env_file="$DOCKER_DIR/configs/env/.env.$env"
    if [[ -f "$env_file" ]] && [[ ! -f "$PROJECT_ROOT/.env" ]]; then
        log_info "复制环境配置文件..."
        cp "$env_file" "$PROJECT_ROOT/.env"
        log_success "环境配置文件已复制到 .env"
    fi
    
    # 设置文件权限
    log_info "设置文件权限..."
    chmod 755 "$PROJECT_ROOT/uploads"
    chmod 755 "$PROJECT_ROOT/temp"
    chmod 755 "$PROJECT_ROOT/logs"
    
    # 检查Docker和Docker Compose
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "$env 环境初始化完成"
}

# 清理Docker资源
clean_docker() {
    log_info "清理Docker资源..."
    
    if [[ "$FORCE" == true ]]; then
        log_warning "强制清理所有Docker资源..."
        
        # 停止所有容器
        if docker ps -q | grep -q .; then
            log_info "停止所有运行中的容器..."
            docker stop $(docker ps -q)
        fi
        
        # 删除所有容器
        if docker ps -aq | grep -q .; then
            log_info "删除所有容器..."
            docker rm $(docker ps -aq)
        fi
        
        # 删除所有镜像
        if docker images -q | grep -q .; then
            log_info "删除所有镜像..."
            docker rmi $(docker images -q) -f
        fi
        
        # 清理网络
        log_info "清理网络..."
        docker network prune -f
        
        # 清理卷
        log_info "清理卷..."
        docker volume prune -f
        
        # 清理构建缓存
        log_info "清理构建缓存..."
        docker builder prune -af
        
    else
        log_info "清理未使用的Docker资源..."
        
        # 清理停止的容器
        docker container prune -f
        
        # 清理未使用的镜像
        docker image prune -f
        
        # 清理未使用的网络
        docker network prune -f
        
        # 清理未使用的卷
        docker volume prune -f
        
        # 清理构建缓存
        docker builder prune -f
    fi
    
    log_success "Docker资源清理完成"
}

# 备份数据
backup_data() {
    local env=${1:-prod}
    local backup_dir="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
    
    log_info "备份 $env 环境数据到 $backup_dir..."
    
    mkdir -p "$backup_dir"
    
    # 备份数据库
    log_info "备份MySQL数据库..."
    local mysql_container=""
    case $env in
        dev) mysql_container="audit_mysql_dev" ;;
        test) mysql_container="audit_mysql_test" ;;
        prod) mysql_container="audit_mysql_prod" ;;
    esac
    
    if docker ps --format "table {{.Names}}" | grep -q "$mysql_container"; then
        docker exec "$mysql_container" mysqldump -u root -prootpassword audit_db > "$backup_dir/mysql_backup.sql"
        log_success "MySQL数据库备份完成"
    else
        log_warning "MySQL容器未运行，跳过数据库备份"
    fi
    
    # 备份上传文件
    if [[ -d "$PROJECT_ROOT/uploads" ]]; then
        log_info "备份上传文件..."
        cp -r "$PROJECT_ROOT/uploads" "$backup_dir/"
        log_success "上传文件备份完成"
    fi
    
    # 备份日志文件
    if [[ -d "$PROJECT_ROOT/logs" ]]; then
        log_info "备份日志文件..."
        cp -r "$PROJECT_ROOT/logs" "$backup_dir/"
        log_success "日志文件备份完成"
    fi
    
    # 创建备份信息文件
    cat > "$backup_dir/backup_info.txt" << EOF
备份时间: $(date)
环境: $env
备份内容:
- MySQL数据库
- 上传文件
- 日志文件
EOF
    
    log_success "数据备份完成: $backup_dir"
}

# 恢复数据
restore_data() {
    local backup_dir="$1"
    local env=${2:-prod}
    
    if [[ -z "$backup_dir" ]]; then
        log_error "请指定备份目录"
        exit 1
    fi
    
    if [[ ! -d "$backup_dir" ]]; then
        log_error "备份目录不存在: $backup_dir"
        exit 1
    fi
    
    log_info "从 $backup_dir 恢复 $env 环境数据..."
    
    # 恢复数据库
    if [[ -f "$backup_dir/mysql_backup.sql" ]]; then
        log_info "恢复MySQL数据库..."
        local mysql_container=""
        case $env in
            dev) mysql_container="audit_mysql_dev" ;;
            test) mysql_container="audit_mysql_test" ;;
            prod) mysql_container="audit_mysql_prod" ;;
        esac
        
        if docker ps --format "table {{.Names}}" | grep -q "$mysql_container"; then
            docker exec -i "$mysql_container" mysql -u root -prootpassword audit_db < "$backup_dir/mysql_backup.sql"
            log_success "MySQL数据库恢复完成"
        else
            log_error "MySQL容器未运行，无法恢复数据库"
        fi
    fi
    
    # 恢复上传文件
    if [[ -d "$backup_dir/uploads" ]]; then
        log_info "恢复上传文件..."
        rm -rf "$PROJECT_ROOT/uploads"
        cp -r "$backup_dir/uploads" "$PROJECT_ROOT/"
        log_success "上传文件恢复完成"
    fi
    
    log_success "数据恢复完成"
}

# 更新服务
update_services() {
    local env=${1:-prod}
    
    log_info "更新 $env 环境服务..."
    
    # 拉取最新镜像
    log_info "拉取最新镜像..."
    "$SCRIPT_DIR/deploy.sh" "$env" down
    "$SCRIPT_DIR/build.sh" "$env" --no-cache
    "$SCRIPT_DIR/deploy.sh" "$env" up -d --build
    
    # 健康检查
    log_info "执行健康检查..."
    "$SCRIPT_DIR/health-check.sh" "$env"
    
    log_success "$env 环境服务更新完成"
}

# 监控服务状态
monitor_services() {
    local env=${1:-prod}
    
    log_info "监控 $env 环境服务状态..."
    
    # 显示容器状态
    log_info "容器状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep audit
    
    # 显示资源使用情况
    log_info "资源使用情况:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" | grep audit
    
    # 检查服务健康状态
    "$SCRIPT_DIR/health-check.sh" "$env" --verbose
}

# 设置生产环境密钥
setup_secrets() {
    log_info "设置生产环境密钥..."
    
    local secrets_dir="$DOCKER_DIR/secrets"
    
    # 检查模板文件
    if [[ ! -f "$secrets_dir/mysql_root_password.txt.template" ]]; then
        log_error "密钥模板文件不存在"
        exit 1
    fi
    
    # 生成MySQL root密码
    if [[ ! -f "$secrets_dir/mysql_root_password.txt" ]]; then
        log_info "生成MySQL root密码..."
        openssl rand -base64 32 > "$secrets_dir/mysql_root_password.txt"
        chmod 600 "$secrets_dir/mysql_root_password.txt"
    fi
    
    # 生成MySQL用户密码
    if [[ ! -f "$secrets_dir/mysql_password.txt" ]]; then
        log_info "生成MySQL用户密码..."
        openssl rand -base64 32 > "$secrets_dir/mysql_password.txt"
        chmod 600 "$secrets_dir/mysql_password.txt"
    fi
    
    # 生成应用密钥
    if [[ ! -f "$secrets_dir/secret_key.txt" ]]; then
        log_info "生成应用密钥..."
        openssl rand -base64 64 > "$secrets_dir/secret_key.txt"
        chmod 600 "$secrets_dir/secret_key.txt"
    fi
    
    log_success "生产环境密钥设置完成"
    log_warning "请妥善保管密钥文件，不要提交到版本控制系统"
}

# 主逻辑
case $COMMAND in
    init)
        init_environment "$ENVIRONMENT"
        ;;
    clean)
        clean_docker
        ;;
    backup)
        backup_data "$ENVIRONMENT"
        ;;
    restore)
        if [[ -z "$2" ]]; then
            log_error "请指定备份目录"
            exit 1
        fi
        restore_data "$2" "$ENVIRONMENT"
        ;;
    update)
        update_services "$ENVIRONMENT"
        ;;
    monitor)
        monitor_services "$ENVIRONMENT"
        ;;
    setup-secrets)
        setup_secrets
        ;;
    *)
        log_error "不支持的命令: $COMMAND"
        show_help
        exit 1
        ;;
esac
