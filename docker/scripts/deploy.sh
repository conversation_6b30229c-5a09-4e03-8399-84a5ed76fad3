#!/bin/bash
# Docker部署脚本

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DOCKER_DIR="$PROJECT_ROOT/docker"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker部署脚本

用法: $0 [选项] [环境] [操作]

环境:
    dev         开发环境
    test        测试环境
    prod        生产环境

操作:
    up          启动服务
    down        停止服务
    restart     重启服务
    status      查看服务状态
    logs        查看服务日志
    scale       扩缩容服务
    elk         管理ELK日志系统

选项:
    -h, --help          显示此帮助信息
    -f, --force         强制执行操作
    -d, --detach        后台运行
    --build             启动前重新构建镜像
    --pull              启动前拉取最新镜像
    --remove-orphans    移除孤立容器
    --scale SERVICE=NUM 设置服务副本数

示例:
    $0 dev up                       # 启动开发环境
    $0 prod up --build             # 构建并启动生产环境
    $0 test down                   # 停止测试环境
    $0 prod restart                # 重启生产环境
    $0 dev logs api                # 查看开发环境API服务日志
    $0 prod scale --scale api=3    # 将生产环境API服务扩展到3个副本
    $0 dev elk start               # 启动开发环境ELK服务

EOF
}

# 默认参数
ENVIRONMENT=""
ACTION=""
FORCE=false
DETACH=false
BUILD=false
PULL=false
REMOVE_ORPHANS=false
SCALE_ARGS=""
SERVICE=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -d|--detach)
            DETACH=true
            shift
            ;;
        --build)
            BUILD=true
            shift
            ;;
        --pull)
            PULL=true
            shift
            ;;
        --remove-orphans)
            REMOVE_ORPHANS=true
            shift
            ;;
        --scale)
            SCALE_ARGS="$2"
            shift 2
            ;;
        dev|test|prod)
            ENVIRONMENT="$1"
            shift
            ;;
        up|down|restart|status|logs|scale|elk)
            ACTION="$1"
            shift
            ;;
        *)
            # 可能是服务名称（用于logs命令）
            if [[ "$ACTION" == "logs" ]] && [[ -z "$SERVICE" ]]; then
                SERVICE="$1"
            else
                log_error "未知参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 检查必需参数
if [[ -z "$ENVIRONMENT" ]]; then
    log_error "请指定部署环境 (dev|test|prod)"
    show_help
    exit 1
fi

if [[ -z "$ACTION" ]]; then
    log_error "请指定操作 (up|down|restart|status|logs|scale)"
    show_help
    exit 1
fi

# 检查Docker和Docker Compose是否可用
if ! command -v docker &> /dev/null; then
    log_error "Docker未安装或不可用"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    log_error "Docker Compose未安装或不可用"
    exit 1
fi

# 确定使用的Docker Compose命令
if docker compose version &> /dev/null; then
    COMPOSE_CMD="docker compose"
else
    COMPOSE_CMD="docker-compose"
fi

# 确定compose文件
case $ENVIRONMENT in
    dev)
        COMPOSE_FILE="$DOCKER_DIR/compose/docker-compose.dev.yml"
        ;;
    test)
        COMPOSE_FILE="$DOCKER_DIR/compose/docker-compose.test.yml"
        ;;
    prod)
        COMPOSE_FILE="$DOCKER_DIR/compose/docker-compose.prod.yml"
        ;;
    *)
        log_error "不支持的环境: $ENVIRONMENT"
        exit 1
        ;;
esac

# 检查compose文件是否存在
if [[ ! -f "$COMPOSE_FILE" ]]; then
    log_error "Compose文件不存在: $COMPOSE_FILE"
    exit 1
fi

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 构建基础命令
BASE_CMD="$COMPOSE_CMD -f $COMPOSE_FILE"

# 添加环境变量文件
ENV_FILE="$DOCKER_DIR/configs/env/.env.$ENVIRONMENT"
if [[ -f "$ENV_FILE" ]]; then
    BASE_CMD="$BASE_CMD --env-file $ENV_FILE"
fi

# 执行操作
case $ACTION in
    up)
        log_info "启动 $ENVIRONMENT 环境服务..."
        
        CMD="$BASE_CMD up"
        
        if [[ "$DETACH" == true ]]; then
            CMD="$CMD -d"
        fi
        
        if [[ "$BUILD" == true ]]; then
            CMD="$CMD --build"
        fi
        
        if [[ "$PULL" == true ]]; then
            CMD="$CMD --pull always"
        fi
        
        if [[ "$REMOVE_ORPHANS" == true ]]; then
            CMD="$CMD --remove-orphans"
        fi
        
        log_info "执行命令: $CMD"
        eval $CMD
        
        if [[ $? -eq 0 ]]; then
            log_success "$ENVIRONMENT 环境服务启动成功"
            
            # 显示服务状态
            log_info "服务状态:"
            eval "$BASE_CMD ps"
        else
            log_error "$ENVIRONMENT 环境服务启动失败"
            exit 1
        fi
        ;;
        
    down)
        log_info "停止 $ENVIRONMENT 环境服务..."
        
        CMD="$BASE_CMD down"
        
        if [[ "$FORCE" == true ]]; then
            CMD="$CMD --volumes --remove-orphans"
        fi
        
        log_info "执行命令: $CMD"
        eval $CMD
        
        if [[ $? -eq 0 ]]; then
            log_success "$ENVIRONMENT 环境服务停止成功"
        else
            log_error "$ENVIRONMENT 环境服务停止失败"
            exit 1
        fi
        ;;
        
    restart)
        log_info "重启 $ENVIRONMENT 环境服务..."
        
        # 先停止
        eval "$BASE_CMD down"
        
        # 再启动
        CMD="$BASE_CMD up -d"
        
        if [[ "$BUILD" == true ]]; then
            CMD="$CMD --build"
        fi
        
        log_info "执行命令: $CMD"
        eval $CMD
        
        if [[ $? -eq 0 ]]; then
            log_success "$ENVIRONMENT 环境服务重启成功"
            
            # 显示服务状态
            log_info "服务状态:"
            eval "$BASE_CMD ps"
        else
            log_error "$ENVIRONMENT 环境服务重启失败"
            exit 1
        fi
        ;;
        
    status)
        log_info "$ENVIRONMENT 环境服务状态:"
        eval "$BASE_CMD ps"
        
        log_info "资源使用情况:"
        eval "$BASE_CMD top"
        ;;
        
    logs)
        if [[ -n "$SERVICE" ]]; then
            log_info "查看 $ENVIRONMENT 环境 $SERVICE 服务日志:"
            eval "$BASE_CMD logs -f $SERVICE"
        else
            log_info "查看 $ENVIRONMENT 环境所有服务日志:"
            eval "$BASE_CMD logs -f"
        fi
        ;;
        
    scale)
        if [[ -z "$SCALE_ARGS" ]]; then
            log_error "请使用 --scale SERVICE=NUM 指定扩缩容参数"
            exit 1
        fi
        
        log_info "扩缩容 $ENVIRONMENT 环境服务: $SCALE_ARGS"
        eval "$BASE_CMD up -d --scale $SCALE_ARGS"
        
        if [[ $? -eq 0 ]]; then
            log_success "服务扩缩容成功"
            
            # 显示服务状态
            log_info "服务状态:"
            eval "$BASE_CMD ps"
        else
            log_error "服务扩缩容失败"
            exit 1
        fi
        ;;

    elk)
        # ELK日志系统管理
        log_info "管理ELK日志系统..."

        # 调用ELK管理脚本
        ELK_SCRIPT="$SCRIPT_DIR/elk-manage.sh"
        if [[ -f "$ELK_SCRIPT" ]]; then
            # 传递剩余的参数给ELK管理脚本
            "$ELK_SCRIPT" "$@"
        else
            log_error "ELK管理脚本不存在: $ELK_SCRIPT"
            exit 1
        fi
        ;;

    *)
        log_error "不支持的操作: $ACTION"
        show_help
        exit 1
        ;;
esac
