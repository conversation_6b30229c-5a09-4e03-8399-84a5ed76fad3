#!/bin/bash

# Kibana自动配置脚本
# 在Kibana启动后自动创建索引模式、仪表盘和示例数据

set -e

KIBANA_URL="http://localhost:5601"
ELASTICSEARCH_URL="http://localhost:9200"

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[AUTO-SETUP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 等待服务启动
wait_for_services() {
    log_info "等待Elasticsearch和Kibana服务启动..."
    
    # 等待Elasticsearch
    local es_attempts=0
    while [ $es_attempts -lt 30 ]; do
        if curl -s "$ELASTICSEARCH_URL/_cluster/health" > /dev/null 2>&1; then
            log_success "Elasticsearch已就绪"
            break
        fi
        sleep 5
        ((es_attempts++))
    done
    
    # 等待Kibana
    local kibana_attempts=0
    while [ $kibana_attempts -lt 60 ]; do
        if curl -s "$KIBANA_URL/api/status" > /dev/null 2>&1; then
            log_success "Kibana已就绪"
            break
        fi
        sleep 5
        ((kibana_attempts++))
    done
}

# 创建示例数据
create_sample_data() {
    log_info "创建示例审计日志数据..."
    
    local current_date=$(date +%Y.%m.%d)
    local base_time=$(date -u +"%Y-%m-%dT%H:%M:%S")
    
    # 创建多样化的示例数据
    local endpoints=("/api/v1/audit" "/api/v1/users" "/api/v1/reports" "/api/v1/settings")
    local methods=("GET" "POST" "PUT" "DELETE")
    local levels=("INFO" "WARN" "ERROR" "DEBUG")
    local users=("admin" "user1" "user2" "user3" "analyst" "auditor")
    local event_types=("CREATE" "UPDATE" "DELETE" "VIEW" "EXPORT")
    local compliance_status=("COMPLIANT" "NON_COMPLIANT" "PENDING")
    
    for i in {1..50}; do
        local endpoint=${endpoints[$((RANDOM % ${#endpoints[@]}))]}
        local method=${methods[$((RANDOM % ${#methods[@]}))]}
        local level=${levels[$((RANDOM % ${#levels[@]}))]}
        local user=${users[$((RANDOM % ${#users[@]}))]}
        local event_type=${event_types[$((RANDOM % ${#event_types[@]}))]}
        local compliance=${compliance_status[$((RANDOM % ${#compliance_status[@]}))]}
        local response_time=$((RANDOM % 2000 + 50))
        local status_code=$((RANDOM % 100 < 90 ? 200 : (RANDOM % 100 < 50 ? 400 : 500)))
        
        # 随机时间偏移（过去24小时内）
        local time_offset=$((RANDOM % 86400))
        local timestamp=$(date -u -d "$base_time $time_offset seconds ago" +"%Y-%m-%dT%H:%M:%S.%3NZ")
        
        curl -s -X POST "$ELASTICSEARCH_URL/audit-logs-$current_date/_doc" \
            -H "Content-Type: application/json" \
            -d "{
                \"@timestamp\": \"$timestamp\",
                \"level\": \"$level\",
                \"service\": \"audit_api\",
                \"message\": \"$method $endpoint - Status: $status_code\",
                \"user_id\": \"$user\",
                \"endpoint\": \"$endpoint\",
                \"method\": \"$method\",
                \"response_time\": $response_time,
                \"status_code\": $status_code,
                \"audit_event_type\": \"$event_type\",
                \"compliance_status\": \"$compliance\",
                \"ip_address\": \"192.168.1.$((RANDOM % 254 + 1))\",
                \"user_agent\": \"Mozilla/5.0 (compatible; AuditClient/1.0)\"
            }" > /dev/null
    done
    
    log_success "已创建50条示例数据"
}

# 创建索引模式
create_index_patterns() {
    log_info "创建索引模式..."
    
    # 等待索引数据可用
    sleep 5
    
    # 创建audit-logs索引模式
    curl -s -X POST "$KIBANA_URL/api/saved_objects/index-pattern/audit-logs-pattern" \
        -H "Content-Type: application/json" \
        -H "kbn-xsrf: true" \
        -d '{
            "attributes": {
                "title": "audit-logs-*",
                "timeFieldName": "@timestamp"
            }
        }' > /dev/null
    
    log_success "索引模式创建完成"
}

# 创建可视化组件
create_visualizations() {
    log_info "创建预配置的可视化组件..."
    
    # API请求趋势图
    curl -s -X POST "$KIBANA_URL/api/saved_objects/visualization/api-requests-timeline" \
        -H "Content-Type: application/json" \
        -H "kbn-xsrf: true" \
        -d '{
            "attributes": {
                "title": "API请求时间线",
                "visState": "{\"title\":\"API请求时间线\",\"type\":\"line\",\"params\":{\"grid\":{\"categoryLines\":false,\"style\":{\"color\":\"#eee\"}},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"bottom\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"truncate\":100},\"title\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"left\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":100},\"title\":{\"text\":\"Count\"}}],\"seriesParams\":[{\"show\":true,\"type\":\"line\",\"mode\":\"normal\",\"data\":{\"label\":\"Count\",\"id\":\"1\"},\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"showCircles\":true}],\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"date_histogram\",\"schema\":\"segment\",\"params\":{\"field\":\"@timestamp\",\"interval\":\"auto\",\"customInterval\":\"2h\",\"min_doc_count\":1,\"extended_bounds\":{}}}]}",
                "uiStateJSON": "{}",
                "kibanaSavedObjectMeta": {
                    "searchSourceJSON": "{\"index\":\"audit-logs-pattern\",\"query\":{\"match_all\":{}},\"filter\":[]}"
                }
            }
        }' > /dev/null
    
    # 日志级别饼图
    curl -s -X POST "$KIBANA_URL/api/saved_objects/visualization/log-levels-pie" \
        -H "Content-Type: application/json" \
        -H "kbn-xsrf: true" \
        -d '{
            "attributes": {
                "title": "日志级别分布",
                "visState": "{\"title\":\"日志级别分布\",\"type\":\"pie\",\"params\":{\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"isDonut\":true},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"segment\",\"params\":{\"field\":\"level.keyword\",\"size\":5,\"order\":\"desc\",\"orderBy\":\"1\"}}]}",
                "uiStateJSON": "{}",
                "kibanaSavedObjectMeta": {
                    "searchSourceJSON": "{\"index\":\"audit-logs-pattern\",\"query\":{\"match_all\":{}},\"filter\":[]}"
                }
            }
        }' > /dev/null
    
    # 响应时间直方图
    curl -s -X POST "$KIBANA_URL/api/saved_objects/visualization/response-time-histogram" \
        -H "Content-Type: application/json" \
        -H "kbn-xsrf: true" \
        -d '{
            "attributes": {
                "title": "响应时间分布",
                "visState": "{\"title\":\"响应时间分布\",\"type\":\"histogram\",\"params\":{\"grid\":{\"categoryLines\":false,\"style\":{\"color\":\"#eee\"}},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"bottom\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"truncate\":100},\"title\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"left\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":100},\"title\":{\"text\":\"Count\"}}],\"seriesParams\":[{\"show\":true,\"type\":\"histogram\",\"mode\":\"stacked\",\"data\":{\"label\":\"Count\",\"id\":\"1\"},\"valueAxis\":\"ValueAxis-1\"}],\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"histogram\",\"schema\":\"segment\",\"params\":{\"field\":\"response_time\",\"interval\":100,\"extended_bounds\":{}}}]}",
                "uiStateJSON": "{}",
                "kibanaSavedObjectMeta": {
                    "searchSourceJSON": "{\"index\":\"audit-logs-pattern\",\"query\":{\"range\":{\"response_time\":{\"gte\":0}}},\"filter\":[]}"
                }
            }
        }' > /dev/null
    
    # 用户活动表格
    curl -s -X POST "$KIBANA_URL/api/saved_objects/visualization/user-activity-table" \
        -H "Content-Type: application/json" \
        -H "kbn-xsrf: true" \
        -d '{
            "attributes": {
                "title": "用户活动统计",
                "visState": "{\"title\":\"用户活动统计\",\"type\":\"table\",\"params\":{\"perPage\":10,\"showPartialRows\":false,\"showMeticsAtAllLevels\":false,\"sort\":{\"columnIndex\":null,\"direction\":null},\"showTotal\":false,\"totalFunc\":\"sum\"},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"bucket\",\"params\":{\"field\":\"user_id.keyword\",\"size\":10,\"order\":\"desc\",\"orderBy\":\"1\"}},{\"id\":\"3\",\"enabled\":true,\"type\":\"avg\",\"schema\":\"metric\",\"params\":{\"field\":\"response_time\"}}]}",
                "uiStateJSON": "{}",
                "kibanaSavedObjectMeta": {
                    "searchSourceJSON": "{\"index\":\"audit-logs-pattern\",\"query\":{\"match_all\":{}},\"filter\":[]}"
                }
            }
        }' > /dev/null
    
    log_success "可视化组件创建完成"
}

# 创建仪表盘
create_dashboard() {
    log_info "创建预配置仪表盘..."
    
    curl -s -X POST "$KIBANA_URL/api/saved_objects/dashboard/audit-service-dashboard" \
        -H "Content-Type: application/json" \
        -H "kbn-xsrf: true" \
        -d '{
            "attributes": {
                "title": "审计服务监控仪表盘",
                "hits": 0,
                "description": "审计服务的综合监控仪表盘，包含API性能、日志分析和用户活动统计",
                "panelsJSON": "[{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},\"panelIndex\":\"1\",\"embeddableConfig\":{},\"panelRefName\":\"panel_1\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"2\"},\"panelIndex\":\"2\",\"embeddableConfig\":{},\"panelRefName\":\"panel_2\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":15,\"w\":24,\"h\":15,\"i\":\"3\"},\"panelIndex\":\"3\",\"embeddableConfig\":{},\"panelRefName\":\"panel_3\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":24,\"y\":15,\"w\":24,\"h\":15,\"i\":\"4\"},\"panelIndex\":\"4\",\"embeddableConfig\":{},\"panelRefName\":\"panel_4\"}]",
                "timeRestore": false,
                "timeTo": "now",
                "timeFrom": "now-24h",
                "refreshInterval": {
                    "pause": false,
                    "value": 30000
                },
                "kibanaSavedObjectMeta": {
                    "searchSourceJSON": "{\"query\":{\"match_all\":{}},\"filter\":[]}"
                }
            },
            "references": [
                {"name": "panel_1", "type": "visualization", "id": "api-requests-timeline"},
                {"name": "panel_2", "type": "visualization", "id": "log-levels-pie"},
                {"name": "panel_3", "type": "visualization", "id": "response-time-histogram"},
                {"name": "panel_4", "type": "visualization", "id": "user-activity-table"}
            ]
        }' > /dev/null
    
    log_success "仪表盘创建完成"
}

# 设置默认索引模式
set_default_index() {
    log_info "设置默认索引模式..."
    
    curl -s -X POST "$KIBANA_URL/api/kibana/settings" \
        -H "Content-Type: application/json" \
        -H "kbn-xsrf: true" \
        -d '{
            "changes": {
                "defaultIndex": "audit-logs-pattern"
            }
        }' > /dev/null 2>&1
    
    log_success "默认设置完成"
}

# 主函数
main() {
    log_info "开始自动配置Kibana..."
    
    wait_for_services
    create_sample_data
    create_index_patterns
    create_visualizations
    create_dashboard
    set_default_index
    
    log_success "Kibana自动配置完成！"
    log_info "访问地址: http://localhost:5601/app/dashboards#/view/audit-service-dashboard"
}

# 后台运行
main > /tmp/kibana-auto-setup.log 2>&1 &
