#!/bin/bash
# Docker镜像构建脚本

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DOCKER_DIR="$PROJECT_ROOT/docker"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker镜像构建脚本

用法: $0 [选项] [环境]

环境:
    dev         构建开发环境镜像
    prod        构建生产环境镜像
    celery      构建Celery工作进程镜像
    all         构建所有镜像

选项:
    -h, --help      显示此帮助信息
    -t, --tag       指定镜像标签 (默认: latest)
    -r, --registry  指定镜像仓库前缀
    --no-cache      不使用构建缓存
    --push          构建后推送到仓库
    --parallel      并行构建多个镜像

示例:
    $0 dev                          # 构建开发环境镜像
    $0 prod -t v1.0.0              # 构建生产环境镜像并标记为v1.0.0
    $0 all -r myregistry.com/      # 构建所有镜像并添加仓库前缀
    $0 prod --no-cache --push      # 不使用缓存构建生产镜像并推送

EOF
}

# 默认参数
ENVIRONMENT=""
TAG="latest"
REGISTRY=""
NO_CACHE=""
PUSH=false
PARALLEL=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        --no-cache)
            NO_CACHE="--no-cache"
            shift
            ;;
        --push)
            PUSH=true
            shift
            ;;
        --parallel)
            PARALLEL=true
            shift
            ;;
        dev|prod|celery|all)
            ENVIRONMENT="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查环境参数
if [[ -z "$ENVIRONMENT" ]]; then
    log_error "请指定构建环境 (dev|prod|celery|all)"
    show_help
    exit 1
fi

# 检查Docker是否可用
if ! command -v docker &> /dev/null; then
    log_error "Docker未安装或不可用"
    exit 1
fi

# 构建单个镜像的函数
build_image() {
    local env=$1
    local dockerfile=$2
    local image_name=$3
    
    log_info "开始构建 $env 环境镜像..."
    
    # 构建镜像名称
    local full_image_name="${REGISTRY}${image_name}:${TAG}"
    
    # 构建命令
    local build_cmd="docker build $NO_CACHE -f $dockerfile -t $full_image_name $PROJECT_ROOT"
    
    log_info "执行构建命令: $build_cmd"
    
    if eval $build_cmd; then
        log_success "$env 环境镜像构建成功: $full_image_name"
        
        # 推送镜像
        if [[ "$PUSH" == true ]]; then
            log_info "推送镜像到仓库..."
            if docker push "$full_image_name"; then
                log_success "镜像推送成功: $full_image_name"
            else
                log_error "镜像推送失败: $full_image_name"
                return 1
            fi
        fi
        
        return 0
    else
        log_error "$env 环境镜像构建失败"
        return 1
    fi
}

# 并行构建函数
build_parallel() {
    local pids=()
    
    if [[ "$ENVIRONMENT" == "all" ]] || [[ "$ENVIRONMENT" == "dev" ]]; then
        build_image "dev" "$DOCKER_DIR/dockerfiles/Dockerfile.dev" "audit-service-dev" &
        pids+=($!)
    fi
    
    if [[ "$ENVIRONMENT" == "all" ]] || [[ "$ENVIRONMENT" == "prod" ]]; then
        build_image "prod" "$DOCKER_DIR/dockerfiles/Dockerfile.prod" "audit-service" &
        pids+=($!)
    fi
    
    if [[ "$ENVIRONMENT" == "all" ]] || [[ "$ENVIRONMENT" == "celery" ]]; then
        build_image "celery" "$DOCKER_DIR/dockerfiles/Dockerfile.celery" "audit-celery" &
        pids+=($!)
    fi
    
    # 等待所有构建完成
    local failed=0
    for pid in "${pids[@]}"; do
        if ! wait $pid; then
            failed=1
        fi
    done
    
    return $failed
}

# 串行构建函数
build_sequential() {
    local failed=0
    
    if [[ "$ENVIRONMENT" == "all" ]] || [[ "$ENVIRONMENT" == "dev" ]]; then
        if ! build_image "dev" "$DOCKER_DIR/dockerfiles/Dockerfile.dev" "audit-service-dev"; then
            failed=1
        fi
    fi
    
    if [[ "$ENVIRONMENT" == "all" ]] || [[ "$ENVIRONMENT" == "prod" ]]; then
        if ! build_image "prod" "$DOCKER_DIR/dockerfiles/Dockerfile.prod" "audit-service"; then
            failed=1
        fi
    fi
    
    if [[ "$ENVIRONMENT" == "all" ]] || [[ "$ENVIRONMENT" == "celery" ]]; then
        if ! build_image "celery" "$DOCKER_DIR/dockerfiles/Dockerfile.celery" "audit-celery"; then
            failed=1
        fi
    fi
    
    return $failed
}

# 主构建逻辑
log_info "开始构建Docker镜像..."
log_info "环境: $ENVIRONMENT"
log_info "标签: $TAG"
log_info "仓库: ${REGISTRY:-"本地"}"
log_info "并行构建: $PARALLEL"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 执行构建
if [[ "$PARALLEL" == true ]] && [[ "$ENVIRONMENT" == "all" ]]; then
    if build_parallel; then
        log_success "所有镜像构建完成"
    else
        log_error "部分镜像构建失败"
        exit 1
    fi
else
    if build_sequential; then
        log_success "镜像构建完成"
    else
        log_error "镜像构建失败"
        exit 1
    fi
fi

# 显示构建的镜像
log_info "构建的镜像列表:"
docker images | grep -E "(audit-service|audit-celery)" | head -10
