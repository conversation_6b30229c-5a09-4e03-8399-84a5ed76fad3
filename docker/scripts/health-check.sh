#!/bin/bash
# 健康检查脚本

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
健康检查脚本

用法: $0 [选项] [环境]

环境:
    dev         开发环境
    test        测试环境
    prod        生产环境

选项:
    -h, --help      显示此帮助信息
    -v, --verbose   详细输出
    -w, --wait      等待服务就绪的最大时间（秒，默认300）
    --api-only      仅检查API服务
    --db-only       仅检查数据库服务
    --redis-only    仅检查Redis服务

示例:
    $0 dev                  # 检查开发环境所有服务
    $0 prod --verbose       # 详细检查生产环境
    $0 test --api-only      # 仅检查测试环境API服务

EOF
}

# 默认参数
ENVIRONMENT=""
VERBOSE=false
WAIT_TIME=300
CHECK_API=true
CHECK_DB=true
CHECK_REDIS=true
CHECK_CELERY=true

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -w|--wait)
            WAIT_TIME="$2"
            shift 2
            ;;
        --api-only)
            CHECK_API=true
            CHECK_DB=false
            CHECK_REDIS=false
            CHECK_CELERY=false
            shift
            ;;
        --db-only)
            CHECK_API=false
            CHECK_DB=true
            CHECK_REDIS=false
            CHECK_CELERY=false
            shift
            ;;
        --redis-only)
            CHECK_API=false
            CHECK_DB=false
            CHECK_REDIS=true
            CHECK_CELERY=false
            shift
            ;;
        dev|test|prod)
            ENVIRONMENT="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查环境参数
if [[ -z "$ENVIRONMENT" ]]; then
    log_error "请指定检查环境 (dev|test|prod)"
    show_help
    exit 1
fi

# 确定端口配置
case $ENVIRONMENT in
    dev)
        API_PORT=8000
        MYSQL_PORT=3306
        REDIS_PORT=6379
        ;;
    test)
        API_PORT=8002
        MYSQL_PORT=3307
        REDIS_PORT=6380
        ;;
    prod)
        API_PORT=80
        MYSQL_PORT=3306
        REDIS_PORT=6379
        ;;
    *)
        log_error "不支持的环境: $ENVIRONMENT"
        exit 1
        ;;
esac

# 检查端口是否可用的函数
check_port() {
    local host=$1
    local port=$2
    local service_name=$3
    local timeout=${4:-10}
    
    if [[ "$VERBOSE" == true ]]; then
        log_info "检查 $service_name 服务 ($host:$port)..."
    fi
    
    if timeout $timeout bash -c "echo >/dev/tcp/$host/$port" 2>/dev/null; then
        if [[ "$VERBOSE" == true ]]; then
            log_success "$service_name 端口 $port 可访问"
        fi
        return 0
    else
        log_error "$service_name 端口 $port 不可访问"
        return 1
    fi
}

# 检查HTTP服务的函数
check_http() {
    local url=$1
    local service_name=$2
    local timeout=${3:-10}
    
    if [[ "$VERBOSE" == true ]]; then
        log_info "检查 $service_name HTTP服务 ($url)..."
    fi
    
    if curl -f -s --max-time $timeout "$url" > /dev/null; then
        if [[ "$VERBOSE" == true ]]; then
            log_success "$service_name HTTP服务正常"
        fi
        return 0
    else
        log_error "$service_name HTTP服务异常"
        return 1
    fi
}

# 检查MySQL数据库的函数
check_mysql() {
    local host=$1
    local port=$2
    local timeout=${3:-10}
    
    if [[ "$VERBOSE" == true ]]; then
        log_info "检查MySQL数据库连接..."
    fi
    
    # 使用Docker容器内的MySQL客户端检查
    local container_name=""
    case $ENVIRONMENT in
        dev)
            container_name="audit_mysql_dev"
            ;;
        test)
            container_name="audit_mysql_test"
            ;;
        prod)
            container_name="audit_mysql_prod"
            ;;
    esac
    
    if docker exec "$container_name" mysqladmin ping -h localhost --silent 2>/dev/null; then
        if [[ "$VERBOSE" == true ]]; then
            log_success "MySQL数据库连接正常"
        fi
        return 0
    else
        log_error "MySQL数据库连接异常"
        return 1
    fi
}

# 检查Redis的函数
check_redis() {
    local host=$1
    local port=$2
    local timeout=${3:-10}
    
    if [[ "$VERBOSE" == true ]]; then
        log_info "检查Redis连接..."
    fi
    
    # 使用Docker容器内的Redis客户端检查
    local container_name=""
    case $ENVIRONMENT in
        dev)
            container_name="audit_redis_dev"
            ;;
        test)
            container_name="audit_redis_test"
            ;;
        prod)
            container_name="audit_redis_prod"
            ;;
    esac
    
    if docker exec "$container_name" redis-cli ping 2>/dev/null | grep -q "PONG"; then
        if [[ "$VERBOSE" == true ]]; then
            log_success "Redis连接正常"
        fi
        return 0
    else
        log_error "Redis连接异常"
        return 1
    fi
}

# 检查Celery Worker的函数
check_celery() {
    if [[ "$VERBOSE" == true ]]; then
        log_info "检查Celery Worker状态..."
    fi
    
    local container_name=""
    case $ENVIRONMENT in
        dev)
            container_name="audit_celery_worker_dev"
            ;;
        test)
            container_name="audit_celery_worker_test"
            ;;
        prod)
            container_name="audit_celery_worker_prod"
            ;;
    esac
    
    if docker exec "$container_name" celery -A app.tasks.celery_tasks inspect ping 2>/dev/null | grep -q "pong"; then
        if [[ "$VERBOSE" == true ]]; then
            log_success "Celery Worker正常"
        fi
        return 0
    else
        log_error "Celery Worker异常"
        return 1
    fi
}

# 等待服务就绪的函数
wait_for_service() {
    local check_func=$1
    local service_name=$2
    local max_wait=$3
    shift 3
    
    log_info "等待 $service_name 服务就绪（最大等待时间: ${max_wait}秒）..."
    
    local count=0
    while [[ $count -lt $max_wait ]]; do
        if $check_func "$@"; then
            log_success "$service_name 服务已就绪"
            return 0
        fi
        
        sleep 5
        count=$((count + 5))
        
        if [[ $((count % 30)) -eq 0 ]]; then
            log_info "仍在等待 $service_name 服务就绪... (${count}/${max_wait}秒)"
        fi
    done
    
    log_error "$service_name 服务在 $max_wait 秒内未就绪"
    return 1
}

# 主检查逻辑
log_info "开始健康检查 - $ENVIRONMENT 环境"

FAILED_CHECKS=0

# 检查API服务
if [[ "$CHECK_API" == true ]]; then
    if ! wait_for_service check_http "API" $WAIT_TIME "http://localhost:$API_PORT/api/v1/health"; then
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
fi

# 检查数据库
if [[ "$CHECK_DB" == true ]]; then
    if ! wait_for_service check_mysql "MySQL" $WAIT_TIME "localhost" "$MYSQL_PORT"; then
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
fi

# 检查Redis
if [[ "$CHECK_REDIS" == true ]]; then
    if ! wait_for_service check_redis "Redis" $WAIT_TIME "localhost" "$REDIS_PORT"; then
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
fi

# 检查Celery
if [[ "$CHECK_CELERY" == true ]]; then
    if ! wait_for_service check_celery "Celery" $WAIT_TIME; then
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
fi

# 输出检查结果
echo
if [[ $FAILED_CHECKS -eq 0 ]]; then
    log_success "所有服务健康检查通过！"
    
    if [[ "$VERBOSE" == true ]]; then
        log_info "服务详细状态:"
        
        if [[ "$CHECK_API" == true ]]; then
            echo "  API服务: http://localhost:$API_PORT"
            curl -s "http://localhost:$API_PORT/api/v1/health" | jq . 2>/dev/null || echo "  API响应正常"
        fi
        
        if [[ "$CHECK_DB" == true ]]; then
            echo "  MySQL: localhost:$MYSQL_PORT"
        fi
        
        if [[ "$CHECK_REDIS" == true ]]; then
            echo "  Redis: localhost:$REDIS_PORT"
        fi
    fi
    
    exit 0
else
    log_error "健康检查失败！失败的服务数量: $FAILED_CHECKS"
    exit 1
fi
