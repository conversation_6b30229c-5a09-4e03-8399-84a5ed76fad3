#!/bin/bash
# ELK日志系统管理脚本

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DOCKER_DIR="$PROJECT_ROOT/docker"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
ELK日志系统管理脚本

用法: $0 [命令] [选项]

命令:
    start           启动ELK服务
    stop            停止ELK服务
    restart         重启ELK服务
    status          查看ELK服务状态
    logs            查看ELK服务日志
    setup           初始化ELK配置
    clean           清理ELK数据
    health          健康检查

选项:
    -h, --help      显示此帮助信息
    -f, --force     强制执行
    -v, --verbose   详细输出

示例:
    $0 start                # 启动ELK服务
    $0 stop                 # 停止ELK服务
    $0 logs elasticsearch   # 查看Elasticsearch日志
    $0 setup                # 初始化ELK配置
    $0 clean --force        # 强制清理ELK数据

EOF
}

# 默认参数
COMMAND=""
SERVICE=""
FORCE=false
VERBOSE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        start|stop|restart|status|logs|setup|clean|health)
            COMMAND="$1"
            shift
            ;;
        elasticsearch|logstash|kibana|filebeat|metricbeat)
            SERVICE="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查命令参数
if [[ -z "$COMMAND" ]]; then
    log_error "请指定命令"
    show_help
    exit 1
fi

# 确定使用的Docker Compose命令
if docker compose version &> /dev/null; then
    COMPOSE_CMD="docker compose"
else
    COMPOSE_CMD="docker-compose"
fi

# ELK Compose文件路径
ELK_COMPOSE_FILE="$DOCKER_DIR/compose/docker-compose.elk.yml"

# 检查compose文件是否存在
if [[ ! -f "$ELK_COMPOSE_FILE" ]]; then
    log_error "ELK Compose文件不存在: $ELK_COMPOSE_FILE"
    exit 1
fi

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 构建基础命令
BASE_CMD="$COMPOSE_CMD -f $ELK_COMPOSE_FILE"

# 启动ELK服务
start_elk() {
    log_info "启动ELK日志系统..."
    
    # 确保网络存在
    if ! docker network ls | grep -q audit_network; then
        log_info "创建Docker网络..."
        docker network create audit_network
    fi
    
    # 设置系统参数（Elasticsearch需要）
    log_info "设置系统参数..."
    if [[ "$(uname)" == "Linux" ]]; then
        sudo sysctl -w vm.max_map_count=262144 || log_warning "无法设置vm.max_map_count，可能需要手动设置"
    fi
    
    # 启动服务
    eval "$BASE_CMD up -d"
    
    if [[ $? -eq 0 ]]; then
        log_success "ELK服务启动成功"
        
        # 等待服务就绪
        log_info "等待服务就绪..."
        sleep 30
        
        # 显示服务状态
        eval "$BASE_CMD ps"
        
        # 启动自动配置
        log_info "启动Kibana自动配置..."
        chmod +x "$SCRIPT_DIR/auto-setup-kibana.sh"
        "$SCRIPT_DIR/auto-setup-kibana.sh" &

        # 显示访问地址
        cat << EOF

🎉 ELK服务启动完成！

📋 服务地址:
   📊 Kibana: http://localhost:5601
   🔍 Elasticsearch: http://localhost:9200
   📝 Logstash: http://localhost:9600

🚀 自动配置进行中...
   ⏳ 正在创建索引模式、可视化组件和仪表盘
   📊 完成后可直接访问: http://localhost:5601/app/dashboards
   📝 配置日志: /tmp/kibana-auto-setup.log

EOF
    else
        log_error "ELK服务启动失败"
        exit 1
    fi
}

# 停止ELK服务
stop_elk() {
    log_info "停止ELK日志系统..."
    
    if [[ "$FORCE" == true ]]; then
        eval "$BASE_CMD down --volumes"
    else
        eval "$BASE_CMD down"
    fi
    
    if [[ $? -eq 0 ]]; then
        log_success "ELK服务停止成功"
    else
        log_error "ELK服务停止失败"
        exit 1
    fi
}

# 重启ELK服务
restart_elk() {
    log_info "重启ELK日志系统..."
    
    stop_elk
    sleep 5
    start_elk
}

# 查看ELK服务状态
status_elk() {
    log_info "ELK服务状态:"
    eval "$BASE_CMD ps"
    
    log_info "服务健康状态:"
    
    # 检查Elasticsearch
    if curl -f -s http://localhost:9200/_cluster/health > /dev/null; then
        log_success "Elasticsearch: 健康"
    else
        log_error "Elasticsearch: 异常"
    fi
    
    # 检查Logstash
    if curl -f -s http://localhost:9600 > /dev/null; then
        log_success "Logstash: 健康"
    else
        log_error "Logstash: 异常"
    fi
    
    # 检查Kibana
    if curl -f -s http://localhost:5601/api/status > /dev/null; then
        log_success "Kibana: 健康"
    else
        log_error "Kibana: 异常"
    fi
}

# 查看ELK服务日志
logs_elk() {
    if [[ -n "$SERVICE" ]]; then
        log_info "查看 $SERVICE 服务日志:"
        eval "$BASE_CMD logs -f $SERVICE"
    else
        log_info "查看所有ELK服务日志:"
        eval "$BASE_CMD logs -f"
    fi
}

# 初始化ELK配置
setup_elk() {
    log_info "初始化ELK配置..."
    
    # 检查配置文件是否存在
    local config_dir="$DOCKER_DIR/configs/elk"
    
    if [[ ! -d "$config_dir" ]]; then
        log_error "ELK配置目录不存在: $config_dir"
        exit 1
    fi
    
    # 设置文件权限
    log_info "设置配置文件权限..."
    find "$config_dir" -type f -name "*.yml" -exec chmod 644 {} \;
    
    # 创建必要的目录
    mkdir -p logs
    
    log_success "ELK配置初始化完成"
}

# 清理ELK数据
clean_elk() {
    log_info "清理ELK数据..."
    
    if [[ "$FORCE" != true ]]; then
        log_warning "这将删除所有ELK数据，包括索引和日志"
        read -p "确认继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi
    
    # 停止服务
    eval "$BASE_CMD down --volumes"
    
    # 删除数据卷
    docker volume rm $(docker volume ls -q | grep -E "(elasticsearch|filebeat|metricbeat)") 2>/dev/null || true
    
    log_success "ELK数据清理完成"
}

# 健康检查
health_check() {
    log_info "执行ELK健康检查..."
    
    local failed=0
    
    # 检查容器状态
    local containers=$(eval "$BASE_CMD ps -q")
    if [[ -z "$containers" ]]; then
        log_error "没有运行中的ELK容器"
        exit 1
    fi
    
    # 检查Elasticsearch
    log_info "检查Elasticsearch..."
    if curl -f -s http://localhost:9200/_cluster/health | grep -q '"status":"green\|yellow"'; then
        log_success "Elasticsearch健康检查通过"
    else
        log_error "Elasticsearch健康检查失败"
        failed=$((failed + 1))
    fi
    
    # 检查Logstash
    log_info "检查Logstash..."
    if curl -f -s http://localhost:9600 > /dev/null; then
        log_success "Logstash健康检查通过"
    else
        log_error "Logstash健康检查失败"
        failed=$((failed + 1))
    fi
    
    # 检查Kibana
    log_info "检查Kibana..."
    if curl -f -s http://localhost:5601/api/status > /dev/null; then
        log_success "Kibana健康检查通过"
    else
        log_error "Kibana健康检查失败"
        failed=$((failed + 1))
    fi
    
    if [[ $failed -eq 0 ]]; then
        log_success "所有ELK服务健康检查通过"
    else
        log_error "$failed 个服务健康检查失败"
        exit 1
    fi
}

# 主执行逻辑
case $COMMAND in
    start)
        start_elk
        ;;
    stop)
        stop_elk
        ;;
    restart)
        restart_elk
        ;;
    status)
        status_elk
        ;;
    logs)
        logs_elk
        ;;
    setup)
        setup_elk
        ;;
    clean)
        clean_elk
        ;;
    health)
        health_check
        ;;
    *)
        log_error "不支持的命令: $COMMAND"
        show_help
        exit 1
        ;;
esac
