# Docker部署指南

本文档介绍如何使用Docker部署审计服务系统。

## 📁 目录结构

```
docker/
├── README.md                    # 本文档
├── quick-start.sh              # 快速启动脚本
├── dockerfiles/                # Dockerfile文件
│   ├── Dockerfile.dev          # 开发环境
│   ├── Dockerfile.prod         # 生产环境
│   └── Dockerfile.celery       # Celery工作进程
├── compose/                    # Docker Compose配置
│   ├── docker-compose.dev.yml  # 开发环境
│   ├── docker-compose.test.yml # 测试环境
│   ├── docker-compose.prod.yml # 生产环境
│   └── docker-compose.elk.yml  # ELK日志系统
├── scripts/                    # 管理脚本
│   ├── build.sh               # 镜像构建脚本
│   ├── deploy.sh              # 部署脚本
│   ├── health-check.sh        # 健康检查脚本
│   ├── manage.sh              # 环境管理脚本
│   └── elk-manage.sh          # ELK日志系统管理脚本
├── configs/                    # 配置文件
│   ├── env/                   # 环境变量配置
│   ├── mysql/                 # MySQL配置
│   ├── redis/                 # Redis配置
│   └── elk/                   # ELK日志系统配置
├── nginx/                      # Nginx配置
│   ├── nginx.conf             # 主配置
│   └── conf.d/                # 站点配置
└── secrets/                    # 密钥文件模板
    ├── mysql_root_password.txt.template
    ├── mysql_password.txt.template
    └── secret_key.txt.template
```

## 🚀 快速开始

### 1. 开发环境

```bash
# 快速启动开发环境
./docker/quick-start.sh dev

# 或者手动启动
./docker/scripts/manage.sh init -e dev
./docker/scripts/build.sh dev
./docker/scripts/deploy.sh dev up -d
```

访问地址：
- API服务: http://localhost:8000
- API文档: http://localhost:8000/docs
- Flower监控: http://localhost:5555

### 2. 测试环境

```bash
# 启动测试环境
./docker/quick-start.sh test

# 运行测试
./docker/scripts/deploy.sh test up test_runner
```

### 3. 生产环境

```bash
# 设置生产环境密钥
./docker/scripts/manage.sh setup-secrets

# 配置生产环境变量
cp docker/configs/env/.env.prod.template docker/configs/env/.env.prod
# 编辑 .env.prod 文件，填入实际配置

# 启动生产环境
./docker/quick-start.sh prod
```

## 🛠️ 详细使用说明

### 镜像构建

```bash
# 构建开发环境镜像
./docker/scripts/build.sh dev

# 构建生产环境镜像
./docker/scripts/build.sh prod

# 构建所有镜像
./docker/scripts/build.sh all

# 构建并推送到仓库
./docker/scripts/build.sh prod -t v1.0.0 -r myregistry.com/ --push
```

### 服务部署

```bash
# 启动服务
./docker/scripts/deploy.sh dev up -d

# 停止服务
./docker/scripts/deploy.sh dev down

# 重启服务
./docker/scripts/deploy.sh dev restart

# 查看服务状态
./docker/scripts/deploy.sh dev status

# 查看服务日志
./docker/scripts/deploy.sh dev logs

# 扩缩容服务
./docker/scripts/deploy.sh prod scale --scale api=3
```

### 健康检查

```bash
# 检查所有服务
./docker/scripts/health-check.sh dev

# 详细检查
./docker/scripts/health-check.sh prod --verbose

# 仅检查API服务
./docker/scripts/health-check.sh dev --api-only
```

### 环境管理

```bash
# 初始化环境
./docker/scripts/manage.sh init -e dev

# 清理Docker资源
./docker/scripts/manage.sh clean

# 备份数据
./docker/scripts/manage.sh backup -e prod

# 恢复数据
./docker/scripts/manage.sh restore /path/to/backup -e prod

# 更新服务
./docker/scripts/manage.sh update -e prod

# 监控服务
./docker/scripts/manage.sh monitor -e prod
```

## 🔧 配置说明

### 环境变量配置

每个环境都有对应的环境变量配置文件：

- `docker/configs/env/.env.dev` - 开发环境
- `docker/configs/env/.env.test` - 测试环境
- `docker/configs/env/.env.prod.template` - 生产环境模板

### 数据库配置

MySQL配置文件位于 `docker/configs/mysql/my.cnf`，包含：
- 性能优化参数
- 字符集配置
- 日志配置
- 安全设置

### Redis配置

Redis配置文件位于 `docker/configs/redis/redis.conf`，包含：
- 内存管理
- 持久化设置
- 网络配置
- 性能优化

### Nginx配置

Nginx配置文件位于 `docker/nginx/`，包含：
- 反向代理配置
- 负载均衡
- SSL/TLS配置
- 安全头设置

## 🔐 安全配置

### 生产环境密钥

```bash
# 生成密钥文件
./docker/scripts/manage.sh setup-secrets

# 手动设置密钥
echo "your-strong-password" > docker/secrets/mysql_root_password.txt
echo "your-user-password" > docker/secrets/mysql_password.txt
echo "your-secret-key" > docker/secrets/secret_key.txt

# 设置文件权限
chmod 600 docker/secrets/*.txt
```

### SSL证书配置

1. 将SSL证书文件放置到 `docker/nginx/ssl/` 目录
2. 修改 `docker/nginx/conf.d/audit-service.conf` 中的SSL配置
3. 重启Nginx服务

## 📊 监控和日志

### 服务监控

```bash
# 查看容器状态
docker ps

# 查看资源使用
docker stats

# 查看服务日志
docker-compose -f docker/compose/docker-compose.prod.yml logs -f api

# Prometheus监控
curl http://localhost:8001/metrics
```

### 日志管理

日志文件位置：
- 应用日志: `logs/audit-service.log`
- Nginx日志: `docker/nginx/logs/`
- MySQL日志: 容器内 `/var/log/mysql/`

### ELK日志系统

ELK（Elasticsearch, Logstash, Kibana）日志系统提供了强大的日志收集、分析和可视化功能。

#### 启动ELK服务

```bash
# 使用ELK管理脚本启动
./docker/scripts/elk-manage.sh start

# 或者使用部署脚本
./docker/scripts/deploy.sh dev elk start

# 与主服务一起启动
./start.sh docker --with-elk
```

#### ELK服务地址

- **Kibana**: http://localhost:5601 - 日志可视化和分析
- **Elasticsearch**: http://localhost:9200 - 搜索和存储引擎
- **Logstash**: http://localhost:9600 - 日志处理管道

#### ELK管理命令

```bash
# 查看ELK服务状态
./docker/scripts/elk-manage.sh status

# 查看ELK服务日志
./docker/scripts/elk-manage.sh logs

# 健康检查
./docker/scripts/elk-manage.sh health

# 停止ELK服务
./docker/scripts/elk-manage.sh stop

# 清理ELK数据
./docker/scripts/elk-manage.sh clean --force
```

#### ELK配置

ELK配置文件位于 `docker/configs/elk/` 目录：

- `elasticsearch/elasticsearch.yml` - Elasticsearch配置
- `logstash/logstash.yml` - Logstash配置
- `logstash/pipeline/` - Logstash管道配置
- `kibana/kibana.yml` - Kibana配置
- `filebeat/filebeat.yml` - Filebeat配置
- `metricbeat/metricbeat.yml` - Metricbeat配置

## 🔄 数据备份和恢复

### 自动备份

```bash
# 创建备份任务
./docker/scripts/manage.sh backup -e prod

# 定时备份（添加到crontab）
0 2 * * * /path/to/project/docker/scripts/manage.sh backup -e prod
```

### 数据恢复

```bash
# 恢复数据
./docker/scripts/manage.sh restore /path/to/backup -e prod
```

## 🚨 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看容器日志
   docker logs container_name
   
   # 检查配置文件
   docker-compose config
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker exec mysql_container mysqladmin ping
   
   # 查看数据库日志
   docker logs mysql_container
   ```

3. **Redis连接失败**
   ```bash
   # 检查Redis状态
   docker exec redis_container redis-cli ping
   ```

4. **端口冲突**
   ```bash
   # 查看端口占用
   netstat -tulpn | grep :8000
   
   # 修改docker-compose.yml中的端口映射
   ```

### 性能优化

1. **镜像优化**
   - 使用多阶段构建
   - 优化.dockerignore文件
   - 使用Alpine Linux基础镜像

2. **容器资源限制**
   ```yaml
   deploy:
     resources:
       limits:
         cpus: '2.0'
         memory: 2G
   ```

3. **数据库优化**
   - 调整innodb_buffer_pool_size
   - 优化查询索引
   - 定期清理日志

## 📝 开发指南

### 本地开发

```bash
# 启动开发环境
./docker/quick-start.sh dev

# 代码热重载（开发环境已配置）
# 修改代码后自动重启

# 进入容器调试
docker exec -it audit_api_dev bash
```

### 测试

```bash
# 运行单元测试
./docker/scripts/deploy.sh test up test_runner

# 运行特定测试
docker exec audit_test_runner pytest tests/test_api.py -v
```

### 部署流程

1. 开发环境测试
2. 构建镜像
3. 测试环境验证
4. 生产环境部署
5. 健康检查
6. 监控告警

## 📚 相关文档

- **[文档中心](../docs/README.md)** - 完整的文档索引
- **[Docker部署体系](../docs/deployment/DOCKER_DEPLOYMENT.md)** - Docker架构总览
- **[项目主文档](../README.md)** - 项目概述和快速开始
- **[测试指南](../tests/README.md)** - 测试使用文档

## 📞 支持

如有问题，请：
1. 查看日志文件
2. 运行健康检查脚本
3. 查看本文档的故障排除部分
4. 联系开发团队
