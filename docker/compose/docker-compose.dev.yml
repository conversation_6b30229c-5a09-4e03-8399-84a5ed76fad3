# 开发环境Docker Compose配置
version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: audit_mysql_dev
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: audit_db
      MYSQL_USER: audit_user
      MYSQL_PASSWORD: audit_password
    ports:
      - "3307:3306"  # 使用3307端口避免冲突
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ../mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=256M
      --max-connections=200
      --innodb-log-file-size=64M
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "audit_user", "-paudit_password"]
      timeout: 20s
      retries: 10
      interval: 30s
    networks:
      - audit_network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: audit_redis_dev
    ports:
      - "6380:6379"  # 使用6380端口避免冲突
    volumes:
      - redis_dev_data:/data
    command: >
      redis-server
      --appendonly yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 60 1000
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - audit_network
    restart: unless-stopped

  # FastAPI应用（开发模式）
  api:
    build:
      context: ../..
      dockerfile: docker/dockerfiles/Dockerfile.dev
    container_name: audit_api_dev
    ports:
      - "8000:8000"
      - "8001:8001"  # 监控端口
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - DATABASE_URL=mysql+pymysql://audit_user:audit_password@mysql:3306/audit_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - PYTHONPATH=/app
      - EMBEDDING_API_URL=http://*************:42050/v1/
      - EMBEDDING_API_KEY=EMPTY
      - EMBEDDING_MODEL=bge-m3
      - LLM_API_URL=http://**************:18000/v1/
      - LLM_API_KEY=EMPTY
      - LLM_DEFAULT_MODEL=Qwen2.5-72B-Instruct-GPTQ-Int4-kd
      - KBS_ADDRESS=http://**************:36039/query
      - REMOTE_TREE_URL=http://**************:36032/idp_tree
      - ENABLE_METRICS=true
    volumes:
      - ../../app:/app/app:ro  # 代码热重载
      - ../../uploads:/app/uploads
      - ../../temp:/app/temp
      - ../../logs:/app/logs
      - ../../tests:/app/tests:ro
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      timeout: 10s
      retries: 5
      interval: 30s
      start_period: 40s
    networks:
      - audit_network
    restart: unless-stopped

  # Celery工作进程（开发模式）
  celery_worker:
    build:
      context: ../..
      dockerfile: docker/dockerfiles/Dockerfile.dev
    container_name: audit_celery_worker_dev
    hostname: audit-worker-dev
    command: celery -A app.tasks.celery_tasks worker --loglevel=debug --concurrency=${CELERY_WORKER_CONCURRENCY:-4} --logfile=/app/logs/celery.log --hostname=audit-worker@%h --events
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - DATABASE_URL=mysql+pymysql://audit_user:audit_password@mysql:3306/audit_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - PYTHONPATH=/app
      - EMBEDDING_API_URL=http://*************:42050/v1/
      - EMBEDDING_API_KEY=EMPTY
      - EMBEDDING_MODEL=bge-m3
      - LLM_API_URL=http://**************:18000/v1/
      - LLM_API_KEY=EMPTY
      - LLM_DEFAULT_MODEL=Qwen2.5-72B-Instruct-GPTQ-Int4-kd
      - KBS_ADDRESS=http://**************:36039/query
      - REMOTE_TREE_URL=http://**************:36032/idp_tree
    volumes:
      - ../../app:/app/app:ro  # 代码热重载
      - ../../uploads:/app/uploads
      - ../../temp:/app/temp
      - ../../logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - audit_network
    restart: unless-stopped

  # Flower监控（开发环境）
  flower:
    build:
      context: ../..
      dockerfile: docker/dockerfiles/Dockerfile.dev
    container_name: audit_flower_dev
    command: celery -A app.tasks.celery_tasks flower --port=5555 --persistent=True --db=/tmp/flower.db --max_workers=10 --purge_offline_workers=60 --enable_events=True --auto_refresh=True --tasks_columns=name,uuid,state,args,kwargs,result,received,started,runtime,worker
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DATABASE_URL=mysql+pymysql://audit_user:audit_password@mysql:3306/audit_db
      - CELERY_TIMEZONE=Asia/Shanghai
      - TZ=Asia/Shanghai
      - FLOWER_UNAUTHENTICATED_API=true
    depends_on:
      - redis
    networks:
      - audit_network
    restart: unless-stopped

volumes:
  mysql_dev_data:
  redis_dev_data:

networks:
  audit_network:
    driver: bridge
