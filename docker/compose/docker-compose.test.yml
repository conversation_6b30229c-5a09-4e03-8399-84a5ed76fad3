# 测试环境Docker Compose配置
version: '3.8'

services:
  # MySQL数据库（测试环境）
  mysql_test:
    image: mysql:8.0
    container_name: audit_mysql_test
    environment:
      MYSQL_ROOT_PASSWORD: testpassword
      MYSQL_DATABASE: audit_test_db
      MYSQL_USER: audit_test_user
      MYSQL_PASSWORD: audit_test_password
    ports:
      - "3307:3306"  # 使用不同端口避免冲突
    volumes:
      - mysql_test_data:/var/lib/mysql
      - ../mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=128M
      --max-connections=100
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "audit_test_user", "-paudit_test_password"]
      timeout: 20s
      retries: 10
      interval: 30s
    networks:
      - audit_test_network
    restart: unless-stopped

  # Redis缓存（测试环境）
  redis_test:
    image: redis:7-alpine
    container_name: audit_redis_test
    ports:
      - "6380:6379"  # 使用不同端口避免冲突
    volumes:
      - redis_test_data:/data
    command: >
      redis-server
      --appendonly yes
      --maxmemory 128mb
      --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - audit_test_network
    restart: unless-stopped

  # FastAPI应用（测试环境）
  api_test:
    build:
      context: ../..
      dockerfile: docker/dockerfiles/Dockerfile.prod
    container_name: audit_api_test
    ports:
      - "8002:8000"
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - DATABASE_URL=mysql+pymysql://audit_test_user:audit_test_password@mysql_test:3306/audit_test_db
      - REDIS_URL=redis://redis_test:6379/0
      - CELERY_BROKER_URL=redis://redis_test:6379/0
      - CELERY_RESULT_BACKEND=redis://redis_test:6379/0
      - PYTHONPATH=/app
      - EMBEDDING_API_URL=http://*************:42050/v1/
      - EMBEDDING_API_KEY=EMPTY
      - EMBEDDING_MODEL=bge-m3
      - LLM_API_URL=http://**************:18000/v1/
      - LLM_API_KEY=EMPTY
      - LLM_DEFAULT_MODEL=Qwen2.5-72B-Instruct-GPTQ-Int4-kd
      - KBS_ADDRESS=http://**************:36039/query
      - REMOTE_TREE_URL=http://**************:36032/idp_tree
      - ENABLE_METRICS=true
    volumes:
      - ../../uploads:/app/uploads
      - ../../temp:/app/temp
      - ../../logs:/app/logs
    depends_on:
      mysql_test:
        condition: service_healthy
      redis_test:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      timeout: 10s
      retries: 5
      interval: 30s
      start_period: 40s
    networks:
      - audit_test_network
    restart: unless-stopped

  # Celery工作进程（测试环境）
  celery_worker_test:
    build:
      context: ../..
      dockerfile: docker/dockerfiles/Dockerfile.celery
    container_name: audit_celery_worker_test
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - DATABASE_URL=mysql+pymysql://audit_test_user:audit_test_password@mysql_test:3306/audit_test_db
      - REDIS_URL=redis://redis_test:6379/0
      - CELERY_BROKER_URL=redis://redis_test:6379/0
      - CELERY_RESULT_BACKEND=redis://redis_test:6379/0
      - PYTHONPATH=/app
      - EMBEDDING_API_URL=http://*************:42050/v1/
      - EMBEDDING_API_KEY=EMPTY
      - EMBEDDING_MODEL=bge-m3
      - LLM_API_URL=http://**************:18000/v1/
      - LLM_API_KEY=EMPTY
      - LLM_DEFAULT_MODEL=Qwen2.5-72B-Instruct-GPTQ-Int4-kd
      - KBS_ADDRESS=http://**************:36039/query
      - REMOTE_TREE_URL=http://**************:36032/idp_tree
    volumes:
      - ../../uploads:/app/uploads
      - ../../temp:/app/temp
      - ../../logs:/app/logs
    depends_on:
      mysql_test:
        condition: service_healthy
      redis_test:
        condition: service_healthy
    networks:
      - audit_test_network
    restart: unless-stopped

  # 测试运行器
  test_runner:
    build:
      context: ../..
      dockerfile: docker/dockerfiles/Dockerfile.dev
    container_name: audit_test_runner
    command: >
      bash -c "
        echo 'Waiting for services to be ready...' &&
        sleep 30 &&
        echo 'Running tests...' &&
        python -m pytest tests/ -v --tb=short --cov=app --cov-report=html --cov-report=term
      "
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - DATABASE_URL=mysql+pymysql://audit_test_user:audit_test_password@mysql_test:3306/audit_test_db
      - REDIS_URL=redis://redis_test:6379/0
      - CELERY_BROKER_URL=redis://redis_test:6379/0
      - CELERY_RESULT_BACKEND=redis://redis_test:6379/0
      - PYTHONPATH=/app
    volumes:
      - ../../:/app
      - test_results:/app/htmlcov
    depends_on:
      api_test:
        condition: service_healthy
      celery_worker_test:
        condition: service_healthy
    networks:
      - audit_test_network
    profiles:
      - test

volumes:
  mysql_test_data:
  redis_test_data:
  test_results:

networks:
  audit_test_network:
    driver: bridge
