# 生产环境Docker Compose配置
version: '3.8'

services:
  # MySQL数据库（生产环境）
  mysql:
    image: mysql:8.0
    container_name: audit_mysql_prod
    environment:
      MYSQL_ROOT_PASSWORD_FILE: /run/secrets/mysql_root_password
      MYSQL_DATABASE: audit_db
      MYSQL_USER: audit_user
      MYSQL_PASSWORD_FILE: /run/secrets/mysql_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_prod_data:/var/lib/mysql
      - ../mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ../configs/mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=1G
      --max-connections=500
      --innodb-log-file-size=256M
      --slow-query-log=1
      --slow-query-log-file=/var/log/mysql/slow.log
      --long-query-time=2
    secrets:
      - mysql_root_password
      - mysql_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
      interval: 30s
    networks:
      - audit_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis缓存（生产环境）
  redis:
    image: redis:7-alpine
    container_name: audit_redis_prod
    ports:
      - "6381:6379"  # 生产环境使用6381端口
    volumes:
      - redis_prod_data:/data
      - ../configs/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - audit_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: audit_nginx_prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ../nginx/conf.d:/etc/nginx/conf.d:ro
      - ../nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - api
    networks:
      - audit_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # FastAPI应用（生产环境）
  api:
    build:
      context: ../..
      dockerfile: docker/dockerfiles/Dockerfile.prod
    image: audit-service:latest
    container_name: audit_api_prod
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - DATABASE_URL=mysql+pymysql://audit_user:${MYSQL_PASSWORD}@mysql:3306/audit_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - PYTHONPATH=/app
      - EMBEDDING_API_URL=${EMBEDDING_API_URL}
      - EMBEDDING_API_KEY=${EMBEDDING_API_KEY}
      - EMBEDDING_MODEL=${EMBEDDING_MODEL}
      - LLM_API_URL=${LLM_API_URL}
      - LLM_API_KEY=${LLM_API_KEY}
      - LLM_DEFAULT_MODEL=${LLM_DEFAULT_MODEL}
      - KBS_ADDRESS=${KBS_ADDRESS}
      - REMOTE_TREE_URL=${REMOTE_TREE_URL}
      - ENABLE_METRICS=true
      - SECRET_KEY_FILE=/run/secrets/secret_key
    volumes:
      - ../../uploads:/app/uploads
      - ../../temp:/app/temp
      - ../../logs:/app/logs
    secrets:
      - secret_key
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      timeout: 10s
      retries: 5
      interval: 30s
      start_period: 60s
    networks:
      - audit_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Celery工作进程（生产环境）
  celery_worker:
    build:
      context: ../..
      dockerfile: docker/dockerfiles/Dockerfile.celery
    image: audit-celery:latest
    container_name: audit_celery_worker_prod
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - DATABASE_URL=mysql+pymysql://audit_user:${MYSQL_PASSWORD}@mysql:3306/audit_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - PYTHONPATH=/app
      - EMBEDDING_API_URL=${EMBEDDING_API_URL}
      - EMBEDDING_API_KEY=${EMBEDDING_API_KEY}
      - EMBEDDING_MODEL=${EMBEDDING_MODEL}
      - LLM_API_URL=${LLM_API_URL}
      - LLM_API_KEY=${LLM_API_KEY}
      - LLM_DEFAULT_MODEL=${LLM_DEFAULT_MODEL}
      - KBS_ADDRESS=${KBS_ADDRESS}
      - REMOTE_TREE_URL=${REMOTE_TREE_URL}
    volumes:
      - ../../uploads:/app/uploads
      - ../../temp:/app/temp
      - ../../logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - audit_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

secrets:
  mysql_root_password:
    file: ../secrets/mysql_root_password.txt
  mysql_password:
    file: ../secrets/mysql_password.txt
  secret_key:
    file: ../secrets/secret_key.txt

volumes:
  mysql_prod_data:
  redis_prod_data:
  nginx_logs:

networks:
  audit_network:
    driver: bridge
