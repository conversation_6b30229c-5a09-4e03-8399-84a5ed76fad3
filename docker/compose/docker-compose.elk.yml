# ELK日志系统Docker Compose配置
# Elasticsearch, Logstash, Kibana, Filebeat, Metricbeat
version: '3.8'

services:
  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: audit_elasticsearch
    environment:
      - node.name=elasticsearch
      - cluster.name=audit-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - http.host=0.0.0.0
      - transport.host=localhost
      - network.host=0.0.0.0
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
      - ../configs/elk/elasticsearch/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml:ro
    ports:
      - "9200:9200"
      - "9300:9300"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - audit_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2g
        reservations:
          memory: 1g

  # Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: audit_logstash
    volumes:
      - ../configs/elk/logstash/logstash.yml:/usr/share/logstash/config/logstash.yml:ro
      - ../configs/elk/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ../../logs:/usr/share/logstash/logs:ro
    ports:
      - "5044:5044"
      - "9600:9600"
    environment:
      LS_JAVA_OPTS: "-Xmx512m -Xms512m"
      ELASTICSEARCH_HOSTS: "http://elasticsearch:9200"
    depends_on:
      elasticsearch:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9600 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - audit_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1g
        reservations:
          memory: 512m

  # Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: audit_kibana
    volumes:
      - ../configs/elk/kibana/kibana.yml:/usr/share/kibana/config/kibana.yml:ro
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
      SERVER_HOST: "0.0.0.0"
      SERVER_BASEPATH: ""
      SERVER_PUBLICBASEURL: "http://localhost:5601"
    depends_on:
      elasticsearch:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    networks:
      - audit_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1g
        reservations:
          memory: 512m

  # Filebeat
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: audit_filebeat
    user: root
    volumes:
      - ../configs/elk/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ../../logs:/usr/share/filebeat/logs:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - filebeat_data:/usr/share/filebeat/data
    environment:
      - ELASTICSEARCH_HOSTS=elasticsearch:9200
      - KIBANA_HOST=kibana:5601
      - LOGSTASH_HOSTS=logstash:5044
    depends_on:
      elasticsearch:
        condition: service_healthy
      logstash:
        condition: service_healthy
    networks:
      - audit_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256m
        reservations:
          memory: 128m

  # Metricbeat (系统指标监控)
  metricbeat:
    image: docker.elastic.co/beats/metricbeat:8.11.0
    container_name: audit_metricbeat
    user: root
    volumes:
      - ../configs/elk/metricbeat/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /sys/fs/cgroup:/hostfs/sys/fs/cgroup:ro
      - /proc:/hostfs/proc:ro
      - /:/hostfs:ro
      - metricbeat_data:/usr/share/metricbeat/data
    environment:
      - ELASTICSEARCH_HOSTS=elasticsearch:9200
      - KIBANA_HOST=kibana:5601
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - audit_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256m
        reservations:
          memory: 128m

volumes:
  elasticsearch_data:
    driver: local
  filebeat_data:
    driver: local
  metricbeat_data:
    driver: local

networks:
  audit_network:
    driver: bridge
