# 开发环境Dockerfile
# 包含开发工具和调试功能
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONPATH=/app

# 安装系统依赖（包含开发工具）
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libmariadb-dev \
    pkg-config \
    curl \
    wget \
    git \
    vim \
    htop \
    procps \
    net-tools \
    iputils-ping \
    telnet \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# 安装开发依赖
RUN pip install --no-cache-dir \
    ipython \
    jupyter \
    pytest-cov \
    pytest-xdist \
    pre-commit

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p /app/uploads /app/temp /app/logs

# 创建非root用户
RUN useradd --create-home --shell /bin/bash --uid 1000 app
RUN chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000 8001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# 默认启动命令（开发模式）
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
