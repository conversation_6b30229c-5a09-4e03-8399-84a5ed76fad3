# Filebeat configuration for audit service

# Filebeat inputs
filebeat.inputs:
  # Application log files
  - type: log
    enabled: true
    paths:
      - /usr/share/filebeat/logs/*.log
    fields:
      service: audit-service
      log_type: application
    fields_under_root: true
    json.keys_under_root: true
    json.add_error_key: true
    json.message_key: message
    multiline.pattern: '^\d{4}-\d{2}-\d{2}'
    multiline.negate: true
    multiline.match: after
    scan_frequency: 10s
    harvester_buffer_size: 16384
    max_bytes: 10485760

  # Docker container logs - 简化配置
  - type: container
    enabled: true
    paths:
      - '/var/lib/docker/containers/*/*.log'
    processors:
      - add_docker_metadata:
          host: "unix:///var/run/docker.sock"
      - decode_json_fields:
          fields: ["message"]
          target: ""
          overwrite_keys: true
    # 只收集审计服务相关的容器
    include_lines: ['audit_api', 'audit_celery', 'audit_flower', 'audit_mysql', 'audit_redis']
    fields:
      log_type: container
      environment: development
    fields_under_root: true

# Filebeat modules
filebeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: false

# Processors
processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
  - add_docker_metadata: ~
  - add_kubernetes_metadata: ~
  - timestamp:
      field: "@timestamp"
      layouts:
        - '2006-01-02T15:04:05.000Z'
        - '2006-01-02T15:04:05Z'
      test:
        - '2023-01-01T12:00:00.000Z'

# Output configuration - 直接输出到Elasticsearch
# output.logstash:
#   hosts: ["logstash:5044"]
#   compression_level: 3
#   bulk_max_size: 2048

# Direct output to Elasticsearch (绕过Logstash问题)
output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "audit-logs-%{+yyyy.MM.dd}"

# Setup template
setup.template.name: "audit-logs"
setup.template.pattern: "audit-logs-*"
setup.template.settings:
  index.number_of_shards: 1
  index.number_of_replicas: 0

# Kibana configuration (disabled for now)
# setup.kibana:
#   host: "kibana:5601"

# Index template settings
setup.template.settings:
  index.number_of_shards: 1
  index.number_of_replicas: 0
  index.refresh_interval: 5s

# Dashboards (disabled for now)
setup.dashboards.enabled: false

# Logging configuration
logging.level: info
logging.to_files: false
logging.to_stderr: true

# Monitoring (disabled to avoid configuration complexity)
monitoring.enabled: false

# HTTP endpoint for health checks
http.enabled: true
http.host: 0.0.0.0
http.port: 5066

# Registry settings
filebeat.registry.path: /usr/share/filebeat/data/registry
filebeat.registry.file_permissions: 0600
filebeat.registry.flush: 1s

# Queue settings
queue.mem:
  events: 4096
  flush.min_events: 512
  flush.timeout: 1s
