# Metricbeat configuration for audit service

# Metricbeat modules
metricbeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: true
  reload.period: 10s

# System module
metricbeat.modules:
  # System metrics
  - module: system
    metricsets:
      - cpu
      - load
      - memory
      - network
      - process
      - process_summary
      - socket_summary
      - filesystem
      - fsstat
    enabled: true
    period: 10s
    processes: ['.*']
    cpu.metrics: ["percentages", "normalized_percentages"]
    core.metrics: ["percentages"]

  # Docker module
  - module: docker
    metricsets:
      - container
      - cpu
      - diskio
      - healthcheck
      - info
      - memory
      - network
    hosts: ["unix:///var/run/docker.sock"]
    period: 10s
    enabled: true

  # HTTP module for service health checks
  - module: http
    metricsets:
      - json
    period: 30s
    hosts: ["http://api:8000/api/v1/health"]
    namespace: "audit_service"
    path: "/"
    enabled: true

  # Redis module
  - module: redis
    metricsets:
      - info
      - keyspace
    period: 10s
    hosts: ["redis:6379"]
    enabled: true

  # MySQL module
  - module: mysql
    metricsets:
      - status
      - performance
    period: 30s
    hosts: ["tcp(mysql:3306)/"]
    username: audit_user
    password: audit_password
    enabled: true

  # Elasticsearch module
  - module: elasticsearch
    metricsets:
      - node
      - node_stats
      - cluster_stats
    period: 30s
    hosts: ["http://elasticsearch:9200"]
    enabled: true

  # Logstash module
  - module: logstash
    metricsets:
      - node
      - node_stats
    period: 30s
    hosts: ["http://logstash:9600"]
    enabled: true

  # Kibana module (disabled for now)
  - module: kibana
    metricsets:
      - status
    period: 30s
    hosts: ["http://kibana:5601"]
    enabled: false

# Processors
processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
  - add_docker_metadata: ~
  - add_kubernetes_metadata: ~

# Output to Elasticsearch
output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "metricbeat-audit-service-%{+yyyy.MM.dd}"

# Kibana configuration (disabled for now)
# setup.kibana:
#   host: "kibana:5601"

# Index template (disabled for now)
setup.template.enabled: false
# setup.template.name: "metricbeat-audit-service"
# setup.template.pattern: "metricbeat-audit-service-*"
# setup.template.settings:
#   index.number_of_shards: 1
#   index.number_of_replicas: 0

# Dashboards (disabled for now)
setup.dashboards.enabled: false

# Logging
logging.level: info
logging.to_files: true
logging.files:
  path: /usr/share/metricbeat/logs
  name: metricbeat
  keepfiles: 7
  permissions: 0644

# Monitoring
monitoring.enabled: true

# HTTP endpoint
http.enabled: true
http.host: 0.0.0.0
http.port: 5067
