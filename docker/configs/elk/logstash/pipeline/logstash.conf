# 简化的Logstash配置，专门用于审计服务日志处理

input {
  beats {
    port => 5044
    host => "0.0.0.0"
  }
}

filter {
  # 首先尝试解析JSON格式的日志（新的结构化日志）
  if [message] =~ /^\{.*\}$/ {
    json {
      source => "message"
      target => "parsed_json"
      tag_on_failure => ["_jsonparsefailure"]
    }
    
    # 如果JSON解析成功，提取字段
    if "_jsonparsefailure" not in [tags] {
      # 提取基础字段
      if [parsed_json][@timestamp] {
        date {
          match => [ "[parsed_json][@timestamp]", "ISO8601" ]
          target => "@timestamp"
        }
      }
      
      # 映射标准字段
      if [parsed_json][level] { mutate { replace => { "level" => "%{[parsed_json][level]}" } } }
      if [parsed_json][logger] { mutate { replace => { "logger" => "%{[parsed_json][logger]}" } } }
      if [parsed_json][message] { mutate { replace => { "message" => "%{[parsed_json][message]}" } } }
      if [parsed_json][service] { mutate { replace => { "service" => "%{[parsed_json][service]}" } } }
      if [parsed_json][hostname] { mutate { replace => { "hostname" => "%{[parsed_json][hostname]}" } } }
      if [parsed_json][environment] { mutate { add_field => { "environment" => "%{[parsed_json][environment]}" } } }
      if [parsed_json][version] { mutate { add_field => { "version" => "%{[parsed_json][version]}" } } }
      
      # 提取业务上下文字段
      if [parsed_json][task] {
        if [parsed_json][task][task_id] { mutate { add_field => { "task_id" => "%{[parsed_json][task][task_id]}" } } }
        if [parsed_json][task][task_name] { mutate { add_field => { "task_name" => "%{[parsed_json][task][task_name]}" } } }
        if [parsed_json][task][task_status] { mutate { add_field => { "task_status" => "%{[parsed_json][task][task_status]}" } } }
      }
      
      if [parsed_json][http] {
        if [parsed_json][http][request_id] { mutate { add_field => { "request_id" => "%{[parsed_json][http][request_id]}" } } }
        if [parsed_json][http][http_method] { mutate { add_field => { "http_method" => "%{[parsed_json][http][http_method]}" } } }
        if [parsed_json][http][url] { mutate { add_field => { "url" => "%{[parsed_json][http][url]}" } } }
        if [parsed_json][http][status_code] { mutate { add_field => { "status_code" => "%{[parsed_json][http][status_code]}" } } }
        if [parsed_json][http][duration_ms] { mutate { add_field => { "duration_ms" => "%{[parsed_json][http][duration_ms]}" } } }
      }
      
      # 添加日志分类标签
      mutate { add_field => { "log_format" => "structured_json" } }
      
      # 清理原始JSON字段
      mutate { remove_field => ["parsed_json"] }
    }
  }
  
  # 处理传统的Celery日志格式（如果不是JSON格式）
  else if [service] == "audit_celery" or [container][name] =~ /celery/ {
    # 解析Celery日志格式: [2025-08-01 03:16:35,202: DEBUG/MainProcess] message
    grok {
      match => { 
        "message" => "\[%{TIMESTAMP_ISO8601:celery_timestamp}: %{LOGLEVEL:celery_level}/%{DATA:celery_process}\] %{GREEDYDATA:celery_message}" 
      }
      tag_on_failure => ["_grokparsefailure_celery"]
    }
    
    # 如果成功解析Celery格式，使用解析后的字段
    if "_grokparsefailure_celery" not in [tags] {
      mutate {
        replace => { "level" => "%{celery_level}" }
        replace => { "message" => "%{celery_message}" }
        add_field => { 
          "process_name" => "%{celery_process}"
          "log_format" => "celery_traditional"
        }
      }
      
      # 解析Celery时间戳
      date {
        match => [ "celery_timestamp", "yyyy-MM-dd HH:mm:ss,SSS" ]
        target => "@timestamp"
      }
      
      # 清理临时字段
      mutate {
        remove_field => ["celery_timestamp", "celery_level", "celery_process", "celery_message"]
      }
    }
  }
  
  # 处理Flower日志格式
  else if [service] == "audit_flower" or [container][name] =~ /flower/ {
    # 解析Flower日志格式: [I 250801 03:03:33 mixins:228] message
    grok {
      match => { 
        "message" => "\[%{DATA:flower_level} %{DATA:flower_date} %{TIME:flower_time} %{DATA:flower_module}:%{INT:flower_line}\] %{GREEDYDATA:flower_message}" 
      }
      tag_on_failure => ["_grokparsefailure_flower"]
    }
    
    # 如果成功解析Flower格式，使用解析后的字段
    if "_grokparsefailure_flower" not in [tags] {
      # 映射Flower日志级别
      if [flower_level] == "I" {
        mutate { replace => { "level" => "INFO" } }
      } else if [flower_level] == "W" {
        mutate { replace => { "level" => "WARN" } }
      } else if [flower_level] == "E" {
        mutate { replace => { "level" => "ERROR" } }
      } else if [flower_level] == "D" {
        mutate { replace => { "level" => "DEBUG" } }
      }
      
      mutate {
        replace => { "message" => "%{flower_message}" }
        add_field => { 
          "module_name" => "%{flower_module}"
          "line_number" => "%{flower_line}"
          "log_format" => "flower"
        }
      }
      
      # 清理临时字段
      mutate {
        remove_field => ["flower_level", "flower_date", "flower_time", "flower_module", "flower_line", "flower_message"]
      }
    }
  }

  # 添加环境信息
  mutate {
    add_field => { 
      "environment" => "development"
      "version" => "1.0.0"
    }
  }
  
  # 分类日志级别
  if [level] == "ERROR" {
    mutate {
      add_tag => ["error"]
    }
  } else if [level] == "WARN" {
    mutate {
      add_tag => ["warning"]
    }
  } else if [level] == "INFO" {
    mutate {
      add_tag => ["info"]
    }
  } else if [level] == "DEBUG" {
    mutate {
      add_tag => ["debug"]
    }
  }
}

output {
  # 输出到Elasticsearch
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "audit-logs-%{+YYYY.MM.dd}"
  }
  
  # 调试输出（开发环境可启用）
  # stdout { codec => rubydebug }
}
