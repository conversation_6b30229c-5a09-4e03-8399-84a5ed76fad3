# MySQL生产环境配置
[mysqld]
# 基本设置
default-authentication-plugin=mysql_native_password
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
default-time-zone='+08:00'

# 连接设置
max_connections=500
max_connect_errors=100000
max_user_connections=450

# 内存设置
innodb_buffer_pool_size=1G
innodb_buffer_pool_instances=4
innodb_log_buffer_size=64M
key_buffer_size=256M
query_cache_size=128M
query_cache_type=1

# 日志设置
innodb_log_file_size=256M
innodb_log_files_in_group=2
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2
log_queries_not_using_indexes=1

# 性能优化
innodb_flush_log_at_trx_commit=2
innodb_flush_method=O_DIRECT
innodb_file_per_table=1
innodb_read_io_threads=8
innodb_write_io_threads=8
innodb_thread_concurrency=16

# 超时设置
wait_timeout=28800
interactive_timeout=28800
net_read_timeout=30
net_write_timeout=60

# 二进制日志
log-bin=mysql-bin
binlog_format=ROW
expire_logs_days=7
max_binlog_size=100M

# 安全设置
skip-name-resolve
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
