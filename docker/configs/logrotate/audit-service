# 审计服务日志轮转配置
# 确保日志文件不会占用过多磁盘空间

# 应用程序日志
/app/logs/*.log {
    # 每日轮转
    daily
    
    # 保留30天的日志
    rotate 30
    
    # 如果日志文件不存在，不报错
    missingok
    
    # 不轮转空文件
    notifempty
    
    # 使用gzip压缩旧日志
    compress
    
    # 延迟压缩，保留最新的一个轮转文件不压缩
    delaycompress
    
    # 创建新的日志文件，权限644，所有者root，组root
    create 644 root root
    
    # 轮转后执行的脚本
    postrotate
        # 重新加载应用程序（如果需要）
        # /usr/bin/supervisorctl restart audit-service > /dev/null 2>&1 || true
        
        # 或者发送USR1信号给应用程序重新打开日志文件
        # pkill -USR1 -f "python.*audit" > /dev/null 2>&1 || true
        
        echo "$(date): 审计服务日志轮转完成" >> /var/log/logrotate.log
    endscript
}

# Celery日志
/app/logs/celery*.log {
    daily
    rotate 30
    missingok
    notifempty
    compress
    delaycompress
    create 644 root root
    
    postrotate
        # 重启Celery worker（如果需要）
        # /usr/bin/supervisorctl restart celery-worker > /dev/null 2>&1 || true
        echo "$(date): Celery日志轮转完成" >> /var/log/logrotate.log
    endscript
}

# 错误日志特殊处理
/app/logs/error*.log {
    # 错误日志每小时检查一次
    hourly
    
    # 保留7天的错误日志
    rotate 168
    
    # 大小超过100MB时立即轮转
    size 100M
    
    missingok
    notifempty
    compress
    delaycompress
    create 644 root root
    
    postrotate
        echo "$(date): 错误日志轮转完成" >> /var/log/logrotate.log
    endscript
}

# 访问日志
/app/logs/access*.log {
    daily
    rotate 7
    missingok
    notifempty
    compress
    delaycompress
    create 644 root root
    
    postrotate
        echo "$(date): 访问日志轮转完成" >> /var/log/logrotate.log
    endscript
}

# 性能日志
/app/logs/performance*.log {
    daily
    rotate 14
    missingok
    notifempty
    compress
    delaycompress
    create 644 root root
    
    postrotate
        echo "$(date): 性能日志轮转完成" >> /var/log/logrotate.log
    endscript
}
