#!/bin/bash
# Docker快速启动脚本

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker快速启动脚本

用法: $0 [环境]

环境:
    dev         开发环境（默认）
    test        测试环境
    prod        生产环境

示例:
    $0              # 启动开发环境
    $0 dev          # 启动开发环境
    $0 prod         # 启动生产环境

EOF
}

# 默认环境
ENVIRONMENT=${1:-dev}

# 检查参数
case $ENVIRONMENT in
    dev|test|prod)
        ;;
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        log_error "不支持的环境: $ENVIRONMENT"
        show_help
        exit 1
        ;;
esac

# 显示启动信息
cat << EOF
🚀 审计服务Docker快速启动
================================
环境: $ENVIRONMENT
项目: $(basename "$PROJECT_ROOT")
时间: $(date)
================================

EOF

# 检查Docker环境
log_info "检查Docker环境..."
if ! command -v docker &> /dev/null; then
    log_error "Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    log_error "Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 初始化环境
log_info "初始化环境..."
"$SCRIPT_DIR/scripts/manage.sh" init -e "$ENVIRONMENT"

# 构建镜像
log_info "构建Docker镜像..."
case $ENVIRONMENT in
    test)
        # 测试环境使用生产环境镜像
        "$SCRIPT_DIR/scripts/build.sh" prod
        ;;
    *)
        "$SCRIPT_DIR/scripts/build.sh" "$ENVIRONMENT"
        ;;
esac

# 启动服务
log_info "启动服务..."
"$SCRIPT_DIR/scripts/deploy.sh" "$ENVIRONMENT" up -d --build

# 等待服务就绪
log_info "等待服务就绪..."
sleep 10

# 健康检查
log_info "执行健康检查..."
"$SCRIPT_DIR/scripts/health-check.sh" "$ENVIRONMENT"

# 显示服务信息
log_success "服务启动完成！"

case $ENVIRONMENT in
    dev)
        cat << EOF

🎯 开发环境服务地址:
- API服务: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/api/v1/health
- Flower监控: http://localhost:5555
- 监控指标: http://localhost:8001/metrics

📊 数据库连接:
- MySQL: localhost:3306
- Redis: localhost:6379

🔧 管理命令:
- 查看日志: docker/scripts/deploy.sh dev logs
- 停止服务: docker/scripts/deploy.sh dev down
- 重启服务: docker/scripts/deploy.sh dev restart

EOF
        ;;
    test)
        cat << EOF

🧪 测试环境服务地址:
- API服务: http://localhost:8002
- 健康检查: http://localhost:8002/api/v1/health

📊 数据库连接:
- MySQL: localhost:3307
- Redis: localhost:6380

🔧 管理命令:
- 运行测试: docker/scripts/deploy.sh test up test_runner
- 查看日志: docker/scripts/deploy.sh test logs
- 停止服务: docker/scripts/deploy.sh test down

EOF
        ;;
    prod)
        cat << EOF

🏭 生产环境服务地址:
- API服务: http://localhost:80
- 健康检查: http://localhost:80/api/v1/health

⚠️  生产环境注意事项:
- 请确保已设置生产环境密钥
- 请配置SSL证书（如需要）
- 请配置防火墙和安全组
- 请定期备份数据

🔧 管理命令:
- 查看状态: docker/scripts/manage.sh monitor -e prod
- 备份数据: docker/scripts/manage.sh backup -e prod
- 查看日志: docker/scripts/deploy.sh prod logs

EOF
        ;;
esac

log_info "快速启动完成！"
