"""
Celery异步任务处理模块

负责处理审计服务的异步任务，包括长时间运行的审计处理、
文档解析、知识库查询等耗时操作。使用Celery分布式任务队列
实现高性能的异步处理能力。

主要任务：
- process_audit_async: 异步审计处理任务
- health_check: 健康检查任务
- cleanup_old_tasks: 清理过期任务

配置特性：
- 任务序列化和压缩
- 任务超时和软限制
- 任务跟踪和监控
- 错误处理和重试机制
"""
import logging
from datetime import datetime
import pytz
from typing import Optional, Dict, Any

from celery import Celery
from sqlalchemy.orm import Session

from app.core.config import get_settings
from app.core.database import SessionLocal
# TaskStatus constants
TASK_PENDING = "pending"
TASK_PROCESSING = "processing"
TASK_COMPLETED = "completed"
TASK_FAILED = "failed"
from app.services.task_service import TaskService
from app.services.audit_service import AuditService

settings = get_settings()

# Initialize Celery app
celery_app = Celery(
    "audit_service",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=['app.tasks.celery_tasks']
)

# Configure Celery
celery_app.conf.update(
    task_serializer=settings.celery_task_serializer,
    result_serializer=settings.celery_result_serializer,
    accept_content=settings.celery_accept_content,
    timezone=settings.celery_timezone,
    enable_utc=settings.celery_enable_utc,
    task_track_started=True,
    task_time_limit=3600,  # 1 hour
    task_soft_time_limit=3300,  # 55 minutes
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=False,
    task_compression='gzip',
    result_compression='gzip',
)

# 使用增强的审计日志记录器
from app.utils.audit_logger import get_audit_logger
logger = get_audit_logger(__name__, service="audit_celery", component="tasks")


def get_db_session() -> Session:
    """Get database session for tasks."""
    return SessionLocal()


@celery_app.task(bind=True, name='audit_service.process_audit_async')
def process_audit_async(
    self,
    task_id: str,
    task_name: str,
    config_yaml: str,
    main_doc_json: Optional[str] = None,
    callback_url: Optional[str] = None
) -> Dict[str, Any]:
    """
    异步处理审计任务

    使用Celery执行长时间运行的审计处理任务，支持任务状态跟踪、
    错误处理和结果回调。适用于大型文档或复杂审计规则的处理。

    处理流程：
    1. 更新任务状态为处理中
    2. 执行同步审计处理逻辑
    3. 更新任务状态和结果
    4. 发送回调通知（如果配置）
    5. 处理异常和错误情况

    Args:
        self: Celery任务实例（bind=True时自动传入）
        task_id: 唯一任务标识符
        task_name: 审计任务名称
        config_yaml: YAML格式的配置字符串
        main_doc_json: 主文档JSON字符串（可选）
        callback_url: 回调通知URL（可选）

    Returns:
        Dict[str, Any]: 处理结果字典，包含状态、数据、时间等信息

    Raises:
        Exception: 处理过程中的各种异常会被捕获并记录到任务状态中
    """
    db = get_db_session()
    task_service = TaskService(db)
    audit_service = AuditService()

    # 设置任务上下文
    logger.set_task_id(task_id)
    beijing_tz = pytz.timezone('Asia/Shanghai')
    start_time = datetime.now(beijing_tz)

    try:
        # 记录任务开始
        logger.audit_event("AUDIT_TASK_START", "异步审计任务开始执行",
                          task_id=task_id,
                          task_name=task_name,
                          celery_task_id=self.request.id)

        # Update task status to processing
        with logger.operation_context("update_task_status_processing"):
            task_service.update_task_status(task_id, TASK_PROCESSING)
            logger.business_log("task", "status_update", "任务状态更新为处理中",
                              old_status="pending",
                              new_status="processing")

        # Process audit
        with logger.operation_context("execute_audit_processing"):
            result = audit_service.process_audit_sync(
                config_yaml=config_yaml,
                main_doc_json=main_doc_json,
                task_name=task_name
            )

        end_time = datetime.now(beijing_tz)
        duration = (end_time - start_time).total_seconds()

        # Update task status to completed
        task_service.update_task_status(
            task_id,
            TASK_COMPLETED,
            result=result
        )

        # Send callback if provided
        if callback_url:
            audit_service.send_callback(
                callback_url=callback_url,
                task_id=task_id,
                task_name=task_name,
                message='审核成功',
                data=result,
                status='true'
            )

        # Clean up task data from Redis
        task_service.delete_task_data(task_id)

        logger.info(f"Async audit task {task_id} completed successfully in {duration:.2f}s")

        return {
            'task_id': task_id,
            'status': 'completed',
            'result': result,
            'duration': duration
        }

    except Exception as e:
        logger.error(f"Error in async audit task {task_id}: {e}", exc_info=True)

        # Update task status to failed
        task_service.update_task_status(
            task_id,
            TASK_FAILED,
            exception_info=str(e)
        )

        # Send error callback if provided
        if callback_url:
            audit_service.send_callback(
                callback_url=callback_url,
                task_id=task_id,
                task_name=task_name,
                message=f'审核出错: {str(e)}',
                status='false'
            )

        # Clean up task data from Redis
        task_service.delete_task_data(task_id)

        # Re-raise the exception for Celery to handle
        raise

    finally:
        db.close()


@celery_app.task(name='audit_service.cleanup_old_tasks')
def cleanup_old_tasks() -> Dict[str, Any]:
    """
    清理过期任务

    定期清理数据库中的过期任务记录，释放存储空间并保持
    数据库性能。默认清理7天前的已完成或失败任务。

    清理策略：
    - 删除7天前的已完成任务
    - 删除7天前的失败任务
    - 保留进行中的任务
    - 记录清理统计信息

    Returns:
        Dict[str, Any]: 清理结果，包含删除的任务数量和状态
    """
    db = get_db_session()

    try:
        from sqlalchemy import and_
        from datetime import timedelta

        # Delete tasks older than 7 days
        beijing_tz = pytz.timezone('Asia/Shanghai')
        cutoff_date = datetime.now(beijing_tz) - timedelta(days=7)

        deleted_count = db.query(RequestTracking).filter(
            and_(
                RequestTracking.status.in_([TASK_COMPLETED, TASK_FAILED]),
                RequestTracking.updated_at < cutoff_date
            )
        ).delete()

        db.commit()

        logger.info(f"Cleaned up {deleted_count} old tasks")

        return {
            'deleted_count': deleted_count,
            'cutoff_date': cutoff_date.isoformat()
        }

    except Exception as e:
        logger.error(f"Error in cleanup task: {e}")
        db.rollback()
        raise

    finally:
        db.close()


@celery_app.task(name='audit_service.health_check')
def health_check() -> Dict[str, Any]:
    """
    健康检查任务

    定期执行的健康检查任务，用于监控Celery工作进程和
    相关服务的运行状态。检查数据库连接、Redis连接等。

    检查项目：
    - 数据库连接状态
    - Redis连接状态
    - 任务队列状态
    - 系统资源使用情况

    Returns:
        Dict[str, Any]: 健康状态字典，包含各组件的状态信息
    """
    try:
        # Test database connection
        db = get_db_session()
        db.execute("SELECT 1")
        db.close()
        db_status = "healthy"
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        db_status = f"unhealthy: {str(e)}"

    try:
        # Test Redis connection
        from app.core.redis import get_redis
        redis_client = get_redis()
        redis_client.ping()
        redis_status = "healthy"
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        redis_status = f"unhealthy: {str(e)}"

    beijing_tz = pytz.timezone('Asia/Shanghai')
    return {
        'timestamp': datetime.now(beijing_tz).isoformat(),
        'database': db_status,
        'redis': redis_status,
        'celery': 'healthy'
    }


# Configure periodic tasks
from celery.schedules import crontab

celery_app.conf.beat_schedule = {
    'cleanup-old-tasks': {
        'task': 'audit_service.cleanup_old_tasks',
        'schedule': crontab(hour=2, minute=0),  # Run daily at 2 AM
    },
    'health-check': {
        'task': 'audit_service.health_check',
        'schedule': 60.0,  # Run every minute
    },
}
