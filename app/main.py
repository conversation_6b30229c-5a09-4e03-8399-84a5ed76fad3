"""
审计服务主应用程序

这是一个基于FastAPI的独立审计服务，提供以下功能：
- 异步和同步审计处理
- 文档解析和分析
- 知识库查询和匹配
- 任务状态跟踪
- 健康检查和监控

主要组件：
- FastAPI应用框架
- Celery异步任务处理
- MySQL数据库存储
- Redis缓存和消息队列
- ELK日志分析系统
"""
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import get_settings
from app.core.database import create_tables
from app.utils.logging import setup_logging
from app.api.v1 import audit, health
from app.models.schemas import ErrorResponse

settings = get_settings()

# Setup logging with ELK support
setup_logging(
    log_file_path=settings.log_file_path,
    debug=settings.debug
)

# 使用增强的审计日志记录器
from app.utils.audit_logger import get_audit_logger
logger = get_audit_logger(__name__, service="audit_api", component="main")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用程序生命周期管理器

    负责应用启动和关闭时的初始化和清理工作：
    - 启动时：创建数据库表、初始化目录结构
    - 关闭时：清理资源和连接

    Args:
        app: FastAPI应用实例

    Yields:
        None: 应用运行期间保持活跃
    """
    # 启动阶段
    logger.info("正在启动审计服务",
               startup_phase="begin",
               app_name=settings.app_name,
               app_version=settings.app_version)

    # 创建数据库表
    try:
        with logger.operation_context("database_initialization"):
            create_tables()
            logger.business_log("database", "create_tables", "数据库表创建/验证完成")
    except Exception as e:
        logger.error_with_traceback("创建数据库表时出错", e, startup_phase="database_init")
        raise

    # 确保上传和临时目录存在
    try:
        from app.utils.file_utils import ensure_directory
        with logger.operation_context("directory_initialization"):
            ensure_directory(settings.upload_dir)
            ensure_directory(settings.temp_dir)
            logger.business_log("filesystem", "create_directories", "文件目录创建/验证完成",
                              upload_dir=settings.upload_dir,
                              temp_dir=settings.temp_dir)
    except Exception as e:
        logger.error_with_traceback("创建文件目录时出错", e, startup_phase="directory_init")
        raise

    logger.audit_event("SERVICE_START", "审计服务启动完成",
                      startup_duration_ms=0,  # 可以添加实际的启动时间
                      status="success")

    yield

    # 关闭阶段
    logger.audit_event("SERVICE_STOP", "审计服务开始关闭")
    logger.info("正在关闭审计服务", shutdown_phase="begin")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Standalone Audit Service with FastAPI, Celery, Redis, and MySQL",
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 异常处理器
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    处理请求验证错误

    当客户端发送的请求数据不符合API规范时触发，
    返回详细的验证错误信息帮助客户端修正请求。

    Args:
        request: HTTP请求对象
        exc: 请求验证异常

    Returns:
        JSONResponse: 包含错误详情的JSON响应
    """
    logger.warning("请求验证错误",
                  url=str(request.url),
                  method=request.method,
                  validation_errors=exc.errors(),
                  error_type="validation_error",
                  status_code=422)
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=ErrorResponse(
            error="validation_error",
            message="请求数据验证失败",
            details={"errors": exc.errors()}
        ).dict()
    )


@app.exception_handler(SQLAlchemyError)
async def database_exception_handler(request: Request, exc: SQLAlchemyError):
    """
    处理数据库错误

    当数据库操作失败时触发，记录详细错误信息并返回
    用户友好的错误响应，避免暴露敏感的数据库信息。

    Args:
        request: HTTP请求对象
        exc: SQLAlchemy数据库异常

    Returns:
        JSONResponse: 数据库错误响应
    """
    logger.error_with_traceback("数据库操作失败", exc,
                               url=str(request.url),
                               method=request.method,
                               error_type="database_error",
                               status_code=500)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            error="database_error",
            message="数据库操作失败"
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """
    处理通用异常

    捕获所有未被其他异常处理器处理的异常，
    记录详细错误信息并返回通用错误响应。

    Args:
        request: HTTP请求对象
        exc: 通用异常

    Returns:
        JSONResponse: 通用错误响应
    """
    logger.error_with_traceback("未处理的服务器异常", exc,
                               url=str(request.url),
                               method=request.method,
                               error_type="internal_error",
                               status_code=500)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            error="internal_error",
            message="服务器内部错误"
        ).dict()
    )


# Include routers
app.include_router(
    health.router,
    prefix="/api/v1",
    tags=["health"]
)

app.include_router(
    audit.router,
    prefix="/api/v1",
    tags=["audit"]
)


# 根路径端点
@app.get("/")
async def root():
    """
    根路径端点

    提供服务基本信息，用于快速检查服务是否正常运行。

    Returns:
        dict: 包含服务名称、版本和状态的字典
    """
    return {
        "service": settings.app_name,
        "version": settings.app_version,
        "status": "running"
    }


# 监控指标端点（如果启用）
if settings.enable_metrics:
    from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
    from fastapi import Response

    @app.get("/metrics")
    async def metrics():
        """
        Prometheus监控指标端点

        提供应用程序的监控指标数据，供Prometheus采集。
        包括请求计数、响应时间、错误率等关键指标。

        Returns:
            Response: Prometheus格式的指标数据
        """
        return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
