import logging
import os
from typing import Dict, Any, Optional
from app.core.config import get_settings
from app.services.http_service import get_http_service, HTTPServiceError
from app.models.schemas import DocTreeNode

settings = get_settings()
logger = logging.getLogger(__name__)

class TreeGenerator:
    def __init__(self):
        self.remote_tree_url = settings.remote_tree_url
        self.http_service = get_http_service()

    def _convert_to_doc_tree_node(self, raw_data: Dict[str, Any]) -> DocTreeNode:
        """
        Recursively convert raw response data to DocTreeNode.

        Args:
            raw_data: Raw data from remote service

        Returns:
            DocTreeNode: Converted tree node
        """
        # Extract basic fields
        text = raw_data.get('text', '')
        summary = raw_data.get('summary', '')
        block_type = raw_data.get('block_type')
        data = raw_data.get('data', {})

        # Recursively convert children
        children = []
        for child_data in raw_data.get('children', []):
            child_node = self._convert_to_doc_tree_node(child_data)
            children.append(child_node)

        # Create DocTreeNode
        return DocTreeNode(
            text=text,
            summary=summary,
            data=data,
            children=children,
            block_type=block_type
        )

    def gen_origin_tree(self, doc_path: str, **kwargs) -> DocTreeNode:
        """
        Generate document structure tree from file and convert to DocTreeNode.

        Args:
            doc_path: Path to the document file

        Returns:
            DocTreeNode: Document structure tree as DocTreeNode object

        Raises:
            FileNotFoundError: If the document file doesn't exist
            HTTPServiceError: If the remote service request fails
        """
        logger.info(f"Generating tree for document: {doc_path}")

        try:
            with open(doc_path, 'rb') as doc_file:
                # Prepare files for multipart/form-data upload
                # The remote service expects request.files.get('doc')
                files = {'doc': doc_file}

                logger.debug(f"Sending file to remote service: {self.remote_tree_url}")
                logger.debug(f"File name: {os.path.basename(doc_path)}")

                # Use the enhanced HTTP service with file upload support
                headers = {
                    'Accept': 'application/json',
                    'User-Agent': settings.http_user_agent
                }

                # Use the upload_file method which handles multipart/form-data correctly
                response = self.http_service.upload_file(
                    self.remote_tree_url,
                    files=files,
                    headers=headers,
                    timeout=settings.http_timeout
                )

                logger.info(f"Received response with status: {response.status_code}")
                raw_response = response.json()

                # Check if the response is successful
                if raw_response.get('ok') != 'true':
                    error_msg = "Remote service returned error"
                    if raw_response.get('data') and isinstance(raw_response['data'], list) and len(raw_response['data']) > 0:
                        error_msg = raw_response['data'][0].get('msg_info', error_msg)
                    logger.error(f"Remote service error: {error_msg}")
                    raise HTTPServiceError(f"Remote service error: {error_msg}")

                # Extract the tree data
                tree_data = raw_response.get('data')
                if not tree_data:
                    logger.error("No tree data in response")
                    raise HTTPServiceError("No tree data in response")

                # Convert to DocTreeNode
                logger.info("Converting response to DocTreeNode...")
                doc_tree_node = self._convert_to_doc_tree_node(tree_data)

                # Log conversion summary
                def count_nodes(node):
                    count = 1
                    for child in node.children:
                        count += count_nodes(child)
                    return count

                total_nodes = count_nodes(doc_tree_node)
                logger.info(f"Successfully converted to DocTreeNode with {total_nodes} nodes")

                return doc_tree_node

        except FileNotFoundError:
            logger.error(f"Document file not found: {doc_path}")
            raise
        except HTTPServiceError as e:
            logger.error(f"HTTP service error for {doc_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"Error generating tree for {doc_path}: {e}")
            raise

tree_generator = TreeGenerator()
