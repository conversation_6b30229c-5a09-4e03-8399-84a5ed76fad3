"""
审计配置服务

负责管理审计配置的服务模块，提供配置解析、验证、访问和管理功能。
支持YAML格式的复杂配置结构，包括模型配置、文档配置、知识库配置等。

主要功能：
- YAML配置解析和验证
- 配置项访问和查询
- 遍历规则管理
- 知识集配置处理
- 路径和目录管理
"""
import os
import time
import logging
from typing import Optional, Dict, Any, List
from pathlib import Path

from app.core.config import get_settings
from app.models.config_models import AuditConfigModel, TraversalRuleConfig
from app.utils.file_utils import ensure_directory

logger = logging.getLogger(__name__)


class ConfigurationError(Exception):
    """配置相关错误异常类"""
    pass


class AuditConfigurationService:
    """
    审计配置服务类

    负责管理和提供审计配置的核心服务，包括：
    - YAML配置解析和验证
    - 配置项的结构化访问
    - 文件路径和目录管理
    - 知识库和模型配置管理
    - 遍历规则配置处理

    该服务确保配置的一致性和有效性，为审计处理器
    提供标准化的配置访问接口。
    """

    def __init__(self, config_yaml: str, app_root_path: str, task_name: str):
        """
        初始化配置服务

        解析YAML配置，初始化文件路径，验证配置有效性。

        Args:
            config_yaml: YAML格式的配置字符串
            app_root_path: 应用程序根路径
            task_name: 任务名称，用于创建专用目录

        Raises:
            ConfigurationError: 配置无效时抛出
        """
        logger.info(f"Initializing audit configuration service for task: {task_name}")

        self.task_name = task_name
        self.app_root_path = app_root_path
        self.raw_yaml = config_yaml

        # Parse configuration
        try:
            self.config = AuditConfigModel.from_yaml(config_yaml)
            logger.info("Configuration parsed successfully")
        except Exception as e:
            logger.error(f"Failed to parse configuration: {e}")
            raise ConfigurationError(f"Invalid configuration: {e}")

        # Initialize paths
        self._init_paths()

        # Validate configuration
        self._validate_configuration()

        logger.info("Audit configuration service initialized successfully")

    def _init_paths(self):
        """Initialize file paths for processing."""
        timestamp = str(time.time())
        self.root_path = os.path.join(self.app_root_path, timestamp)
        self.parse_save_path = os.path.join(self.root_path, 'parse')

        # Ensure directories exist
        ensure_directory(self.root_path)
        ensure_directory(self.parse_save_path)

        logger.debug(f"Initialized paths: root={self.root_path}, parse={self.parse_save_path}")

    def _validate_configuration(self):
        """Validate the configuration."""
        # Check if at least one knowledge set is defined
        if not self.config.knowledge_sets and not self.config._dynamic_fields:
            logger.warning("No knowledge sets defined in configuration")

        # Validate model configurations
        relevance_model = self.get_relevance_model()
        if not relevance_model:
            logger.warning("No relevance model specified, will use default")

        # Validate knowledge base configuration
        if self.config.knowledge_base:
            graphs = self.get_knowledge_base_graphs()
            if not graphs:
                logger.warning("Knowledge base configured but no graphs specified")

    # Model configuration methods
    def get_relevance_model(self) -> str:
        """Get relevance model with fallback to settings."""
        model = self.config.get_relevance_model()
        return model or get_settings().relevance_model

    def get_summary_model(self) -> str:
        """Get summary model with fallback to settings."""
        model = self.config.get_summary_model()
        return model or get_settings().summary_model

    def get_temperature(self) -> float:
        """Get temperature with fallback to settings."""
        temp = self.config.get_temperature()
        return temp if temp is not None else get_settings().temperature

    def get_ocr_model(self) -> str:
        """Get OCR model with fallback to settings."""
        if self.config.global_config and self.config.global_config.ocr_model:
            return self.config.global_config.ocr_model
        return get_settings().ocr_type

    def get_embedding_model(self) -> Optional[str]:
        """Get embedding model."""
        if self.config.models and self.config.models.embedding_model:
            return self.config.models.embedding_model
        return None

    # Knowledge base configuration methods
    def get_knowledge_base_address(self) -> str:
        """Get knowledge base address with fallback to settings."""
        address = self.config.get_knowledge_base_address()
        return address or get_settings().kbs_address

    def get_knowledge_base_graphs(self) -> list:
        """Get knowledge base graph IDs."""
        return self.config.get_knowledge_base_graphs()

    # Processing configuration methods
    def is_advanced_search_enabled(self) -> bool:
        """Check if advanced search is enabled."""
        return self.config.is_advanced_search_enabled()

    def get_similarity_range(self) -> list:
        """Get similarity range."""
        return self.config.get_similarity_range()

    def get_co_docs_top_k(self) -> int:
        """Get co-documents top K value."""
        return self.config.get_co_docs_top_k()

    # Knowledge sets access methods
    def get_knowledge_sets(self) -> Dict[str, Any]:
        """Get all knowledge sets."""
        result = {}

        # Add parsed knowledge sets
        if self.config.knowledge_sets:
            for name, config in self.config.knowledge_sets.items():
                result[name] = config.model_dump(by_alias=True, exclude_none=True)

        # Add dynamic fields (unparsed knowledge sets)
        result.update(self.config._dynamic_fields)

        return result

    def get_knowledge_set(self, name: str) -> Optional[Dict[str, Any]]:
        """Get specific knowledge set by name."""
        knowledge_sets = self.get_knowledge_sets()
        return knowledge_sets.get(name)

    def has_knowledge_set(self, name: str) -> bool:
        """Check if knowledge set exists."""
        return name in self.get_knowledge_sets()

    # Co-documents configuration methods
    def get_co_docs_config(self) -> Optional[Dict[str, Any]]:
        """Get co-documents configuration."""
        if self.config.co_docs:
            return self.config.co_docs.model_dump(by_alias=True, exclude_none=True)
        return None

    def get_co_docs_groups(self) -> list:
        """Get co-documents groups."""
        if self.config.co_docs and self.config.co_docs.groups:
            return [group.model_dump(by_alias=True, exclude_none=True) for group in self.config.co_docs.groups]
        return []

    # Main document configuration methods
    def get_main_doc_config(self) -> Optional[Dict[str, Any]]:
        """Get main document configuration."""
        if self.config.main_doc:
            return self.config.main_doc.model_dump(by_alias=True, exclude_none=True)
        return None

    def get_main_doc_input(self) -> Optional[Dict[str, Any]]:
        """Get main document input configuration."""
        if self.config.main_doc and self.config.main_doc.input:
            return self.config.main_doc.input.model_dump(by_alias=True, exclude_none=True)
        return None

    def get_main_doc_path(self) -> Optional[str]:
        """Get main document path."""
        if self.config.main_doc and self.config.main_doc.input:
            return self.config.main_doc.input.doc_path
        return None

    def get_traversal_rules(self) -> Dict[str, Dict[str, Any]]:
        """Get all traversal rules for main document as dictionaries (for backward compatibility)."""
        if self.config.main_doc and self.config.main_doc.traversal_rules:
            result = {}
            for rule_name, rule_config in self.config.main_doc.traversal_rules.items():
                result[rule_name] = rule_config.model_dump(by_alias=True, exclude_none=True)
            return result
        return {}

    def get_traversal_rule_objects(self) -> Dict[str, 'TraversalRuleConfig']:
        """Get all traversal rules for main document as pydantic objects."""
        if self.config.main_doc and self.config.main_doc.traversal_rules:
            return self.config.main_doc.traversal_rules.copy()
        return {}

    def get_traversal_rule(self, rule_name: str) -> Optional[Dict[str, Any]]:
        """Get specific traversal rule by name."""
        traversal_rules = self.get_traversal_rules()
        return traversal_rules.get(rule_name)

    def has_traversal_rule(self, rule_name: str) -> bool:
        """Check if traversal rule exists."""
        return rule_name in self.get_traversal_rules()

    def get_traversal_rule_names(self) -> List[str]:
        """Get list of all traversal rule names."""
        return list(self.get_traversal_rules().keys())

    def get_traversal_rules_count(self) -> int:
        """Get count of traversal rules."""
        return len(self.get_traversal_rules())

    # Output configuration methods
    def get_output_config(self) -> Dict[str, Any]:
        """Get output configuration."""
        if self.config.output:
            return self.config.output.model_dump(by_alias=True, exclude_none=True)
        return {"filter": "none", "out_doc": "main", "format": "json"}

    # Utility methods
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return self.config.to_dict()

    def to_yaml(self) -> str:
        """Convert configuration to YAML string."""
        return self.config.to_yaml()

    def get_raw_config(self) -> Dict[str, Any]:
        """Get raw configuration dictionary (for backward compatibility)."""
        import yaml
        return yaml.safe_load(self.raw_yaml)

    def get_prompt_config(self) -> Dict[str, Any]:
        """Get prompt configuration from raw config."""
        raw_config = self.get_raw_config()
        return raw_config.get('prompt', {})

    def get_global_config(self) -> Dict[str, Any]:
        """Get global configuration from raw config."""
        raw_config = self.get_raw_config()
        return raw_config.get('global', {})

    def get_models_config(self) -> Dict[str, Any]:
        """Get models configuration from raw config."""
        raw_config = self.get_raw_config()
        return raw_config.get('models', {})

    def save_config(self, file_path: str):
        """Save configuration to file."""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(self.to_yaml())
            logger.info(f"Configuration saved to: {file_path}")
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            raise ConfigurationError(f"Failed to save configuration: {e}")

    def validate_and_get_errors(self) -> list:
        """Validate configuration and return list of errors."""
        errors = []

        # Check required models
        if not self.get_relevance_model():
            errors.append("No relevance model specified")

        if not self.get_summary_model():
            errors.append("No summary model specified")

        # Check knowledge sets
        knowledge_sets = self.get_knowledge_sets()
        if not knowledge_sets:
            errors.append("No knowledge sets defined")

        # Validate each knowledge set
        for name, config in knowledge_sets.items():
            if not isinstance(config, dict):
                errors.append(f"Knowledge set '{name}' has invalid format")
                continue

            # Check if knowledge set has extraction configuration
            if "抽取" not in config and "知识" not in config:
                errors.append(f"Knowledge set '{name}' has no extraction or knowledge configuration")

        # Check knowledge base configuration
        if self.config.knowledge_base:
            graphs = self.get_knowledge_base_graphs()
            if not graphs:
                errors.append("Knowledge base configured but no graphs specified")

        return errors

    def is_valid(self) -> bool:
        """Check if configuration is valid."""
        return len(self.validate_and_get_errors()) == 0

    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary for logging/debugging."""
        return {
            "task_name": self.task_name,
            "relevance_model": self.get_relevance_model(),
            "summary_model": self.get_summary_model(),
            "temperature": self.get_temperature(),
            "ocr_model": self.get_ocr_model(),
            "knowledge_base_address": self.get_knowledge_base_address(),
            "knowledge_base_graphs": self.get_knowledge_base_graphs(),
            "knowledge_sets_count": len(self.get_knowledge_sets()),
            "knowledge_sets": list(self.get_knowledge_sets().keys()),
            "co_docs_groups_count": len(self.get_co_docs_groups()),
            "main_doc_path": self.get_main_doc_path(),
            "traversal_rules_count": self.get_traversal_rules_count(),
            "traversal_rule_names": self.get_traversal_rule_names(),
            "advanced_search_enabled": self.is_advanced_search_enabled(),
            "similarity_range": self.get_similarity_range(),
            "is_valid": self.is_valid()
        }


def create_audit_config(config_yaml: str, app_root_path: str, task_name: str) -> AuditConfigurationService:
    """
    Factory function to create audit configuration service.

    Args:
        config_yaml: YAML configuration string
        app_root_path: Application root path
        task_name: Task name

    Returns:
        AuditConfigurationService: Configured service instance

    Raises:
        ConfigurationError: If configuration is invalid
    """
    return AuditConfigurationService(config_yaml, app_root_path, task_name)
