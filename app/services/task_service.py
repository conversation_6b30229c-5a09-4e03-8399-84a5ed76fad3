"""
任务管理服务

负责处理审计请求和任务跟踪的服务模块。提供任务的创建、状态更新、
查询和管理功能，支持同步和异步任务的完整生命周期管理。

主要功能：
- 任务创建和唯一ID生成
- 任务状态跟踪和更新
- 任务结果存储和检索
- 任务历史记录管理
- Redis缓存集成
"""
import uuid
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.models.database import RequestTracking
from app.models.schemas import TaskStatusResponse
from app.core.database import get_db
from app.core.redis import get_redis

logger = logging.getLogger(__name__)


class TaskService:
    """
    任务管理服务类

    提供审计任务的完整生命周期管理，包括任务创建、状态跟踪、
    结果存储和查询功能。集成数据库持久化和Redis缓存。

    主要职责：
    - 生成唯一任务ID
    - 管理任务状态转换
    - 存储任务配置和结果
    - 提供任务查询接口
    - 处理任务异常情况
    """

    def __init__(self, db: Session):
        """
        初始化任务服务

        Args:
            db: 数据库会话实例
        """
        self.db = db
        self.redis = get_redis()

    def create_task(
        self,
        task_name: str,
        config: str,
        main_doc: Optional[str] = None
    ) -> str:
        """
        创建新的审计任务

        生成唯一的任务ID，创建数据库记录，并在Redis中缓存
        任务配置信息。任务初始状态为pending。

        Args:
            task_name: 任务名称，用于标识和显示
            config: YAML格式的审计配置字符串
            main_doc: 主文档JSON字符串（可选）

        Returns:
            str: 生成的唯一任务ID

        Raises:
            SQLAlchemyError: 数据库操作失败时抛出
        """
        task_id = str(uuid.uuid4())

        try:
            # Create database record
            task_record = RequestTracking(
                task_id=task_id,
                task_name=task_name,
                request_start_time=datetime.utcnow(),
                status="pending"
            )

            self.db.add(task_record)
            self.db.commit()

            # Store task data in Redis for processing
            # 确保所有值都不是None，Redis不能存储None值，并且都是字符串类型
            try:
                task_data = {
                    "task_id": str(task_id),
                    "task_name": str(task_name) if task_name is not None else "",
                    "config": str(config) if config is not None else "",
                    "main_doc": str(main_doc) if main_doc is not None else "",
                    "created_at": str(datetime.utcnow().isoformat())
                }

                logger.info(f"Storing task data in Redis: {list(task_data.keys())}")
                self.redis.hset(f"task:{task_id}", mapping=task_data)
            except Exception as redis_error:
                logger.error(f"Redis error: {redis_error}")
                logger.error(f"Task data types: {[(k, type(v), v) for k, v in task_data.items()]}")
                raise
            try:
                logger.info(f"Setting Redis TTL for task:{task_id}")
                self.redis.expire(f"task:{task_id}", 86400)  # 24 hours TTL
            except Exception as expire_error:
                logger.error(f"Redis expire error: {expire_error}")
                logger.error(f"task_id type: {type(task_id)}, value: {task_id}")
                raise

            logger.info(f"Created task {task_id} with name '{task_name}'")
            return task_id

        except SQLAlchemyError as e:
            logger.error(f"Database error creating task: {e}")
            self.db.rollback()
            raise
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            raise

    def get_task_status(self, task_id: str) -> Optional[TaskStatusResponse]:
        """
        Get task status by ID.

        Args:
            task_id: Task identifier

        Returns:
            Optional[TaskStatusResponse]: Task status or None if not found
        """
        try:
            task_record = self.db.query(RequestTracking).filter(
                RequestTracking.task_id == task_id
            ).first()

            if not task_record:
                return None

            return TaskStatusResponse(
                task_id=task_record.task_id,
                task_name=task_record.task_name,
                status=task_record.status,
                request_start_time=task_record.request_start_time,
                request_end_time=task_record.request_end_time,
                request_result=task_record.request_result,
                exception_info=task_record.exception_info,
                created_at=task_record.created_at,
                updated_at=task_record.updated_at
            )

        except SQLAlchemyError as e:
            logger.error(f"Database error getting task status: {e}")
            raise

    def update_task_status(
        self,
        task_id: str,
        status: str,
        result: Optional[Dict[str, Any]] = None,
        exception_info: Optional[str] = None
    ) -> bool:
        """
        Update task status and result.

        Args:
            task_id: Task identifier
            status: New task status
            result: Task result data (optional)
            exception_info: Exception information (optional)

        Returns:
            bool: True if updated successfully
        """
        try:
            task_record = self.db.query(RequestTracking).filter(
                RequestTracking.task_id == task_id
            ).first()

            if not task_record:
                logger.warning(f"Task {task_id} not found for status update")
                return False

            # Update fields
            task_record.status = status
            if result is not None:
                task_record.request_result = result
            if exception_info is not None:
                task_record.exception_info = exception_info

            # Set end time for completed/failed tasks
            if status in ["completed", "failed"]:
                task_record.request_end_time = datetime.utcnow()

            self.db.commit()

            logger.info(f"Updated task {task_id} status to {status}")
            return True

        except SQLAlchemyError as e:
            logger.error(f"Database error updating task status: {e}")
            self.db.rollback()
            raise

    def get_task_data(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get task data from Redis.

        Args:
            task_id: Task identifier

        Returns:
            Optional[Dict[str, Any]]: Task data or None if not found
        """
        try:
            task_data = self.redis.hgetall(f"task:{task_id}")
            return task_data if task_data else None
        except Exception as e:
            logger.error(f"Error getting task data from Redis: {e}")
            return None

    def delete_task_data(self, task_id: str) -> bool:
        """
        Delete task data from Redis.

        Args:
            task_id: Task identifier

        Returns:
            bool: True if deleted successfully
        """
        try:
            result = self.redis.delete(f"task:{task_id}")
            return result > 0
        except Exception as e:
            logger.error(f"Error deleting task data from Redis: {e}")
            return False

    def get_pending_tasks(self, limit: int = 100) -> List[TaskStatusResponse]:
        """
        Get list of pending tasks.

        Args:
            limit: Maximum number of tasks to return

        Returns:
            List[TaskStatusResponse]: List of pending tasks
        """
        try:
            tasks = self.db.query(RequestTracking).filter(
                RequestTracking.status == "pending"
            ).order_by(RequestTracking.created_at).limit(limit).all()

            return [
                TaskStatusResponse(
                    task_id=task.task_id,
                    task_name=task.task_name,
                    status=task.status,
                    request_start_time=task.request_start_time,
                    request_end_time=task.request_end_time,
                    request_result=task.request_result,
                    exception_info=task.exception_info,
                    created_at=task.created_at,
                    updated_at=task.updated_at
                )
                for task in tasks
            ]

        except SQLAlchemyError as e:
            logger.error(f"Database error getting pending tasks: {e}")
            raise
