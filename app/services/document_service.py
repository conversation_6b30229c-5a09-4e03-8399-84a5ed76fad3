"""
文档处理服务

从原始审计服务中提取的文档处理功能模块。负责处理各种类型的文档，
包括PDF、Excel、图片、JSON等格式的解析、内容提取和结构化处理。

主要功能：
- 多格式文档解析（PDF、Excel、图片、JSON）
- 文档内容提取和清理
- 文档结构分析和层次化处理
- 协同文档处理和匹配
- 文档下载和本地化存储
- OCR文字识别集成
"""
import os
import json
import yaml
import logging
import pandas as pd
import time
from typing import Tuple, Optional, Dict, Any, List, Union
from pathlib import Path

from app.core.config import get_settings
from app.utils.doc_type import DocType
from app.utils.file_utils import(
    download_and_process_doc,
    parse_image,
    parse_excel,
    ensure_directory
)
from app.services.filetree_service import tree_generator

settings = get_settings()
logger = logging.getLogger(__name__)


class AuditConfig:
    """Configuration management for audit processing."""

    def __init__(self, config_yaml: str, app_root_path: str, task_name: str):
        logger.info(f"Initializing audit config for task: {task_name}")
        self.raw_config = yaml.safe_load(config_yaml)
        self.root_path = None
        self.parse_save_path = None
        self.task_name = task_name
        self._init_paths(app_root_path)
        self._init_models()
        self._init_kb_config()

    def _init_paths(self, app_root_path: str):
        """Initialize file paths for processing."""
        timestamp = str(time.time())
        self.root_path = os.path.join(app_root_path, timestamp)
        self.parse_save_path = os.path.join(self.root_path, 'parse')

        # Ensure directories exist
        ensure_directory(self.root_path)
        ensure_directory(self.parse_save_path)

    def _init_models(self):
        """Initialize model configurations."""
        global_conf = self.raw_config.get('global', {})
        self.relevance_model = global_conf.get('RELEVANCE_MODEL', settings.relevance_model)
        self.summary_model = global_conf.get('SUMMARY_MODEL', settings.summary_model)
        self.temperature = global_conf.get('temperature', settings.temperature)
        self.ocr_model = global_conf.get("OCR_MODEL", settings.ocr_type)

    def _init_kb_config(self):
        """Initialize knowledge base configuration."""
        self.kbs_address = settings.kbs_address
        kb_conf = self.raw_config.get('knowledge_base')
        if kb_conf:
            self.global_kbs = [graph['id'] for graph in kb_conf['graph']['graphs']]
            ip = kb_conf.get('ip', '***********')
            port = kb_conf.get('port', 9159)
            self.kbs_address = f'http://{ip}:{port}/query'
        else:
            self.global_kbs = []


class DocumentProcessor:
    """
    文档处理器

    负责处理各种类型文档的核心处理器，提供统一的文档处理接口。
    支持主文档和协同文档的解析、内容提取、结构分析等功能。

    主要功能：
    - 主文档处理和内容提取
    - 协同文档批量处理
    - 多格式文档解析支持
    - 文档结构化和层次化处理
    - OCR文字识别集成
    - 文档缓存和优化
    """

    def __init__(self, config_service):
        """
        初始化文档处理器

        Args:
            config_service: 审计配置服务实例，提供配置和路径管理
        """
        self.config_service = config_service
        # For backward compatibility, create a simple config object
        self.config = type('Config', (), {
            'root_path': config_service.root_path,
            'parse_save_path': config_service.parse_save_path,
            'task_name': config_service.task_name,
            'ocr_model': config_service.get_ocr_model(),
            'raw_config': config_service.get_raw_config()
        })()

    def process_main_doc(
        self,
        main_doc_path: Optional[str] = None,
        main_doc_json: Optional[str] = None
    ) -> Tuple[str, DocType, Any]:
        """
        Process main document.

        Args:
            main_doc_path: Path to main document
            main_doc_json: JSON string of main document

        Returns:
            Tuple[str, DocType, Any]: (document, document_type, processed_data)
        """
        logger.info(f"Processing main document: path={main_doc_path}, json={bool(main_doc_json)}")

        if not main_doc_path and not main_doc_json:
            raise ValueError('No main_doc provided')

        if main_doc_path:
            # Download and process document from URL
            flag, resp = download_and_process_doc(main_doc_path, self.config.root_path)
            if flag is None:
                raise ValueError(resp)
            main_doc = flag
        else:
            main_doc = main_doc_json

        # Determine document type
        main_doc_type = DocType.from_doc_path(main_doc) if not main_doc_json else DocType.JSON
        logger.debug(f"Document type: {main_doc_type}")

        # Load and process document data
        flag, main_temp = self.load_data(
            main_doc_type,
            main_doc,
            self.config.parse_save_path,
            self.config.task_name.startswith("【cw】"),
            self.config.ocr_model
        )

        if not flag:
            raise ValueError(main_temp)

        return main_doc, main_doc_type, main_temp

    def process_co_docs(self) -> Tuple[List, List, List, List, List, List]:
        """
        Process co-documents.

        Returns:
            Tuple: (co_docs_list, co_blocks, co_datas, co_titles, co_contents, co_kbs)
        """
        co_docs_config = self.config.raw_config.get('co_docs', {})
        co_docs_list = co_docs_config.get('groups', []) if co_docs_config else []

        # Process co-documents
        flag, resp = self.process_co_docs_internal(
            co_docs_list,
            self.config.root_path,
            self.config.parse_save_path,
            self.config.task_name.startswith("【cw】"),
            self.config.ocr_model
        )

        if not flag:
            raise ValueError(resp)

        return (
            co_docs_list,
            resp['co_blocks'],
            resp['co_datas'],
            resp['co_titles'],
            resp['co_contents'],
            resp['co_kbs']
        )

    def load_data(
        self,
        doc_type: DocType,
        doc_temp: str,
        parse_save_path: Optional[str] = None,
        use_ocr: bool = False,
        ocr_type: str = "paddle"
    ) -> Tuple[bool, Any]:
        """
        Load and process document data based on type.

        Args:
            doc_type: Document type
            doc_temp: Document content or path
            parse_save_path: Path for saving parsed content
            use_ocr: Whether to use OCR
            ocr_type: OCR type to use

        Returns:
            Tuple[bool, Any]: (success, processed_data)
        """
        try:
            if doc_type == DocType.EXCEL:
                res = pd.read_excel(doc_temp).fillna(method='ffill')
            elif doc_type == DocType.JSON:
                res = json.loads(doc_temp)
            elif doc_type == DocType.IMAGE:
                texts, data, title, content, _ = parse_image(doc_temp)
                res = {
                    'texts': texts,
                    'data': data,
                    'title': title,
                    'content': content
                }
            else:
                try:
                    # TreeGenerator.gen_origin_tree only takes doc_path parameter
                    res = tree_generator.gen_origin_tree(doc_temp)
                except Exception as e:
                    return False, f"Error processing main document: {str(e)}"

            return True, res

        except Exception as e:
            logger.error(f"Error loading document data: {e}")
            return False, f"Document processing error: {str(e)}"

    def process_co_docs_internal(
        self,
        co_docs: List[Dict],
        root_file_path: str,
        parse_save_path: str,
        use_ocr: bool = False,
        ocr_type: str = "paddle"
    ) -> Tuple[bool, Dict]:
        """
        Internal method to process co-documents.

        Args:
            co_docs: List of co-document configurations
            root_file_path: Root file path
            parse_save_path: Parse save path
            use_ocr: Whether to use OCR
            ocr_type: OCR type

        Returns:
            Tuple[bool, Dict]: (success, processed_data)
        """
        co_blocks = []
        co_datas = []
        co_titles = []
        co_contents = []
        co_kbs = []

        if not co_docs:
            return True, {
                'co_blocks': co_blocks,
                'co_datas': co_datas,
                'co_titles': co_titles,
                'co_contents': co_contents,
                'co_kbs': co_kbs
            }

        try:
            for group in co_docs:
                doc_paths = group.get('input', {}).get('doc_paths', '')
                if doc_paths:
                    rule = group.get('检索规则', {})
                    co_blocks_group = []
                    co_datas_group = []
                    co_titles_group = []
                    co_contents_group = []

                    for doc in doc_paths.split(','):
                        logger.info(f'Processing co-doc: {doc}')
                        local_path, error = download_and_process_doc(doc.strip(), root_file_path)
                        if error:
                            return False, error

                        # Process document (simplified)
                        texts, data, title, content, error = self.process_document(
                            local_path, parse_save_path, rule, use_ocr, ocr_type
                        )
                        if error:
                            return False, error

                        co_blocks_group.extend(texts)
                        co_datas_group.extend(data)
                        co_titles_group.extend(title)
                        co_contents_group.extend(content)

                    co_blocks.append(co_blocks_group)
                    co_datas.append(co_datas_group)
                    co_titles.append(co_titles_group)
                    co_contents.append(co_contents_group)
                    co_kbs.append(group.get('kb', []))

            return True, {
                'co_blocks': co_blocks,
                'co_datas': co_datas,
                'co_titles': co_titles,
                'co_contents': co_contents,
                'co_kbs': co_kbs
            }

        except Exception as e:
            logger.error(f"Error processing co-documents: {e}")
            return False, f"Co-document processing error: {str(e)}"

    def process_document(
        self,
        doc_path: str,
        parse_save_path: str,
        rule: Dict,
        use_ocr: bool = False,
        ocr_type: str = "paddle"
    ) -> tuple[list, list, list, list, Union[str, list, None]]:
        """
        Process individual document.

        Args:
            doc_path: Document path
            parse_save_path: Parse save path
            rule: Processing rule
            use_ocr: Whether to use OCR
            ocr_type: OCR type

        Returns:
            Tuple: (texts, data, title, content, error)
        """
        try:
            doc_type = DocType.from_doc_path(doc_path)

            if doc_type == DocType.IMAGE:
                return parse_image(doc_path)
            elif doc_type == DocType.EXCEL:
                return parse_excel(doc_path, rule)
            elif doc_type == DocType.PDF:
                # Process PDF document using tree generator
                from app.services.filetree_service import tree_generator

                try:
                    # Generate document tree
                    doc_tree = tree_generator.gen_origin_tree(doc_path)

                    # Extract text blocks from tree
                    texts = []
                    data = []
                    titles = []
                    contents = []

                    def extract_from_node(node, level=0):
                        if hasattr(node, 'text') and node.text.strip():
                            texts.append(node.text.strip())
                            data.append({
                                'chapter_chain': [node.get_title()] if hasattr(node, 'get_title') else [],
                                'position': getattr(node, 'position', {}),
                                'level': level
                            })
                            titles.append(node.get_title() if hasattr(node, 'get_title') else '')
                            contents.append(node.text.strip())

                        # Process children
                        if hasattr(node, 'children'):
                            for child in node.children:
                                extract_from_node(child, level + 1)

                    extract_from_node(doc_tree)

                    if not texts:
                        # Fallback if no text extracted
                        texts = [f"Document: {os.path.basename(doc_path)}"]
                        data = [{}]
                        titles = [os.path.basename(doc_path)]
                        contents = [f"PDF document: {os.path.basename(doc_path)}"]

                    return texts, data, titles, contents, None

                except Exception as e:
                    logger.error(f"Error processing PDF {doc_path}: {e}")
                    # Fallback to placeholder
                    texts = [f"Document: {os.path.basename(doc_path)}"]
                    data = [{}]
                    titles = [os.path.basename(doc_path)]
                    contents = [f"PDF processing failed: {os.path.basename(doc_path)}"]
                    return texts, data, titles, contents, None
            else:
                # Fallback for other document types
                texts = [f"Document: {os.path.basename(doc_path)}"]
                data = [{}]
                titles = [os.path.basename(doc_path)]
                contents = [f"Unsupported document type: {os.path.basename(doc_path)}"]
                return texts, data, titles, contents, None

        except Exception as e:
            logger.error(f"Error processing document {doc_path}: {e}")
            return [], [], [], [], f"Document processing error: {str(e)}"
