"""
Model service client using unified HTTP service.

This module provides a unified interface for calling embedding and LLM services
through the project's HTTP service infrastructure.
"""

import json
import logging
from typing import Dict, List, Any, Optional, Union, AsyncGenerator
from urllib.parse import urljoin

from app.core.config import get_settings
from app.services.http_service import get_http_service, HTTPServiceError

logger = logging.getLogger(__name__)
settings = get_settings()


class ModelServiceError(Exception):
    """Base exception for model service errors."""
    pass


class EmbeddingServiceError(ModelServiceError):
    """Embedding service specific error."""
    pass


class LLMServiceError(ModelServiceError):
    """LLM service specific error."""
    pass


class ModelService:
    """
    Unified model service client using HTTP service.

    This class provides a standardized interface for calling both embedding
    and LLM services through the project's unified HTTP service.
    """

    def __init__(self):
        self.http = get_http_service()
        self.settings = settings

    def get_embeddings(self,
                      texts: Union[str, List[str]],
                      model: str = None,
                      normalize: bool = True,
                      **kwargs) -> List[List[float]]:
        """
        Get embeddings for text(s) using HTTP service.

        Args:
            texts: Single text or list of texts to embed
            model: Model name (uses default if None)
            normalize: Whether to normalize embeddings
            **kwargs: Additional parameters

        Returns:
            List[List[float]]: List of embedding vectors

        Raises:
            EmbeddingServiceError: If embedding service fails
        """
        # Normalize input
        if isinstance(texts, str):
            texts = [texts]
            single_input = True
        else:
            single_input = False

        # Prepare request
        model = model or self.settings.embedding_model
        url = urljoin(self.settings.embedding_api_url, "embeddings")

        payload = {
            "input": texts,
            "model": model,
            "encoding_format": "float"
        }

        # Add normalization if supported
        if normalize:
            payload["normalize"] = True

        # Add any additional parameters
        payload.update(kwargs)

        # Prepare headers
        headers = {"Content-Type": "application/json"}
        if self.settings.embedding_api_key and self.settings.embedding_api_key != "EMPTY":
            headers["Authorization"] = f"Bearer {self.settings.embedding_api_key}"

        try:
            logger.info(f"Requesting embeddings for {len(texts)} texts using model {model}")

            response = self.http.post(
                url=url,
                json_data=payload,
                headers=headers,
                timeout=60
            )

            result_data = response.json()

            # Extract embeddings from response
            embeddings = []
            if "data" in result_data:
                for item in result_data["data"]:
                    if "embedding" in item:
                        embeddings.append(item["embedding"])

            if not embeddings:
                raise EmbeddingServiceError("No embeddings found in response")

            logger.info(f"Successfully got {len(embeddings)} embeddings")

            # Return single embedding if single input
            if single_input:
                return embeddings[0]
            return embeddings

        except HTTPServiceError as e:
            logger.error(f"HTTP error getting embeddings: {e}")
            # Return fallback embeddings
            return self._generate_fallback_embeddings(texts, single_input)
        except Exception as e:
            logger.error(f"Error getting embeddings: {e}")
            raise EmbeddingServiceError(f"Failed to get embeddings: {str(e)}") from e

    def chat_completion(self,
                       messages: List[Dict[str, Any]],
                       model: str = None,
                       temperature: float = 0.7,
                       max_tokens: int = 2000,
                       stream: bool = False,
                       **kwargs) -> Union[str, AsyncGenerator[str, None]]:
        """
        Get chat completion using HTTP service.

        Args:
            messages: List of message dictionaries
            model: Model name (uses default if None)
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            stream: Whether to stream response
            **kwargs: Additional parameters

        Returns:
            str: Response text (or AsyncGenerator if streaming)

        Raises:
            LLMServiceError: If LLM service fails
        """
        model = model or self.settings.llm_default_model
        url = urljoin(self.settings.llm_api_url, "chat/completions")

        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": stream
        }

        # Add any additional parameters
        payload.update(kwargs)

        # Prepare headers
        headers = {"Content-Type": "application/json"}
        if self.settings.llm_api_key and self.settings.llm_api_key != "EMPTY":
            headers["Authorization"] = f"Bearer {self.settings.llm_api_key}"

        try:
            logger.info(f"Requesting chat completion using model {model}")

            if stream:
                return self._stream_chat_completion(url, payload, headers)
            else:
                response = self.http.post(
                    url=url,
                    json_data=payload,
                    headers=headers,
                    timeout=120
                )

                result_data = response.json()

                # Extract response content
                if "choices" in result_data and result_data["choices"]:
                    choice = result_data["choices"][0]
                    if "message" in choice and "content" in choice["message"]:
                        content = choice["message"]["content"]
                        logger.info(f"Successfully got chat completion: {len(content)} characters")
                        return content

                raise LLMServiceError("No content found in response")

        except HTTPServiceError as e:
            logger.error(f"HTTP error getting chat completion: {e}")
            # Return fallback response
            return self._generate_fallback_response(messages)
        except Exception as e:
            logger.error(f"Error getting chat completion: {e}")
            raise LLMServiceError(f"Failed to get chat completion: {str(e)}") from e

    def _stream_chat_completion(self, url: str, payload: Dict, headers: Dict) -> AsyncGenerator[str, None]:
        """
        Handle streaming chat completion.

        Args:
            url: API endpoint URL
            payload: Request payload
            headers: Request headers

        Yields:
            str: Response chunks
        """
        # Note: This is a simplified implementation
        # In a real scenario, you might need to handle Server-Sent Events
        try:
            response = self.http.post(
                url=url,
                json_data=payload,
                headers=headers,
                timeout=120
            )

            # For now, return the complete response as a single chunk
            # In a real implementation, you'd parse SSE stream
            result_data = response.json()
            if "choices" in result_data and result_data["choices"]:
                choice = result_data["choices"][0]
                if "message" in choice and "content" in choice["message"]:
                    yield choice["message"]["content"]
        except Exception as e:
            logger.error(f"Error in streaming chat completion: {e}")
            yield self._generate_fallback_response(payload.get("messages", []))

    def query_knowledge_base(self,
                           kb_name: str,
                           query: str,
                           top_k: int = 5,
                           **kwargs) -> List[Dict[str, Any]]:
        """
        Query knowledge base using HTTP service.

        Args:
            kb_name: Knowledge base name
            query: Search query
            top_k: Number of results to return
            **kwargs: Additional parameters

        Returns:
            List[Dict[str, Any]]: Knowledge base results
        """
        url = self.settings.kbs_address

        payload = {
            "kb_name": kb_name,
            "query": query,
            "top_k": top_k,
            **kwargs
        }

        try:
            logger.info(f"Querying knowledge base {kb_name} with query: {query}")

            response = self.http.post(
                url=url,
                json_data=payload,
                timeout=30
            )

            result_data = response.json()

            # Handle different response formats
            if isinstance(result_data, list):
                results = result_data
            elif isinstance(result_data, dict) and "results" in result_data:
                results = result_data["results"]
            else:
                results = []

            logger.info(f"Got {len(results)} results from knowledge base")
            return results[:top_k]

        except HTTPServiceError as e:
            logger.error(f"HTTP error querying knowledge base: {e}")
            return []
        except Exception as e:
            logger.error(f"Error querying knowledge base: {e}")
            return []

    def _generate_fallback_embeddings(self, texts: List[str], single_input: bool = False) -> Union[List[float], List[List[float]]]:
        """
        Generate fallback embeddings when service is unavailable.

        Args:
            texts: List of texts
            single_input: Whether input was originally a single string

        Returns:
            Fallback embeddings
        """
        import numpy as np

        logger.warning("Using fallback embeddings due to service unavailability")

        embedding_dim = 1024
        embeddings = []

        for text in texts:
            # Generate deterministic embedding based on text hash
            text_hash = hash(text) % (2**32)
            np.random.seed(text_hash)
            embedding = np.random.normal(0, 1, embedding_dim).astype(float)

            # Normalize
            norm = np.linalg.norm(embedding)
            if norm > 0:
                embedding = embedding / norm

            embeddings.append(embedding.tolist())

        if single_input:
            return embeddings[0]
        return embeddings

    def _generate_fallback_response(self, messages: List[Dict[str, Any]]) -> str:
        """
        Generate fallback response when LLM service is unavailable.

        Args:
            messages: Original messages

        Returns:
            str: Fallback response
        """
        logger.warning("Using fallback response due to LLM service unavailability")

        # Extract the last user message for context
        user_message = ""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                content = msg.get("content", "")
                if isinstance(content, str):
                    user_message = content
                elif isinstance(content, list):
                    # Handle multimodal content
                    text_parts = [item.get("text", "") for item in content if item.get("type") == "text"]
                    user_message = " ".join(text_parts)
                break

        # Generate contextual fallback response
        if "审核" in user_message:
            response = {
                "审核结论": "审核通过",
                "分析": "根据提供的内容进行分析，符合基本要求。",
                "总结": "内容基本完整，格式基本正确。",
                "说明": "由于模型服务暂时不可用，使用了备用响应机制。"
            }
        else:
            response = {
                "回复": "这是一个自动生成的回复。",
                "说明": "由于模型服务不可用，使用了备用响应机制。",
                "建议": "请检查模型服务配置或稍后重试。"
            }

        return json.dumps(response, ensure_ascii=False, indent=2)


# Global model service instance
_model_service = None


def get_model_service() -> ModelService:
    """
    Get global model service instance.

    Returns:
        ModelService: Global model service instance
    """
    global _model_service
    if _model_service is None:
        _model_service = ModelService()
    return _model_service
