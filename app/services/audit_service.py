"""
Core audit service extracted and refactored from the original audit service.
"""
import json
import logging
import concurrent.futures
import pandas as pd
import re
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime

from app.core.config import get_settings
from app.services.config_service import AuditConfigurationService, ConfigurationError
from app.models.config_models import TraversalRuleConfig
from app.services.document_service import DocumentProcessor
from app.services.http_service import get_http_service, HTTPServiceError
from app.utils.doc_type import DocType
from app.utils.text_processing import auto_text_clean, adjust_indent, extract_json
from app.utils.document_processing import Doc_, parse_excel, parse_image
from app.utils.document_tree import DocTreeNode, rule_filter
from app.utils.model_client import get_chat_completion
from app.utils.audit_prompts import DocumentAuditPrompt
from app.utils.embedding_utils import get_embedding, build_index, rerank

settings = get_settings()
logger = logging.getLogger(__name__)

# Constants from original audit service
title_dict = {
    '一': ['一'],
    '二': ['一', '二'],
    '三': ['一', '二', '三'],
    '四': ['一', '二', '三', '四'],
    '五': ['一', '二', '三', '四', '五'],
    '六': ['一', '二', '三', '四', '五', '六'],
    '七': ['一', '二', '三', '四', '五', '六', '七'],
    '八': ['一', '二', '三', '四', '五', '六', '七', '八'],
    '九': ['一', '二', '三', '四', '五', '六', '七', '八', '九'],
    '十': ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
}


class AuditProcessor:
    """
    审计处理器

    负责执行文档审计的核心处理逻辑，包括：
    - 文档内容提取和解析
    - 知识库查询和匹配
    - 规则验证和合规检查
    - 结果汇总和格式化

    支持多线程并发处理以提高性能。
    """

    def __init__(self, config_service: AuditConfigurationService, num_threading: int = 1, rerank: bool = False, **kwargs):
        """
        初始化审计处理器

        Args:
            config_service: 审计配置服务实例，提供配置管理功能
            num_threading: 并发处理线程数，默认1个线程
            rerank: 是否启用重排序功能，提高匹配精度
            **kwargs: 其他处理参数，包括文档数据、模型配置等
        """
        self.config_service = config_service
        self.conf = config_service.get_raw_config()  # 向后兼容性
        self.rerank = rerank
        self.kwargs = kwargs
        self.num_threading = num_threading

        # 使用配置服务方法提取配置
        self.top_k = self.config_service.get_co_docs_top_k()

        # 考虑实际可用的文档块数量
        co_blocks = kwargs.get("co_blocks", [])
        if co_blocks:
            self.top_k = min([self.top_k] + [len(blocks) for blocks in co_blocks])

        logger.info(f"top_k设置为: {self.top_k}")

        # 高级搜索配置
        self.is_advanced_search = self.config_service.is_advanced_search_enabled()
        similarity_range = self.config_service.get_similarity_range()
        self.low, self.high = similarity_range if self.is_advanced_search else [0, 99]

        logger.info(f"审计处理器初始化完成 top_k={self.top_k}, "
                   f"高级搜索={self.is_advanced_search}, "
                   f"相似度范围=[{self.low}, {self.high}], "
                   f"重排序={self.rerank}")

    def audit_walk(self) -> List[Dict[str, Any]]:
        """
        主要审计处理方法

        遍历所有配置的审计规则，对每个规则执行完整的审计流程：
        1. 提取文档内容块
        2. 执行知识库查询
        3. 进行合规性检查
        4. 汇总审计结果

        Returns:
            List[Dict[str, Any]]: 所有规则的审计结果列表
        """
        try:
            main_doc_type = self.kwargs.get('main_doc_type')
            main_temp = self.kwargs.get('main_temp')

            # Get traversal rules using config service
            traversal_rules = self.config_service.get_traversal_rules()

            if not traversal_rules:
                logger.warning("No traversal rules found")
                return []

            all_results = []

            # Process each traversal rule (following original logic)
            for rule_key, rule in traversal_rules.items():
                logger.info(f"Processing traversal rule: {rule_key}")

                # Extract blocks using this rule (original _extract_blocks logic)
                blocks, datas, titles, contents, reference_content = self._extract_blocks(rule, main_doc_type, main_temp)

                logger.debug(f'Rule {rule_key} extracted {len(blocks)} blocks')
                logger.debug(f"blocks: {blocks}")
                logger.debug(f"datas: {datas}")
                logger.debug(f"titles: {titles}")
                logger.debug(f"contents: {contents}")

                # Update kwargs with extracted data for this rule
                self.kwargs.update({
                    'blocks': blocks,
                    'datas': datas,
                    'titles': titles,
                    'contents': contents,
                    'emb_reg': rule.get('emb_reg'),
                    'reference_content': reference_content,
                    'qa_reg': rule.get('qa_reg')
                })

                # Clean up blocks (replace empty with content)
                for i in range(len(blocks)):
                    if not blocks[i] or blocks[i] == 'null':
                        blocks[i] = contents[i] if i < len(contents) else ""

                logger.info(f'Processing {len(blocks)} blocks for rule {rule_key}')

                # Process this rule's blocks using group audit (original logic)
                rule_results = self._group_audit()
                all_results.extend(rule_results)

                logger.info(f"Rule '{rule_key}' produced {len(rule_results)} audit results")

            logger.info(f"Audit processing completed with {len(all_results)} total results")
            return all_results

        except Exception as e:
            logger.error(f"Error in audit processing: {e}")
            raise

    def _extract_blocks(self, rule, doc_type, main_temp):
        """
        从不同类型的文档中提取文本块和相关内容 (基于原始逻辑)

        Args:
            rule: 提取规则配置
            doc_type: 文档类型(EXCEL/JSON/PDF等)
            main_temp: 文档内容

        Returns:
            tuple: (blocks, datas, titles, contents, reference_content)
        """
        try:
            # 获取基础配置
            qa_reg = rule.get('qa_reg', '')
            emb_reg = rule.get('emb_reg', '')

            logger.debug(f"Extracting blocks with qa_reg: {qa_reg}, emb_reg: {emb_reg}")

            # 根据文档类型分发处理
            if doc_type == 'EXCEL':
                return self._process_excel(main_temp, emb_reg, qa_reg)
            elif doc_type == 6:  # JSON类型
                return self._process_json(main_temp, emb_reg, qa_reg)
            elif doc_type == 'IMAGE':
                return self._parse_image(main_temp)
            else:  # PDF等其他文档
                return self._process_other(rule, main_temp)

        except Exception as e:
            logger.error(f"Error extracting blocks: {e}")
            return [], [], [], {}, []

    def _get_qa_reg_list(self, qa_reg):
        """处理qa_reg规则,返回处理后的规则列表"""
        if not qa_reg:
            return []

        qa_reg_list = qa_reg.split(',')

        # 处理特殊的层级标题规则 (如果需要的话)
        # 这里可以根据实际需求添加特殊处理逻辑

        return qa_reg_list

    def _process_excel(self, main_temp, emb_reg, qa_reg):
        """处理Excel类型文档"""
        try:
            if not emb_reg:
                return [], [], [], {}, []

            emb_cols = emb_reg.split(',')
            qa_reg_list = self._get_qa_reg_list(qa_reg)

            # 合并多列数据生成文本块
            blocks = []
            if hasattr(main_temp, 'columns'):  # pandas DataFrame
                for i in range(len(main_temp)):
                    block = ''.join(str(main_temp.iloc[i][col]) for col in emb_cols if col in main_temp.columns)
                    blocks.append(block)

                # 提取QA相关列的内容
                contents = {}
                for name in qa_reg.split(',') if qa_reg else []:
                    if name in main_temp.columns:
                        contents[name] = main_temp[name].astype(str).tolist()

                # 提取引用内容
                reference_content = []
                if 'reference_content' in main_temp.columns:
                    reference_content = main_temp['reference_content'].astype(str).tolist()
                else:
                    reference_content = [{}] * len(blocks)
            else:
                # 处理其他格式的Excel数据
                blocks = []
                contents = {}
                reference_content = []

            return blocks, [{}] * len(blocks), blocks, contents, reference_content

        except Exception as e:
            logger.error(f"Error processing Excel: {e}")
            return [], [], [], {}, []

    def _process_json(self, main_temp, emb_reg, qa_reg):
        """处理JSON类型文档"""
        try:
            if not isinstance(main_temp, list):
                return [], [], [], {}, []

            # 确定需要提取的列
            emb_cols = emb_reg.split(',') if emb_reg else qa_reg.split(',') if qa_reg else []
            qa_reg_list = self._get_qa_reg_list(qa_reg)
            required_cols = list(set(emb_cols + qa_reg_list))

            # 过滤有效数据
            valid_items = []
            for item in main_temp:
                if isinstance(item, dict) and any(item.get(col) for col in required_cols):
                    valid_items.append(item)

            if not valid_items:
                return [], [], [], {}, []

            # 构建返回数据
            blocks = []
            for item in valid_items:
                block = ''.join(str(item.get(col, '')) for col in emb_cols)
                blocks.append(block)

            contents = {}
            for name in qa_reg.split(',') if qa_reg else []:
                contents[name] = [str(item.get(name, '')) for item in valid_items]

            reference_content = [item.get('引用字段', {}) for item in valid_items]

            return blocks, [{}] * len(blocks), blocks, contents, reference_content

        except Exception as e:
            logger.error(f"Error processing JSON: {e}")
            return [], [], [], {}, []

    def _parse_image(self, main_temp):
        """处理图像类型文档"""
        try:
            # 简单的图像处理逻辑
            if isinstance(main_temp, str):
                return [main_temp], [{}], [main_temp], {}, [{}]
            else:
                return [], [], [], {}, []
        except Exception as e:
            logger.error(f"Error parsing image: {e}")
            return [], [], [], {}, []

    def _process_other(self, rule, main_temp):
        """处理其他类型文档(PDF等)"""
        try:
            # 使用rule_filter函数处理其他类型文档
            from app.utils.rule_filter import rule_filter

            blocks, datas, titles, contents = rule_filter(rule, root_node=main_temp)
            reference_content = [{}] * len(blocks)

            return blocks, datas, titles, contents, reference_content

        except ImportError:
            logger.warning("rule_filter not available, using fallback processing")
            # 简单的fallback处理
            if isinstance(main_temp, str):
                return [main_temp], [{}], [main_temp], {}, [{}]
            else:
                return [], [], [], {}, []
        except Exception as e:
            logger.error(f"Error processing other document type: {e}")
            return [], [], [], {}, []

    def _group_audit(self):
        """
        处理一组审核任务,包括并行处理文本块、整合知识库和索引结果 (基于原始逻辑)

        Returns:
            list: 包含审核结果的列表,每个元素包含:
                - kb_info: 知识库匹配信息
                - model_out: 模型输出结果
                - reference_links: 参考文档链接
                - maindoc_info: 主文档信息(位置、章节链等)
                - codoc_info: 协同文档信息
                - id: 文档ID列表
        """
        try:
            # 获取所需的输入数据
            blocks = self.kwargs.get('blocks', [])
            datas = self.kwargs.get('datas', [])
            titles = self.kwargs.get('titles', [])
            contents = self.kwargs.get('contents', [])
            co_docs = self.kwargs.get('co_docs', [])
            main_doc_url = self.kwargs.get('main_doc_url', '')
            co_titles = self.kwargs.get('co_titles', [])
            co_contents = self.kwargs.get('co_contents', [])
            co_datas = self.kwargs.get('co_datas', [])
            reference_content = self.kwargs.get('reference_content', [])

            if not blocks:
                logger.warning("No blocks to process")
                return []

            tmp_data = []
            outputs = []
            logger.info(f'Processing {len(blocks)} blocks')

            # 并行处理文本块
            import concurrent.futures

            for bg in range(0, len(blocks), self.num_threading):
                with concurrent.futures.ThreadPoolExecutor(min(self.num_threading, len(blocks) - bg)) as executor:
                    futures = {}
                    for i in range(bg, min(bg + self.num_threading, len(blocks))):
                        ref_content = reference_content[i] if i < len(reference_content) else {}
                        future = executor.submit(
                            self._recall_chat,
                            i,
                            blocks[i],
                            ref_content
                        )
                        futures[future] = [i, blocks[i], ref_content]

                    for future in concurrent.futures.as_completed(futures):
                        try:
                            result = future.result()
                            if result:
                                outputs.extend(result)
                        except Exception as e:
                            logger.error(f"Error in thread execution: {e}")
                            continue

            logger.info(f'Got {len(outputs)} outputs from processing')

            # 处理每个输出结果
            for output in outputs:
                try:
                    # 提取知识库和索引输出
                    kb_out = output.get('kb_out', {})
                    index_out = output.get('index_out', {})
                    kb_info = []
                    index_info = []

                    # 获取模型输出消息
                    message = self._get_model_output(kb_out, index_out, output)

                    # 获取参考ID和主文本
                    reference_id = output.get('id', 0)
                    main_text = self._get_main_text(contents, output, reference_id)

                    # 处理知识库输出
                    if kb_out:
                        kb_info = self._process_kb_output(kb_out)
                    logger.debug(f'kb_info: {len(kb_info)} items')

                    # 处理索引输出
                    if index_out:
                        index_info = self._process_index_output(
                            index_out, co_docs, co_titles, co_contents,
                            co_datas
                        )
                    logger.debug(f'index_info: {len(index_info)} items')

                    # 处理主文档位置信息
                    main_doc_pos = self._get_main_doc_position(
                        datas, reference_id, output, index_out, co_datas
                    )

                    # 构建最终结果
                    result = {
                        'kb_info': kb_info,
                        'model_out': message,
                        'reference_links': main_doc_url,
                        'maindoc_info': {
                            'link': main_doc_url,
                            'chapter_chain': [] if not datas or reference_id >= len(datas) or not datas[reference_id] else (
                                datas[reference_id].get('chapter_chain', []) + [titles[reference_id] if reference_id < len(titles) else '']),
                            'position': main_doc_pos,
                            'text': auto_text_clean(main_text) if main_text else ''
                        },
                        'codoc_info': index_info,
                        'id': [reference_id] + index_out.get('ids', []) if index_out else [reference_id],
                        'similarity': index_out.get('similarities', []) if index_out else []
                    }
                    tmp_data.append(result)

                except Exception as e:
                    logger.error(f"Error processing output result: {e}")
                    continue

            logger.info(f"Group audit completed with {len(tmp_data)} results")
            return tmp_data

        except Exception as e:
            logger.error(f"Error in group audit: {e}")
            return []

    def _get_model_output(self, kb_out, index_out, output):
        """获取模型输出结果"""
        if kb_out or index_out:
            return index_out.get('model_out') if index_out.get('model_out') else kb_out.get('model_out', '')
        return output.get('model_out', '')

    def _get_main_text(self, contents, output, reference_id):
        """获取主文本内容"""
        try:
            if isinstance(contents, dict):
                # 如果contents是字典，尝试从rules或其他字段获取
                rules = output.get('rules', [])
                if rules:
                    return '\n'.join(rules)
                # 从contents字典中获取
                for key, value in contents.items():
                    if isinstance(value, list) and reference_id < len(value):
                        return value[reference_id]
                return ''

            # 如果contents是列表
            if isinstance(contents, list) and reference_id < len(contents):
                content = contents[reference_id]
                # 处理图像数据
                if isinstance(content, str) and content.startswith("data:image/png;base64,"):
                    return content[:50]
                return str(content)

            return ''

        except Exception as e:
            logger.error(f"Error getting main text: {e}")
            return ''

    def _get_main_doc_position(self, datas, reference_id, output, index_out, co_datas):
        """处理主文档位置信息"""
        try:
            if not datas or reference_id >= len(datas) or not datas[reference_id]:
                return {}

            data = datas[reference_id]
            position = {}

            # 从data中提取位置信息
            if isinstance(data, dict):
                position.update({
                    'bbox': data.get('bbox', []),
                    'pno': data.get('pno', 0),
                    'block_id': data.get('block_id', ''),
                    'psize': data.get('psize', [])
                })

            return position

        except Exception as e:
            logger.error(f"Error getting main doc position: {e}")
            return {}

    def _process_without_rules(self, main_doc_type: DocType, main_temp: Any) -> List[Dict[str, Any]]:
        """
        Process document without specific traversal rules (fallback method).

        Args:
            main_doc_type: Type of main document
            main_temp: Main document data

        Returns:
            List[Dict[str, Any]]: Audit results
        """
        try:
            logger.info("Processing document without traversal rules")

            # Extract blocks using default method
            blocks, datas, titles, contents = self._extract_blocks_from_main(main_doc_type, main_temp)

            # Process each block
            results = []
            for i, block in enumerate(blocks):
                if not block or block == 'null':
                    block = contents[i] if i < len(contents) else ""

                # Perform audit on this block
                audit_result = self._audit_single_block(
                    block=block,
                    data=datas[i] if i < len(datas) else {},
                    title=titles[i] if i < len(titles) else "",
                    content=contents[i] if i < len(contents) else "",
                    block_index=i
                )

                if audit_result:
                    results.append(audit_result)

            logger.info(f"Default processing completed with {len(results)} results")
            return results

        except Exception as e:
            logger.error(f"Error in default processing: {e}")
            return []

    def _extract_blocks_from_main(self, main_doc_type: DocType, main_temp: Any) -> Tuple[List, List, List, List]:
        """
        Extract blocks from main document.

        Args:
            main_doc_type: Type of main document
            main_temp: Main document data

        Returns:
            Tuple[List, List, List, List]: (blocks, datas, titles, contents)
        """
        try:
            if main_doc_type == DocType.EXCEL:
                return self._process_excel_main(main_temp)
            elif main_doc_type == DocType.JSON:
                return self._process_json_main(main_temp)
            elif main_doc_type == DocType.IMAGE:
                return self._process_image_main(main_temp)
            else:
                return self._process_other_main(main_temp)

        except Exception as e:
            logger.error(f"Error extracting blocks from main document: {e}")
            return [], [], [], []

    def _process_excel_main(self, main_temp: Any) -> Tuple[List, List, List, List]:
        """Process Excel main document."""
        try:
            if hasattr(main_temp, 'to_string'):
                # pandas DataFrame
                blocks = [main_temp.to_string()]
                datas = [{}]
                titles = ["Excel Data"]
                contents = [main_temp.to_string()]
            else:
                blocks = [str(main_temp)]
                datas = [{}]
                titles = ["Excel Data"]
                contents = [str(main_temp)]

            return blocks, datas, titles, contents
        except Exception as e:
            logger.error(f"Error processing Excel main document: {e}")
            return [], [], [], []

    def _process_json_main(self, main_temp: Any) -> Tuple[List, List, List, List]:
        """Process JSON main document."""
        try:
            if isinstance(main_temp, str):
                data = json.loads(main_temp)
            else:
                data = main_temp

            blocks = [json.dumps(data, ensure_ascii=False)]
            datas = [data if isinstance(data, dict) else {}]
            titles = ["JSON Data"]
            contents = [json.dumps(data, ensure_ascii=False, indent=2)]

            return blocks, datas, titles, contents
        except Exception as e:
            logger.error(f"Error processing JSON main document: {e}")
            return [], [], [], []

    def _process_image_main(self, main_temp: Any) -> Tuple[List, List, List, List]:
        """Process Image main document."""
        try:
            if isinstance(main_temp, dict):
                blocks = main_temp.get('texts', [])
                datas = main_temp.get('data', [])
                titles = main_temp.get('title', [])
                contents = main_temp.get('content', [])
            else:
                blocks = [str(main_temp)]
                datas = [{}]
                titles = ["Image"]
                contents = [str(main_temp)]

            return blocks, datas, titles, contents
        except Exception as e:
            logger.error(f"Error processing Image main document: {e}")
            return [], [], [], []

    def _process_other_main(self, main_temp: Any) -> Tuple[List, List, List, List]:
        """Process other document types."""
        try:
            blocks = [str(main_temp)]
            datas = [{}]
            titles = ["Document"]
            contents = [str(main_temp)]

            return blocks, datas, titles, contents
        except Exception as e:
            logger.error(f"Error processing other main document: {e}")
            return [], [], [], []

    def _extract_blocks_from_main_with_rule(
        self,
        rule_obj: 'TraversalRuleConfig',
        main_doc_type: DocType,
        main_temp: Any
    ) -> Tuple[List, List, List, List, List]:
        """
        Extract blocks from main document using a specific traversal rule.

        This method implements the real extraction logic from the original audit service.

        Args:
            rule_obj: Traversal rule configuration object
            main_doc_type: Type of main document
            main_temp: Main document data

        Returns:
            Tuple[List, List, List, List]: (blocks, datas, titles, contents) filtered by rule
        """
        try:
            # Convert rule object to dict for compatibility with original logic
            rule = rule_obj.model_dump(by_alias=True, exclude_none=True)

            # Use the original extraction logic based on document type
            return self._extract_blocks(rule, main_doc_type, main_temp)

        except Exception as e:
            logger.error(f"Error extracting blocks with rule: {e}")
            return [], [], [], []

    def _extract_blocks(self, rule: Dict[str, Any], doc_type: DocType, main_temp: Any) -> Tuple[List, List, List, Union[List, Dict], List]:
        """
        Extract blocks from different types of documents based on rules.

        This is the core extraction method from the original audit service.

        Args:
            rule: Extraction rule configuration
            doc_type: Document type (EXCEL/JSON/PDF etc.)
            main_temp: Document content

        Returns:
            Tuple[List, List, List, List]: (texts, data, titles, contents)
        """
        try:
            # Get basic configuration
            qa_reg = rule.get('qa_reg', '')
            emb_reg = rule.get('emb_reg', '')

            # Route to appropriate handler based on document type
            if doc_type == DocType.EXCEL:
                return self._process_excel_blocks(main_temp, emb_reg, qa_reg)
            elif doc_type == 6:  # JSON type
                return self._process_json_blocks(main_temp, emb_reg, qa_reg)
            elif doc_type == DocType.IMAGE:
                # Handle both file path and mock data
                if isinstance(main_temp, str):
                    return parse_image(main_temp)
                elif isinstance(main_temp, dict):
                    # Handle mock data format
                    texts = main_temp.get('texts', [])
                    data = main_temp.get('data', [])
                    title = main_temp.get('title', [])
                    content = main_temp.get('content', [])
                    references = main_temp.get('references', [])
                    return texts, data, title, content, references
                else:
                    logger.error(f"Unsupported main_temp format for IMAGE type: {type(main_temp)}")
                    return [], [], [], [], []
            else:  # PDF and other documents
                return self._process_other_blocks(rule, main_temp)

        except Exception as e:
            logger.error(f"Error in _extract_blocks: {e}")
            return [], [], [], [], []

    def _get_qa_reg_list(self, qa_reg: str) -> List[str]:
        """Process qa_reg rules, return processed rule list."""
        qa_reg_list = qa_reg.split(',')

        # Handle special hierarchical title rules
        if len(qa_reg_list) == 1 and qa_reg_list[0] and qa_reg_list[0][0] in title_dict:
            floor = qa_reg_list[0][0]
            qa_reg_list = [floor + qa_reg[1:] for floor in title_dict[floor]]

        return qa_reg_list

    def _process_excel_blocks(self, main_temp: pd.DataFrame, emb_reg: str, qa_reg: str) -> Tuple[List, List, List, Dict, List]:
        """Process Excel type document blocks."""
        try:
            emb_cols = emb_reg.split(',') if emb_reg else []
            qa_reg_list = self._get_qa_reg_list(qa_reg)

            # Merge multiple columns to generate text blocks
            if emb_cols:
                blocks = [''.join(row) for row in zip(*[
                    main_temp[col].astype(str) for col in emb_cols if col in main_temp.columns
                ])]
            else:
                blocks = []

            # Extract QA related column content
            contents = {}
            for name in qa_reg.split(','):
                if name.strip() and name in main_temp.columns:
                    contents[name] = main_temp[name].astype(str).tolist()

            # Extract reference content
            reference_content = []
            if '引用字段' in main_temp.columns:
                reference_content = main_temp['reference_content'].astype(str).tolist()

            return blocks, [], blocks, contents, reference_content

        except Exception as e:
            logger.error(f"Error processing Excel blocks: {e}")
            return [], [], [], {}, []

    def _process_json_blocks(self, main_temp: List[Dict], emb_reg: str, qa_reg: str) -> Tuple[List, List, List, Dict, list]:
        """Process JSON type document blocks."""
        try:
            # Determine columns to extract
            emb_cols = emb_reg.split(',') if emb_reg else []
            qa_reg_list = self._get_qa_reg_list(qa_reg)
            if not emb_cols:
                emb_cols = qa_reg.split(',')

            required_cols = list(set(emb_cols + qa_reg_list))

            # Filter valid data
            valid_items = [
                item for item in main_temp
                if all(item.get(col) for col in required_cols)
            ]

            if not valid_items:
                return [], [], [], {}, []

            # Build return data
            blocks = [''.join(str(item.get(col, '')) for col in emb_cols)
                      for item in valid_items]

            contents = {}
            for name in qa_reg.split(','):
                if name.strip():
                    contents[name] = [str(item.get(name, '')) for item in valid_items]

            reference_content = [item.get('引用字段', {}) for item in valid_items]

            return blocks, [{} for _ in blocks], blocks, contents, reference_content

        except Exception as e:
            logger.error(f"Error processing JSON blocks: {e}")
            return [], [], [], {}, []

    def _process_other_blocks(self, rule: Dict[str, Any], main_temp: Any) -> Tuple[List, List, List, List, List]:
        """Process other document types (PDF etc.)."""
        try:
            # Use document tree filtering for structured documents
            if hasattr(main_temp, 'children'):  # DocTreeNode
                blocks, datas, titles, contents = rule_filter(rule, root_node=main_temp)
                return blocks, datas, titles, contents, [{} for _ in range(len(blocks))]
            else:
                # Fallback for unstructured content
                text_content = str(main_temp)
                return [text_content], [{}], ["Document"], [text_content], []

        except Exception as e:
            logger.error(f"Error processing other blocks: {e}")
            return [], [], [], [], []

    def _group_audit(self) -> List[Dict[str, Any]]:
        """
        Process a group of audit tasks, including parallel processing of text blocks,
        integrating knowledge bases and index results.

        Returns:
            list: List containing audit results, each element includes:
                - kb_info: Knowledge base matching information
                - model_out: Model output result
                - reference_links: Reference document links
                - maindoc_info: Main document information (position, chapter chain, etc.)
                - codoc_info: Collaborative document information
                - id: Document ID list
        """
        # Get required input data
        blocks = self.kwargs.get('blocks', [])
        datas = self.kwargs.get('datas', [])
        titles = self.kwargs.get('titles', [])
        contents = self.kwargs.get('contents', [])
        co_docs = self.kwargs.get('co_docs', [])
        main_doc_url = self.kwargs.get('main_doc_url', '')
        co_titles = self.kwargs.get('co_titles', [])
        co_contents = self.kwargs.get('co_contents', [])
        co_datas = self.kwargs.get('co_datas', [])
        reference_content = self.kwargs.get('reference_content', [])

        tmp_data = []
        outputs = []
        logger.info(f'Processing {len(blocks)} blocks')

        # Process text blocks in parallel
        for bg in range(0, len(blocks), self.num_threading):
            with concurrent.futures.ThreadPoolExecutor(min(self.num_threading, len(blocks) - bg)) as executor:
                futures = {
                    executor.submit(
                        self._recall_chat,
                        i,
                        blocks[i],
                        reference_content[i] if i < len(reference_content) else None
                    ): [i, blocks[i], reference_content[i] if i < len(reference_content) else None]
                    for i in range(bg, min(bg + self.num_threading, len(blocks)))
                }
                for future in concurrent.futures.as_completed(futures):
                    result = future.result()
                    if result:
                        outputs.extend(result)

        logger.info(f'Generated {len(outputs)} outputs')

        # Process each output result
        for output in outputs:
            try:
                # Extract knowledge base and index output
                kb_out = output.get('kb_out', {})
                index_out = output.get('index_out', {})
                kb_info = []
                index_info = []

                # Get model output message
                message = self._get_model_output(kb_out, index_out, output)

                # Get reference ID and main text
                reference_id = output.get('id', 0)
                main_text = self._get_main_text(contents, output, reference_id)

                # Process knowledge base output
                if kb_out:
                    kb_info = self._process_kb_output(kb_out)
                logger.debug(f'kb_info: {kb_info}')

                # Process index output
                if index_out:
                    index_info = self._process_index_output(
                        index_out, co_docs, co_titles, co_contents,
                        co_datas
                    )
                logger.debug(f'index_info: {index_info}')

                # Process main document position information
                main_doc_pos = self._get_main_doc_position(
                    datas, reference_id, output, index_out, co_datas
                )

                logger.debug(f'main_doc_pos: {main_doc_pos}')

                # Build final result
                result = {
                    'kb_info': kb_info,
                    'model_out': message,
                    'reference_links': main_doc_url,
                    'maindoc_info': {
                        'link': main_doc_url,
                        'chapter_chain': [] if not datas or reference_id >= len(datas) or not datas[reference_id] else (
                                datas[reference_id].get('chapter_chain', []) + [titles[reference_id] if reference_id < len(titles) else ""]),
                        'position': main_doc_pos,
                        'text': auto_text_clean(main_text),
                        'title': titles[reference_id] if reference_id < len(titles) else "",
                        'content': contents[reference_id] if reference_id < len(contents) else ""
                    },
                    'codoc_info': index_info,
                    'id': [reference_id] + index_out.get('ids', []),
                    'similarity': index_out.get('similarities', [])
                }
                tmp_data.append(result)

            except Exception as e:
                logger.error(f"Error processing output result: {e}")

        return tmp_data

    def _get_model_output(self, kb_out: Dict, index_out: Dict, output: Dict) -> str:
        """Get model output result."""
        if kb_out or index_out:
            return index_out.get('model_out') if index_out.get('model_out') else kb_out.get('model_out', '')
        return output.get('model_out', '')

    def _get_main_text(self, contents: Union[Dict, List], output: Dict, reference_id: int) -> str:
        """Get main text content."""
        try:
            if isinstance(contents, dict):
                rules = output.get('rules', [])
                if rules:
                    return '\n'.join(rules)
                else:
                    return '\n'.join([value[reference_id] for key, value in contents.items() if reference_id < len(value)])

            if reference_id < len(contents):
                content = contents[reference_id]
                if isinstance(content, str) and content.startswith("data:image/png;base64,"):
                    return content[:50]
                return str(content)

            return ""
        except Exception as e:
            logger.error(f"Error getting main text: {e}")
            return ""

    def _process_kb_output(self, kb_out: Dict) -> List[Dict]:
        """Process knowledge base output results."""
        try:
            kb_info = []
            origin_pos = []

            extra_info = kb_out.get('extra_info', [])
            for node in extra_info:
                if not node:
                    continue

                node_info = node.get('node_info', {}) if isinstance(node, dict) else {}
                bbox_list = node_info.get('bbox_list', [])
                block_ids = node_info.get('block_ids', [])
                pno_list = node_info.get('pno_list', [])
                psize = node_info.get('psize', [])

                for bbox, block_id, pno, ps in zip(bbox_list, block_ids, pno_list, psize):
                    origin_pos.append({
                        'bbox': bbox,
                        'pno': pno,
                        'block_id': block_id,
                        'psize': ps
                    })

            kb_info.append({
                'position_pos': origin_pos,
                'text': kb_out.get('text', ''),
                'link': kb_out.get('doc_id', '')
            })

            return kb_info
        except Exception as e:
            logger.error(f"Error processing KB output: {e}")
            return []

    def _process_index_output(self, index_out: Dict, co_docs: List, co_titles: List,
                            co_contents: List, co_datas: List) -> List[Dict]:
        """Process index output results."""
        try:
            index_info = []
            ids = index_out.get('ids', [])
            similarities = index_out.get('similarities', [])

            for i in range(len(ids)):
                if ids[i] == -1:
                    doc_paths = co_docs[i]['input']['doc_paths'] if i < len(co_docs) else ''
                    index_info.append({
                        'position': {},
                        'text': "无相关内容",
                        'link': doc_paths,
                        'chapter_chain': ["无相关内容"],
                        'similarity': similarities[i] if i < len(similarities) else 0
                    })
                    continue

                # Get original text
                if i < len(co_contents) and ids[i] < len(co_contents[i]):
                    origin_text = co_contents[i][ids[i]]
                    if origin_text.startswith("data:image/png;base64,"):
                        origin_text = origin_text[:50]
                else:
                    origin_text = ""

                # Get position information
                position = {}
                if co_datas and i < len(co_datas) and ids[i] < len(co_datas[i]):
                    position = self._get_position_info(co_datas[i][ids[i]])

                # Get title
                title = ""
                if i < len(co_titles) and ids[i] < len(co_titles[i]):
                    title = co_titles[i][ids[i]]

                # Get chapter chain
                chapter_chain = []
                if co_datas and i < len(co_datas) and ids[i] < len(co_datas[i]):
                    chapter_chain = co_datas[i][ids[i]].get('chapter_chain', []) + [title]

                # Get document link
                doc_link = ""
                if i < len(co_docs):
                    doc_link = co_docs[i]['input'].get('doc_paths', '')

                # Build index information
                index_info.append({
                    'position': position,
                    'text': f"{title}:{auto_text_clean(origin_text)}" if title else auto_text_clean(origin_text),
                    'link': doc_link,
                    'chapter_chain': chapter_chain,
                    'similarity': similarities[i] if i < len(similarities) else 0
                })

            return index_info
        except Exception as e:
            logger.error(f"Error processing index output: {e}")
            return []

    def _get_position_info(self, data: Dict) -> Dict:
        """Get document position information."""
        if not data:
            return {}

        if not (data.get('position') or data.get('text_position')):
            return {}

        position = {}
        # Process title position
        if data.get('position'):
            position["title_position"] = [{
                'bbox': list(pos[3]) if len(pos) > 3 else [],
                'pno': pos[0] if len(pos) > 0 else 0,
                'block_id': pos[5] if len(pos) > 5 else '',
                'psize': list(pos[4]) if len(pos) > 4 else []
            } for pos in data['position']]
        else:
            position["title_position"] = []

        # Process text position
        if data.get('text_position'):
            if isinstance(data['text_position'], str):
                position['text_position'] = data['text_position']
            else:
                position['text_position'] = [{
                    'bbox': list(pos[3]) if len(pos) > 3 else [],
                    'pno': pos[0] if len(pos) > 0 else 0,
                    'block_id': pos[5] if len(pos) > 5 else '',
                    'psize': list(pos[4]) if len(pos) > 4 else []
                } for pos in data['text_position']]
        else:
            position["text_position"] = []

        return position

    def _get_main_doc_position(self, datas: List, reference_id: int, output: Dict,
                              index_out: Dict, co_datas: List) -> Union[Dict, List]:
        """
        Get main document position information.

        Args:
            datas: Main document data
            reference_id: Main document reference ID
            output: Output data containing rule information
            index_out: Index output data
            co_datas: Collaborative document data

        Returns:
            dict or list: Main document position information
        """
        try:
            if not datas or reference_id >= len(datas) or not datas[reference_id]:
                return {}

            if not output.get('rules'):
                main_doc_pos = {}
                data_item = datas[reference_id]

                main_doc_pos['title_position'] = [{
                    'bbox': list(pos[3]) if len(pos) > 3 else [],
                    'pno': pos[0] if len(pos) > 0 else 0,
                    'block_id': pos[5] if len(pos) > 5 else '',
                    'psize': list(pos[4]) if len(pos) > 4 else []
                } for pos in data_item.get('position', [])]

                main_doc_pos['text_position'] = [{
                    'bbox': list(pos[3]) if len(pos) > 3 else [],
                    'pno': pos[0] if len(pos) > 0 else 0,
                    'block_id': pos[5] if len(pos) > 5 else '',
                    'psize': list(pos[4]) if len(pos) > 4 else []
                } for pos in data_item.get('text_position', [])]

                return main_doc_pos

            # Process rule-based position information
            main_doc_pos = []
            rules_ids = index_out.get('rules_ids', [])

            for i, rule_ids in enumerate(rules_ids):
                if not rule_ids or i >= len(co_datas):
                    continue

                for rule_id in rule_ids:
                    if rule_id >= len(co_datas[i]):
                        continue

                    data_item = co_datas[i][rule_id]
                    if data_item.get('position') or data_item.get('text_position'):
                        pos = {}
                        pos["title_position"] = [{
                            'bbox': list(p[3]) if len(p) > 3 else [],
                            'pno': p[0] if len(p) > 0 else 0,
                            'block_id': p[5] if len(p) > 5 else '',
                            'psize': list(p[4]) if len(p) > 4 else []
                        } for p in data_item.get('position', [])]

                        pos["text_position"] = [{
                            'bbox': list(p[3]) if len(p) > 3 else [],
                            'pno': p[0] if len(p) > 0 else 0,
                            'block_id': p[5] if len(p) > 5 else '',
                            'psize': list(p[4]) if len(p) > 4 else []
                        } for p in data_item.get('text_position', [])]

                        main_doc_pos.append(pos)

            return main_doc_pos

        except Exception as e:
            logger.error(f"Error getting main doc position: {e}")
            return {}

    def _recall_chat(self, index_i: int, text: str, reference_content: Dict = None) -> List[Dict]:
        """
        Execute recall chat operation by searching indices and knowledge bases.

        Args:
            index_i (int): Current document index being processed
            text (str): Text content to search
            reference_content (dict, optional): Reference content for formatting, default None

        Returns:
            list: List containing search results and model outputs
        """
        if text == 'nan' or not text:
            return []

        try:
            # Extract parameters from kwargs
            kbs_address = self.kwargs.get('kbs_address')
            main_doc_type = self.kwargs.get('main_doc_type')
            emb_reg = self.kwargs.get('emb_reg')
            qa_reg = self.kwargs.get('qa_reg')
            blocks = self.kwargs.get('blocks', [])
            titles = self.kwargs.get('titles', [])
            contents = self.kwargs.get('contents', [])
            summary_model = self.kwargs.get('summary_model', 'default')
            co_titles = self.kwargs.get('co_titles', [])
            co_contents = self.kwargs.get('co_contents', [])
            temperature = self.kwargs.get('temperature', 0.7)
            index_list = self.kwargs.get('co_index', [])
            kbs = self.kwargs.get('co_kbs', [])
            co_blocks = self.kwargs.get('co_blocks', [])

            tmp_output = []
            temp_index_list = []
            temp_kb_list = []
            rules = []
            rules_ids = []

            # Handle case where all indices are None
            if not index_list or all(index_list[i] is None for i in range(len(index_list))):
                temp_index_list = [[] for _ in range(len(index_list) if index_list else 0)]
                index_list = []

            # Process index-based search if indices exist
            if index_list:
                for i in range(len(index_list)):
                    index = index_list[i]
                    if not index:
                        temp_index_list.append([])
                        continue

                    # Process reference content if provided
                    if reference_content:
                        if qa_reg == "标题":
                            reference_blocks = co_titles
                        elif qa_reg == "内容":
                            reference_blocks = co_contents
                        else:
                            reference_blocks = [[f"{a}:{b}" for a, b in zip(co_title, co_content)]
                                              for co_title, co_content in zip(co_titles, co_contents)]

                        rule, rule_id = self._process_reference_content(index, reference_content, reference_blocks, i)
                        rules.append(rule)
                        rules_ids.append(rule_id)
                    else:
                        rules.append(None)
                        rules_ids.append(None)

                    # Perform index search
                    ids = self._perform_index_search(index, text, co_blocks, i)
                    temp_index_list.append(ids)

            logger.debug(f"temp_index_list length: {len(temp_index_list)}")

            # Handle case where all knowledge bases are None
            if not kbs or all(kbs[i] is None for i in range(len(kbs))):
                temp_kb_list = [[] for _ in range(len(kbs) if kbs else 0)]
                kbs = []

            # Process knowledge base search if KBs exist
            if kbs:
                for kb in kbs:
                    if not kb:
                        temp_kb_list.append([])
                        continue
                    nodes = self._query_knowledge_base(kb, text, kbs_address)
                    temp_kb_list.append(nodes)

            # Process top-k results and prepare documents for chat completion
            for k in range(self.top_k):
                doc_dict = {}
                images = []

                # Handle case with no index list
                if not temp_index_list:
                    if index_i < len(contents) and contents[index_i].startswith("data:image/png;base64,"):
                        images.append(contents[index_i])
                    else:
                        if index_i < len(titles) and index_i < len(contents):
                            doc_dict['main_doc'] = Doc_(标题=titles[index_i], 内容=contents[index_i])

                # Process each index group
                for kk in range(len(temp_index_list)):
                    if len(temp_index_list[kk]) <= k:
                        break

                    doc_dict_part, image = self._format_document_dict(
                        main_doc_type, index_i, titles, contents, rules, kk, k,
                        reference_content, emb_reg, temp_index_list, temp_kb_list,
                        co_titles, co_contents
                    )
                    doc_dict.update(doc_dict_part)
                    if image is not None:
                        images.append(image)

                if not doc_dict:
                    continue

                logger.debug(f'Document dictionary: {doc_dict}')

                # Generate prompt and get model response using config service
                prompt = self._process_prompt(self.config_service.get_prompt_config(), doc_dict)

                try:
                    newmessage = get_chat_completion(summary_model, prompt, temperature, images=images)
                    newmessage = extract_json(newmessage)
                except Exception as e:
                    logger.error(f'Chat completion or JSON parsing error: {e}')
                    continue

                # Prepare knowledge base output
                kb_out = {}
                if kbs:
                    kb_out = {
                        'main_doc': text,
                        'model_out': newmessage,
                        'extra_info': [nodes[k] if nodes and k < len(nodes) else None for nodes in temp_kb_list],
                        'id': index_i,
                        'text': [nodes[k]['text'] if nodes and k < len(nodes) else None for nodes in temp_kb_list],
                        'doc_id': [nodes[k]['extra_info']['来源文档标题'] if nodes and k < len(nodes) else None for nodes in temp_kb_list]
                    }

                # Prepare index-based output
                index_out = {}
                if index_list:
                    logger.debug(f'Index list results: {temp_index_list}')
                    index_out = {
                        'ids': [int(temp_index[k][0]) if temp_index and k < len(temp_index) else -1 for temp_index in temp_index_list],
                        'model_out': newmessage,
                        'id': index_i,
                        'rules': rules,
                        'rules_ids': rules_ids,
                        'similarities': [temp_index[k][1] if temp_index and k < len(temp_index) else -1 for temp_index in temp_index_list]
                    }
                    logger.debug(f'Index output: {index_out}')

                # Format rules if reference content exists
                formatted_rules = []
                if reference_content and isinstance(contents, dict):
                    formatted_rules = [
                        v[index_i].replace('{{', '{').replace('}}', '}').format(**rules[kk])
                        if rules and kk < len(rules) and rules[kk] and any([r_k in v[index_i] for r_k in reference_content.keys()])
                        else v[index_i]
                        for k, v in contents.items()
                        if index_i < len(v)
                    ]

                # Combine all results
                tmp_output.append({
                    'kb_out': kb_out,
                    'index_out': index_out,
                    'model_out': newmessage,
                    'id': index_i,
                    'rules': formatted_rules
                })
                logger.debug(f'Temporary output: {tmp_output[-1]}')

            return tmp_output

        except Exception as e:
            logger.error(f"Error in _recall_chat: {e}")
            return []

    def _process_reference_content(self, index, reference_content: Dict, reference_blocks: List, i: int) -> Tuple[Dict, List]:
        """
        Process reference content and generate rules.

        Args:
            index: Current index being used
            reference_content: Reference content dictionary
            reference_blocks: Reference document blocks
            i: Current index position

        Returns:
            tuple: (rule dictionary, rule ID list)
        """
        try:
            rule = {}
            rule_id = []

            for idx, (k, v) in enumerate(reference_content.items()):
                if idx < len(reference_blocks) and reference_blocks[idx]:
                    # Use placeholder embedding search
                    # TODO: Implement actual index search
                    embedding = get_embedding([v])
                    # Placeholder search - in real implementation would use index.search
                    ids = [0]  # Placeholder

                    if ids and ids[0] < len(reference_blocks[idx]):
                        rule[k] = reference_blocks[idx][ids[0]]
                        rule_id.append(ids[0])
                    else:
                        rule[k] = ""
                        rule_id.append(0)

            return rule, rule_id
        except Exception as e:
            logger.error(f"Error processing reference content: {e}")
            return {}, []

    def _perform_index_search(self, index, text: str, co_blocks: List, i: int) -> List[Tuple[int, int]]:
        """
        Execute index search and perform reranking.

        Args:
            index: Search index
            text: Search text
            co_blocks: Collaborative document blocks
            i: Current index position

        Returns:
            list: List of (ID, similarity) tuples
        """
        try:
            # TODO: Implement actual FAISS index search
            # This is a placeholder implementation
            search_size = self.top_k * 6 if self.rerank else self.top_k

            # Placeholder search results
            if i < len(co_blocks) and co_blocks[i]:
                num_blocks = len(co_blocks[i])
                # Generate placeholder similarities
                similarities = [0.8, 0.7, 0.6, 0.5, 0.4][:min(search_size, num_blocks)]
                ids = list(range(min(search_size, num_blocks)))

                # Apply threshold filtering
                filtered = [(int(id_val), int(sim * 100))
                           for id_val, sim in zip(ids, similarities)
                           if self.low <= sim * 100 <= self.high]

                if self.rerank and len(filtered) > 1:
                    logger.info(f"Search text: {text}")
                    logger.info(f'Pre-rerank IDs: {[f[0] for f in filtered]}, similarities: {[f[1] for f in filtered]}')
                    try:
                        rerank_ids = rerank(text, [co_blocks[i][f[0]] for f in filtered], self.top_k)
                        filtered = [filtered[idx] for idx in rerank_ids if idx < len(filtered)]
                        logger.info(f'Post-rerank results: {filtered}')
                    except Exception as e:
                        logger.info(f'Reranking error: {e}')

                logger.info(f"Search successful: {filtered}")
                return filtered if filtered else [(-1, 0)]
            else:
                return [(-1, 0)]

        except Exception as e:
            logger.error(f"Error in index search: {e}")
            return [(-1, 0)]

    def _query_knowledge_base(self, kb: str, text: str, kbs_address: str) -> List[Dict]:
        """
        Query knowledge base.

        Args:
            kb: Knowledge base name
            text: Query text
            kbs_address: Knowledge base address

        Returns:
            list: Knowledge base node list
        """
        try:
            # TODO: Implement actual knowledge base query
            # This is a placeholder implementation
            logger.info(f"Querying KB: {kb} with text: {text}")

            # Placeholder response
            nodes = [{
                'text': f"Knowledge base result for: {text}",
                'extra_info': {
                    '来源文档标题': f"Document from {kb}",
                    'relevance_score': 0.8
                },
                'node_info': {
                    'bbox_list': [],
                    'block_ids': [],
                    'pno_list': [],
                    'psize': []
                }
            }]

            return nodes[:self.top_k]

        except Exception as e:
            logger.error(f'Knowledge base query error: {e}')
            return []

    def _format_document_dict(self, main_doc_type: DocType, index_i: int, titles: List,
                            contents: Union[List, Dict], rules: List, kk: int, k: int,
                            reference_content: Dict, emb_reg: str, temp_index_list: List,
                            temp_kb_list: List, co_titles: List, co_contents: List) -> Tuple[Dict, str]:
        """
        Format document dictionary for model input.

        Returns:
            tuple: (document dictionary, image data if any)
        """
        try:
            doc_dict = {}
            image = None

            if main_doc_type in [DocType.EXCEL, 6]:  # Excel or JSON
                if isinstance(contents, dict) and index_i < len(titles):
                    kwgs = {emb_reg: titles[index_i]}

                    for k_name, v_list in contents.items():
                        if index_i < len(v_list):
                            content_val = v_list[index_i]
                            if reference_content and rules and kk < len(rules) and rules[kk]:
                                # Format with reference content
                                if any(r_k in content_val for r_k in reference_content.keys()):
                                    content_val = content_val.replace('{{', '{').replace('}}', '}').format(**rules[kk])
                            kwgs[k_name] = content_val

                    doc_dict['main_doc'] = Doc_(**kwgs)
                else:
                    doc_dict['main_doc'] = Doc_(标题=titles[index_i] if index_i < len(titles) else "",
                                              内容=str(contents))
            else:
                # Other document types
                doc_dict['main_doc'] = Doc_(
                    标题=titles[index_i] if index_i < len(titles) else "",
                    内容=contents[index_i] if isinstance(contents, list) and index_i < len(contents) else str(contents)
                )

            # Add collaborative document information
            if kk < len(temp_index_list) and k < len(temp_index_list[kk]):
                if not temp_index_list[kk] and not (kk < len(temp_kb_list) and temp_kb_list[kk]):
                    doc_dict[f'co_docs{kk}'] = Doc_(标题='', 内容='')
                else:
                    if temp_index_list[kk] and k < len(temp_index_list[kk]):
                        idx_info = temp_index_list[kk][k]
                        if idx_info[0] == -1:
                            doc_dict[f'co_docs{kk}'] = Doc_(标题="无相关内容", 内容="无相关内容")
                        else:
                            co_idx = idx_info[0]
                            if (kk < len(co_contents) and co_idx < len(co_contents[kk]) and
                                not co_contents[kk][co_idx].startswith("data:image/png;base64,")):
                                doc_dict[f'co_docs{kk}'] = Doc_(
                                    标题=co_titles[kk][co_idx] if kk < len(co_titles) and co_idx < len(co_titles[kk]) else "",
                                    内容=co_contents[kk][co_idx]
                                )
                            else:
                                if kk < len(co_contents) and co_idx < len(co_contents[kk]):
                                    image = co_contents[kk][co_idx]
                    elif kk < len(temp_kb_list) and temp_kb_list[kk] and k < len(temp_kb_list[kk]):
                        kb_info = temp_kb_list[kk][k]
                        doc_dict[f'kb{kk}'] = Doc_(
                            标题=kb_info.get('extra_info', {}).get('来源文档标题', ''),
                            内容=kb_info.get('text', '')
                        )

            return doc_dict, image

        except Exception as e:
            logger.error(f"Error formatting document dict: {e}")
            return {}, None

    def _process_prompt(self, conf: Dict, docs: Dict) -> str:
        """
        Process and format the audit prompt.

        Args:
            conf: Prompt configuration
            docs: Document context

        Returns:
            str: Formatted prompt
        """
        try:
            return DocumentAuditPrompt.create_audit_prompt(conf, docs)
        except Exception as e:
            logger.error(f"Error processing prompt: {e}")
            return f"Error generating prompt: {str(e)}"

    def _audit_single_block(
        self,
        block: str,
        data: Dict,
        title: str,
        content: str,
        block_index: int
    ) -> Optional[Dict[str, Any]]:
        """
        Audit a single block.

        Args:
            block: Block text
            data: Block data
            title: Block title
            content: Block content
            block_index: Index of the block

        Returns:
            Optional[Dict[str, Any]]: Audit result or None
        """
        try:
            # Simulate audit processing
            # In the real implementation, this would involve:
            # 1. Semantic matching with co-documents
            # 2. Knowledge base queries
            # 3. LLM-based validation
            # 4. Rule-based checking

            audit_result = {
                'id': [block_index, 0],  # [main_doc_index, co_doc_index]
                'model_out': f'审核通过: {title}',
                'kb_info': [],
                'reference_links': [],
                'maindoc_info': {
                    'position': block_index,
                    'title': title,
                    'content': content[:200] + '...' if len(content) > 200 else content
                },
                'codoc_info': []
            }

            # Add some mock validation logic
            if len(block.strip()) < 10:
                audit_result['model_out'] = f'审核未通过: 内容过短 - {title}'

            return audit_result

        except Exception as e:
            logger.error(f"Error auditing block {block_index}: {e}")
            return None




class AuditService:
    """
    高级审计服务

    提供审计服务的高级接口，封装了完整的审计处理流程。
    负责协调各个组件（配置服务、文档处理器、审计处理器）
    完成端到端的审计任务。

    主要功能：
    - 同步审计处理
    - 配置解析和验证
    - 文档处理和分析
    - 结果格式化和输出
    """

    def __init__(self):
        """初始化审计服务"""
        self.settings = settings

    def process_audit_sync(
        self,
        config_yaml: str,
        main_doc_json: Optional[str] = None,
        task_name: str = "sync_audit"
    ) -> Dict[str, Any]:
        """
        同步处理审计任务

        执行完整的同步审计流程，包括配置解析、文档处理、
        审计执行和结果格式化。适用于需要立即获得结果的场景。

        处理步骤：
        1. 解析和验证配置
        2. 初始化文档处理器
        3. 处理主文档和协同文档
        4. 执行审计规则
        5. 格式化输出结果

        Args:
            config_yaml: YAML格式的审计配置字符串
            main_doc_json: 主文档JSON字符串（可选）
            task_name: 任务名称，用于标识和日志记录

        Returns:
            Dict[str, Any]: 审计结果字典，包含结果数据、执行时间等信息

        Raises:
            Exception: 当审计处理过程中发生错误时抛出
        """
        try:
            start_time = datetime.now()
            logger.info(f"Starting synchronous audit: {task_name}")

            # Initialize configuration using new config service
            config_service = AuditConfigurationService(config_yaml, settings.temp_dir, task_name)

            # Process documents
            doc_processor = DocumentProcessor(config_service)

            # Extract main document path from config if available
            main_doc_path = config_service.get_raw_config().get('main_doc', {}).get('input', {}).get('doc_path')

            # Process main document
            main_doc, main_doc_type, main_temp = doc_processor.process_main_doc(
                main_doc_path, main_doc_json
            )

            # Process co-documents
            co_docs, co_blocks, co_datas, co_titles, co_contents, co_kbs = doc_processor.process_co_docs()

            co_index = build_index(co_blocks)

            # Build audit arguments using config service
            audit_args = {
                'main_doc_type': main_doc_type,
                'co_blocks': co_blocks,
                'co_contents': co_contents,
                'co_datas': co_datas,
                'co_titles': co_titles,
                'co_kbs': co_kbs,
                'co_index': co_index,
                'main_temp': main_temp,
                'temperature': config_service.get_temperature(),
                'summary_model': config_service.get_summary_model(),
                'global_kbs': config_service.get_knowledge_base_graphs(),
                'kbs_address': config_service.get_knowledge_base_address(),
                'main_doc_url': main_doc_path,
                'co_docs': co_docs
            }

            # Process audit using config service
            processor = AuditProcessor(config_service=config_service, num_threading=4, **audit_args)
            results = processor.audit_walk()

            # Process output using config service
            output_results = self.process_output(config_service.get_output_config(), results)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.info(f"Synchronous audit completed in {duration:.2f} seconds")

            return {
                'results': output_results,
                'duration': duration,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat()
            }

        except Exception as e:
            logger.error(f"Error in synchronous audit processing: {e}")
            raise

    def process_output(self, conf: Dict, data: List[Dict]) -> List[Dict]:
        """
        Process and filter audit output.

        Args:
            conf: Output configuration
            data: Raw audit results

        Returns:
            List[Dict]: Processed results
        """
        try:
            # 安全地处理配置值，避免None值
            filter_value = conf.get('filter', 'none')
            filter_type = str(filter_value).lower() if filter_value is not None else 'none'

            sort_value = conf.get('out_doc', 'main')
            sort_way = str(sort_value) if sort_value is not None else 'main'

            # Apply filters
            if filter_type == 'true_value':
                filtered_data = [item for item in data if '审核通过' in item.get('model_out', '')]
            elif filter_type == 'false_value':
                filtered_data = [item for item in data if '审核通过' not in item.get('model_out', '')]
            else:
                filtered_data = data

            # Apply sorting
            if sort_way == 'main':
                filtered_data = sorted(filtered_data, key=lambda x: x.get('id', [0, 0])[0])
            elif sort_way == 'codoc':
                filtered_data = sorted(filtered_data, key=lambda x: x.get('id', [0, 0])[1])

            return filtered_data

        except Exception as e:
            logger.error(f"Error processing output: {e}")
            return data

    def send_callback(
        self,
        callback_url: str,
        task_id: str,
        task_name: str,
        message: str,
        data: Dict[str, Any] = None,
        status: str = 'false'
    ) -> bool:
        """
        Send callback to external service.

        Args:
            callback_url: URL to send callback to
            task_id: Task identifier
            task_name: Task name
            message: Status message
            data: Result data
            status: Success status

        Returns:
            bool: True if callback sent successfully
        """
        try:
            if not callback_url:
                return True

            body = {
                'task_id': task_id,
                'task_name': task_name,
                'body': json.dumps(data or {}, ensure_ascii=False),
                'success': status,
                'message': message
            }

            logger.info(f"Sending callback to {callback_url}")
            http_service = get_http_service()
            response = http_service.post(callback_url, json_data=body, timeout=30)

            logger.info(f"Callback sent successfully for task {task_id}")
            return True

        except HTTPServiceError as e:
            logger.error(f"HTTP error sending callback for task {task_id}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error sending callback for task {task_id}: {e}")
            return False
