# Services module

# Import only HTTP service by default to avoid database dependencies
from .http_service import HTTPService, get_http_service

__all__ = [
    'HTTPService',
    'get_http_service'
]

# Other services can be imported explicitly when needed:
# from .audit_service import AuditService
# from .document_service import DocumentProcessor, AuditConfig
# from .task_service import TaskService
