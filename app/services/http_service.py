"""
HTTP service for making HTTP requests with unified configuration, error handling, and retry logic.
"""
import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional, Union, List
from urllib.parse import urljoin, urlparse

import httpx
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from app.core.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class HTTPServiceError(Exception):
    """Base exception for HTTP service errors."""
    pass


class HTTPTimeoutError(HTTPServiceError):
    """HTTP timeout error."""
    pass


class HTTPRetryExhaustedError(HTTPServiceError):
    """HTTP retry exhausted error."""
    pass


class HTTPService:
    """
    Unified HTTP service for making HTTP requests with retry logic,
    error handling, and logging.
    """

    def __init__(self):
        self.settings = settings
        self._session = None
        self._async_client = None
        self._setup_session()

    def _setup_session(self):
        """Setup requests session with retry strategy and connection pooling."""
        self._session = requests.Session()

        # Setup retry strategy
        retry_strategy = Retry(
            total=self.settings.http_max_retries,
            backoff_factor=self.settings.http_retry_backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "PUT", "DELETE", "OPTIONS", "TRACE", "POST"]
        )

        # Setup HTTP adapter with retry strategy
        adapter = HTTPAdapter(
            pool_connections=self.settings.http_pool_connections,
            pool_maxsize=self.settings.http_pool_maxsize,
            max_retries=retry_strategy
        )

        self._session.mount("http://", adapter)
        self._session.mount("https://", adapter)

        # Set default headers (don't set Content-Type globally as it interferes with file uploads)
        self._session.headers.update({
            'User-Agent': self.settings.http_user_agent,
            'Accept': 'application/json'
        })

    def _get_async_client(self) -> httpx.AsyncClient:
        """Get or create async HTTP client."""
        if self._async_client is None:
            timeout = httpx.Timeout(self.settings.http_timeout)
            limits = httpx.Limits(
                max_connections=self.settings.http_pool_connections,
                max_keepalive_connections=self.settings.http_pool_maxsize
            )

            self._async_client = httpx.AsyncClient(
                timeout=timeout,
                limits=limits,
                headers={
                    'User-Agent': self.settings.http_user_agent,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            )

        return self._async_client

    def _log_request(self, method: str, url: str, **kwargs):
        """Log HTTP request details with structured logging."""
        request_data = {
            "http_method": method.upper(),
            "url": url,
            "type": "http_request_start"
        }

        if kwargs.get('params'):
            request_data["query_params"] = kwargs['params']

        if kwargs.get('json') and logger.isEnabledFor(logging.DEBUG):
            request_data["request_body"] = json.dumps(kwargs['json'], ensure_ascii=False)[:500]

        logger.info("HTTP request started", extra=request_data)

    def _log_response(self, response: Union[requests.Response, httpx.Response], duration: float):
        """Log HTTP response details with structured logging."""
        response_data = {
            "status_code": response.status_code,
            "duration_ms": round(duration * 1000, 2),
            "type": "http_request_complete"
        }

        if response.status_code >= 400:
            response_data["response_body"] = response.text[:500]
            logger.warning("HTTP request failed", extra=response_data)
        else:
            logger.info("HTTP request completed", extra=response_data)

    def request(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        data: Optional[Union[str, bytes, Dict[str, Any]]] = None,
        files: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        auth: Optional[tuple] = None,
        allow_redirects: bool = True,
        **kwargs
    ) -> requests.Response:
        """
        Make synchronous HTTP request.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE, etc.)
            url: Request URL
            params: URL parameters
            json_data: JSON data to send in request body
            data: Raw data to send in request body
            files: Files to upload (for multipart/form-data)
            headers: Additional headers
            timeout: Request timeout in seconds
            auth: Authentication tuple (username, password)
            allow_redirects: Whether to allow redirects
            **kwargs: Additional arguments passed to requests

        Returns:
            requests.Response: HTTP response

        Raises:
            HTTPServiceError: For HTTP-related errors
            HTTPTimeoutError: For timeout errors
            HTTPRetryExhaustedError: When all retries are exhausted
        """
        start_time = time.time()

        # Prepare request arguments
        request_kwargs = {
            'params': params,
            'timeout': timeout or self.settings.http_timeout,
            'auth': auth,
            'allow_redirects': allow_redirects,
            **kwargs
        }

        # Handle headers - special handling for file uploads
        if files is not None:
            # For file uploads, don't set Content-Type header
            # Let requests handle multipart/form-data automatically
            request_headers = (headers or {}).copy()
            # Remove Content-Type if it's set to application/json
            if request_headers.get('Content-Type') == 'application/json':
                del request_headers['Content-Type']
            request_kwargs['headers'] = request_headers
            request_kwargs['files'] = files
        else:
            # For non-file requests, set Content-Type for JSON requests
            request_headers = (headers or {}).copy()
            if json_data is not None and 'Content-Type' not in request_headers:
                request_headers['Content-Type'] = 'application/json'
            request_kwargs['headers'] = request_headers

        # Handle request body
        if json_data is not None:
            request_kwargs['json'] = json_data
        elif data is not None:
            request_kwargs['data'] = data

        # Log request
        self._log_request(method, url, **request_kwargs)

        try:
            response = self._session.request(method, url, **request_kwargs)
            duration = time.time() - start_time

            # Log response
            self._log_response(response, duration)

            # Raise for HTTP errors
            response.raise_for_status()

            return response

        except requests.exceptions.Timeout as e:
            duration = time.time() - start_time
            logger.error(f"HTTP timeout after {duration:.3f}s: {url}")
            raise HTTPTimeoutError(f"Request timeout: {str(e)}") from e

        except requests.exceptions.RetryError as e:
            duration = time.time() - start_time
            logger.error(f"HTTP retry exhausted after {duration:.3f}s: {url}")
            raise HTTPRetryExhaustedError(f"Retry exhausted: {str(e)}") from e

        except requests.exceptions.RequestException as e:
            duration = time.time() - start_time
            logger.error(f"HTTP request error after {duration:.3f}s: {url} - {str(e)}")
            raise HTTPServiceError(f"Request failed: {str(e)}") from e

    def get(self, url: str, **kwargs) -> requests.Response:
        """Make GET request."""
        return self.request('GET', url, **kwargs)

    def post(self, url: str, **kwargs) -> requests.Response:
        """Make POST request."""
        return self.request('POST', url, **kwargs)

    def put(self, url: str, **kwargs) -> requests.Response:
        """Make PUT request."""
        return self.request('PUT', url, **kwargs)

    def delete(self, url: str, **kwargs) -> requests.Response:
        """Make DELETE request."""
        return self.request('DELETE', url, **kwargs)

    def patch(self, url: str, **kwargs) -> requests.Response:
        """Make PATCH request."""
        return self.request('PATCH', url, **kwargs)

    def head(self, url: str, **kwargs) -> requests.Response:
        """Make HEAD request."""
        return self.request('HEAD', url, **kwargs)

    def options(self, url: str, **kwargs) -> requests.Response:
        """Make OPTIONS request."""
        return self.request('OPTIONS', url, **kwargs)

    async def async_request(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        data: Optional[Union[str, bytes, Dict[str, Any]]] = None,
        files: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        auth: Optional[tuple] = None,
        follow_redirects: bool = True,
        **kwargs
    ) -> httpx.Response:
        """
        Make asynchronous HTTP request.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE, etc.)
            url: Request URL
            params: URL parameters
            json_data: JSON data to send in request body
            data: Raw data to send in request body
            files: Files to upload (for multipart/form-data)
            headers: Additional headers
            timeout: Request timeout in seconds
            auth: Authentication tuple (username, password)
            follow_redirects: Whether to follow redirects
            **kwargs: Additional arguments passed to httpx

        Returns:
            httpx.Response: HTTP response

        Raises:
            HTTPServiceError: For HTTP-related errors
            HTTPTimeoutError: For timeout errors
        """
        start_time = time.time()
        client = self._get_async_client()

        # Prepare request arguments
        request_kwargs = {
            'params': params,
            'timeout': timeout or self.settings.http_timeout,
            'auth': auth,
            'follow_redirects': follow_redirects,
            **kwargs
        }

        # Handle headers - special handling for file uploads
        if files is not None:
            # For file uploads, don't set Content-Type header
            request_headers = (headers or {}).copy()
            if request_headers.get('Content-Type') == 'application/json':
                del request_headers['Content-Type']
            request_kwargs['headers'] = request_headers
            request_kwargs['files'] = files
        else:
            # For non-file requests, set Content-Type for JSON requests
            request_headers = (headers or {}).copy()
            if json_data is not None and 'Content-Type' not in request_headers:
                request_headers['Content-Type'] = 'application/json'
            request_kwargs['headers'] = request_headers

        # Handle request body
        if json_data is not None:
            request_kwargs['json'] = json_data
        elif data is not None:
            request_kwargs['data'] = data

        # Log request
        self._log_request(method, url, **request_kwargs)

        try:
            response = await client.request(method, url, **request_kwargs)
            duration = time.time() - start_time

            # Log response
            self._log_response(response, duration)

            # Raise for HTTP errors
            response.raise_for_status()

            return response

        except httpx.TimeoutException as e:
            duration = time.time() - start_time
            logger.error(f"HTTP async timeout after {duration:.3f}s: {url}")
            raise HTTPTimeoutError(f"Async request timeout: {str(e)}") from e

        except httpx.RequestError as e:
            duration = time.time() - start_time
            logger.error(f"HTTP async request error after {duration:.3f}s: {url} - {str(e)}")
            raise HTTPServiceError(f"Async request failed: {str(e)}") from e

    async def async_get(self, url: str, **kwargs) -> httpx.Response:
        """Make async GET request."""
        return await self.async_request('GET', url, **kwargs)

    async def async_post(self, url: str, **kwargs) -> httpx.Response:
        """Make async POST request."""
        return await self.async_request('POST', url, **kwargs)

    async def async_put(self, url: str, **kwargs) -> httpx.Response:
        """Make async PUT request."""
        return await self.async_request('PUT', url, **kwargs)

    async def async_delete(self, url: str, **kwargs) -> httpx.Response:
        """Make async DELETE request."""
        return await self.async_request('DELETE', url, **kwargs)

    async def async_patch(self, url: str, **kwargs) -> httpx.Response:
        """Make async PATCH request."""
        return await self.async_request('PATCH', url, **kwargs)

    async def async_head(self, url: str, **kwargs) -> httpx.Response:
        """Make async HEAD request."""
        return await self.async_request('HEAD', url, **kwargs)

    async def async_options(self, url: str, **kwargs) -> httpx.Response:
        """Make async OPTIONS request."""
        return await self.async_request('OPTIONS', url, **kwargs)

    def get_json(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Make GET request and return JSON response.

        Args:
            url: Request URL
            **kwargs: Additional arguments passed to get()

        Returns:
            Dict[str, Any]: JSON response data

        Raises:
            HTTPServiceError: For HTTP-related errors
            ValueError: If response is not valid JSON
        """
        response = self.get(url, **kwargs)
        try:
            return response.json()
        except ValueError as e:
            logger.error(f"Invalid JSON response from {url}: {response.text[:200]}")
            raise ValueError(f"Invalid JSON response: {str(e)}") from e

    def post_json(self, url: str, json_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Make POST request with JSON data and return JSON response.

        Args:
            url: Request URL
            json_data: JSON data to send
            **kwargs: Additional arguments passed to post()

        Returns:
            Dict[str, Any]: JSON response data

        Raises:
            HTTPServiceError: For HTTP-related errors
            ValueError: If response is not valid JSON
        """
        response = self.post(url, json_data=json_data, **kwargs)
        try:
            return response.json()
        except ValueError as e:
            logger.error(f"Invalid JSON response from {url}: {response.text[:200]}")
            raise ValueError(f"Invalid JSON response: {str(e)}") from e

    async def async_get_json(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Make async GET request and return JSON response.

        Args:
            url: Request URL
            **kwargs: Additional arguments passed to async_get()

        Returns:
            Dict[str, Any]: JSON response data

        Raises:
            HTTPServiceError: For HTTP-related errors
            ValueError: If response is not valid JSON
        """
        response = await self.async_get(url, **kwargs)
        try:
            return response.json()
        except ValueError as e:
            logger.error(f"Invalid JSON response from {url}: {response.text[:200]}")
            raise ValueError(f"Invalid JSON response: {str(e)}") from e

    async def async_post_json(self, url: str, json_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Make async POST request with JSON data and return JSON response.

        Args:
            url: Request URL
            json_data: JSON data to send
            **kwargs: Additional arguments passed to async_post()

        Returns:
            Dict[str, Any]: JSON response data

        Raises:
            HTTPServiceError: For HTTP-related errors
            ValueError: If response is not valid JSON
        """
        response = await self.async_post(url, json_data=json_data, **kwargs)
        try:
            return response.json()
        except ValueError as e:
            logger.error(f"Invalid JSON response from {url}: {response.text[:200]}")
            raise ValueError(f"Invalid JSON response: {str(e)}") from e

    def upload_file(self, url: str, files: Dict[str, Any], **kwargs) -> requests.Response:
        """
        Upload files using multipart/form-data.

        Args:
            url: Upload URL
            files: Dictionary of files to upload
                  Format: {'field_name': file_object} or
                         {'field_name': (filename, file_object, content_type)}
            **kwargs: Additional arguments passed to post()

        Returns:
            requests.Response: HTTP response

        Raises:
            HTTPServiceError: For HTTP-related errors
        """
        logger.info(f"Uploading files to {url}")
        logger.debug(f"Files: {list(files.keys())}")

        # Remove Content-Type from headers if present to let requests handle it
        headers = kwargs.get('headers', {}).copy()
        if 'Content-Type' in headers:
            del headers['Content-Type']
        kwargs['headers'] = headers

        return self.post(url, files=files, **kwargs)

    def download_file(self, url: str, file_path: str, **kwargs) -> bool:
        """
        Download file from URL and save to local path.

        Args:
            url: File URL
            file_path: Local file path to save
            **kwargs: Additional arguments passed to get()

        Returns:
            bool: True if download successful

        Raises:
            HTTPServiceError: For HTTP-related errors
        """
        try:
            response = self.get(url, **kwargs)

            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            logger.info(f"File downloaded successfully: {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error downloading file from {url}: {str(e)}")
            raise HTTPServiceError(f"Download failed: {str(e)}") from e

    def close(self):
        """Close HTTP clients and clean up resources."""
        if self._session:
            self._session.close()

        if self._async_client:
            asyncio.create_task(self._async_client.aclose())

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._async_client:
            await self._async_client.aclose()


# Global HTTP service instance
_http_service = None


def get_http_service() -> HTTPService:
    """
    Get global HTTP service instance.

    Returns:
        HTTPService: Global HTTP service instance
    """
    global _http_service
    if _http_service is None:
        _http_service = HTTPService()
    return _http_service
