"""
Configuration management for the audit service.
"""
import os
from typing import Optional, List
try:
    from pydantic_settings import BaseSettings
    from pydantic import field_validator
except ImportError:
    # Fallback for older pydantic versions
    from pydantic_settings import BaseSettings
from pydantic import field_validator


class Settings(BaseSettings):
    """Application settings."""

    # Application
    app_name: str = "Audit Service"
    app_version: str = "1.0.0"
    debug: bool = False
    log_level: str = "INFO"

    # Database
    database_url: str
    db_host: str = "localhost"
    db_port: int = 3306
    db_name: str = "audit_db"
    db_user: str = "audit_user"
    db_password: str = "audit_password"

    # Redis
    redis_url: str = "redis://localhost:6379/0"
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None

    # Remote Tree URL
    remote_tree_url: str = "http://**************:36032/idp_tree"

    # Celery
    celery_broker_url: str = "redis://localhost:6379/0"
    celery_result_backend: str = "redis://localhost:6379/0"
    celery_task_serializer: str = "json"
    celery_result_serializer: str = "json"
    celery_accept_content: List[str] = ["json"]
    celery_timezone: str = "Asia/Shanghai"
    celery_enable_utc: bool = False

    # File Storage
    upload_dir: str = "./uploads"
    temp_dir: str = "./temp"
    max_file_size: str = "100MB"

    # External Services
    embedding_api_url: str = "http://***********:8000/v1"
    embedding_api_key: str = "EMPTY"
    embedding_model: str = "bge-m3"

    # LLM Service Configuration
    llm_api_url: str = "https://api.openai.com/v1"
    llm_api_key: str = "EMPTY"
    llm_default_model: str = "gpt-3.5-turbo"

    # Knowledge Base Service
    kbs_address: str = "http://***********:9159/query"

    # OCR Configuration
    ocr_type: str = "paddle"
    use_ocr: bool = False

    # Model Configuration
    relevance_model: str = "qwen2-7b-8k-simple_tasks_v1"
    summary_model: str = "Qwen2-72B-Instruct-GPTQ-Int4"
    temperature: float = 1e-8

    # Security
    secret_key: str = "your-secret-key-here"
    access_token_expire_minutes: int = 30

    # Monitoring
    enable_metrics: bool = True
    metrics_port: int = 8001

    # HTTP Service Configuration
    http_timeout: int = 30
    http_max_retries: int = 3
    http_retry_delay: float = 1.0
    http_retry_backoff_factor: float = 2.0
    http_pool_connections: int = 10
    http_pool_maxsize: int = 20
    http_user_agent: str = "AuditService/1.0"

    # ELK Configuration
    elk_enabled: bool = True
    elasticsearch_host: str = "localhost"
    elasticsearch_port: int = 9200
    elasticsearch_index: str = "audit-service-logs"
    logstash_host: str = "localhost"
    logstash_port: int = 5044
    kibana_host: str = "localhost"
    kibana_port: int = 5601
    log_format: str = "json"  # json or text
    log_file_path: str = "./logs/audit-service.log"

    @field_validator("max_file_size")
    @classmethod
    def parse_file_size(cls, v):
        """Parse file size string to bytes."""
        if isinstance(v, str):
            v = v.upper()
            if v.endswith("MB"):
                return int(v[:-2]) * 1024 * 1024
            elif v.endswith("GB"):
                return int(v[:-2]) * 1024 * 1024 * 1024
            elif v.endswith("KB"):
                return int(v[:-2]) * 1024
        return int(v)

    @field_validator("celery_accept_content", mode="before")
    @classmethod
    def parse_celery_accept_content(cls, v):
        """Parse celery accept content."""
        if isinstance(v, str):
            return [item.strip().strip('"\'') for item in v.strip("[]").split(",")]
        return v

    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings
