"""
Database configuration and session management.
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from typing import Generator
import logging

from .config import get_settings

settings = get_settings()

# Create database engine
engine = create_engine(
    settings.database_url,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=settings.debug,
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create declarative base
Base = declarative_base()


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话

    FastAPI依赖注入函数，提供数据库会话管理。
    自动处理会话的创建、提交、回滚和关闭。

    使用方式：
        @app.get("/endpoint")
        def endpoint(db: Session = Depends(get_db)):
            # 使用db进行数据库操作

    Yields:
        Session: SQLAlchemy数据库会话实例

    Raises:
        Exception: 数据库操作异常时自动回滚
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logging.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def create_tables():
    """
    创建所有数据库表

    根据模型定义创建数据库表结构，如果表已存在则不做任何操作。
    在应用启动时调用以确保数据库结构正确。
    """
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """
    删除所有数据库表

    警告：此操作会删除所有数据，仅用于开发和测试环境。
    """
    Base.metadata.drop_all(bind=engine)
