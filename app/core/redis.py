"""
Redis configuration and client management.
"""
import redis
from typing import Optional
import logging

from .config import get_settings

settings = get_settings()


class RedisClient:
    """Redis client singleton."""
    
    _instance: Optional[redis.Redis] = None
    
    @classmethod
    def get_client(cls) -> redis.Redis:
        """
        Get Redis client instance.
        
        Returns:
            redis.Redis: Redis client
        """
        if cls._instance is None:
            try:
                cls._instance = redis.Redis.from_url(
                    settings.redis_url,
                    decode_responses=True,
                    socket_timeout=5,
                    socket_connect_timeout=5,
                    socket_keepalive=True,
                    health_check_interval=30,
                )
                # Test connection
                cls._instance.ping()
                logging.info("Redis connection established")
            except redis.ConnectionError as e:
                logging.error(f"Redis connection error: {e}")
                raise
        
        return cls._instance


def get_redis() -> redis.Redis:
    """
    Get Redis client.
    
    Returns:
        redis.Redis: Redis client
    """
    return RedisClient.get_client()
