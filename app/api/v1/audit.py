"""
Audit API endpoints.
"""
import logging
import time
import uuid
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.schemas import (
    AuditRequest, AuditResponse, TaskStatusResponse, ErrorResponse, TaskStatus
)
# TaskStatus constants
TASK_PENDING = "pending"
TASK_PROCESSING = "processing"
TASK_COMPLETED = "completed"
TASK_FAILED = "failed"
from app.services.task_service import TaskService
from app.services.audit_service import AuditService
from app.tasks.celery_tasks import process_audit_async

router = APIRouter()

# 使用增强的审计日志记录器
from app.utils.audit_logger import get_audit_logger, log_performance, log_audit_event
logger = get_audit_logger(__name__, service="audit_api", component="routes")


@router.post("/audit/async", response_model=AuditResponse, status_code=status.HTTP_202_ACCEPTED)
@log_performance("async_audit_submission")
@log_audit_event("AUDIT_ASYNC_SUBMIT", "提交异步审计任务")
async def audit_async(
    request: AuditRequest,
    db: Session = Depends(get_db)
):
    """
    提交异步审计请求

    将审计任务提交到Celery队列进行异步处理，适用于大型文档
    或复杂审计规则的场景。任务提交后立即返回任务ID，客户端
    可通过任务ID查询处理状态和结果。

    处理流程：
    1. 验证请求数据
    2. 创建任务记录
    3. 提交到Celery队列
    4. 返回任务ID

    Args:
        request: 审计请求数据，包含任务名称、配置和文档信息
        db: 数据库会话依赖注入

    Returns:
        AuditResponse: 任务提交响应，包含任务ID和状态

    Raises:
        HTTPException: 当任务提交失败时抛出500错误
    """
    # 生成请求ID用于跟踪
    request_id = str(uuid.uuid4())
    logger.set_request_id(request_id)

    start_time = time.time()

    try:
        # 记录请求开始
        logger.business_log("audit", "async_submit", "开始处理异步审计请求",
                          task_name=request.task_name,
                          has_main_doc=bool(request.main_doc),
                          has_callback=bool(request.callback_url))

        task_service = TaskService(db)

        # Create task record
        with logger.operation_context("create_task_record"):
            task_id = task_service.create_task(
                task_name=request.task_name,
                config=request.config,
                main_doc=request.main_doc
            )
            logger.set_task_id(task_id)

        # Submit to Celery
        with logger.operation_context("submit_celery_task"):
            process_audit_async.delay(
                task_id=task_id,
                task_name=request.task_name,
                config_yaml=request.config,
                main_doc_json=request.main_doc,
                callback_url=request.callback_url
            )

        # 记录成功提交
        duration = time.time() - start_time
        logger.audit_event("AUDIT_TASK_SUBMITTED", "异步审计任务提交成功",
                          task_id=task_id,
                          task_name=request.task_name,
                          submission_duration_ms=round(duration * 1000, 2))

        return AuditResponse(
            task_id=task_id,
            status=TaskStatus.PENDING,
            message="任务已提交处理"
        )

    except Exception as e:
        duration = time.time() - start_time
        logger.error_with_traceback("提交异步审计任务失败", e,
                                  task_name=request.task_name,
                                  submission_duration_ms=round(duration * 1000, 2),
                                  error_category="task_submission")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"提交审核任务出错: {str(e)}"
        )


@router.post("/audit/sync", response_model=AuditResponse)
async def audit_sync(
    request: AuditRequest,
    db: Session = Depends(get_db)
):
    """
    同步处理审计请求

    立即处理审计任务并返回结果，适用于小型文档或简单
    审计规则的场景。处理过程中客户端需要等待，直到
    审计完成或超时。

    处理流程：
    1. 验证请求数据
    2. 创建任务记录
    3. 同步执行审计处理
    4. 更新任务状态
    5. 返回审计结果

    Args:
        request: 审计请求数据，包含任务名称、配置和文档信息
        db: 数据库会话依赖注入

    Returns:
        AuditResponse: 审计结果响应，包含完整的审计数据

    Raises:
        HTTPException: 当审计处理失败时抛出500错误
    """
    try:
        task_service = TaskService(db)
        audit_service = AuditService()

        # Create task record
        task_id = task_service.create_task(
            task_name=request.task_name,
            config=request.config,
            main_doc=request.main_doc
        )

        # Update status to processing
        task_service.update_task_status(task_id, TASK_PROCESSING)

        try:
            # Process audit synchronously
            result = audit_service.process_audit_sync(
                config_yaml=request.config,
                main_doc_json=request.main_doc,
                task_name=request.task_name
            )

            # Update status to completed
            task_service.update_task_status(
                task_id,
                TASK_COMPLETED,
                result=result
            )

            logger.info(f"Completed sync audit task {task_id}")

            return AuditResponse(
                task_id=task_id,
                status=TaskStatus.COMPLETED,
                message="审核成功",
                data=result
            )

        except Exception as e:
            # Update status to failed
            task_service.update_task_status(
                task_id,
                TASK_FAILED,
                exception_info=str(e)
            )
            raise

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in sync audit processing: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"审核出错: {str(e)}"
        )


@router.get("/audit/status/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(
    task_id: str,
    db: Session = Depends(get_db)
):
    """
    查询审计任务状态

    根据任务ID查询审计任务的当前状态、处理结果和相关信息。
    支持查询异步和同步任务的状态。

    任务状态包括：
    - pending: 等待处理
    - processing: 正在处理
    - completed: 处理完成
    - failed: 处理失败

    Args:
        task_id: 任务唯一标识符
        db: 数据库会话依赖注入

    Returns:
        TaskStatusResponse: 任务状态信息，包含状态、结果、时间等

    Raises:
        HTTPException: 当任务不存在时抛出404错误
    """
    try:
        task_service = TaskService(db)
        task_status = task_service.get_task_status(task_id)

        if not task_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Task {task_id} not found"
            )

        return task_status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务状态出错: {str(e)}"
        )


@router.delete("/audit/task/{task_id}")
async def cancel_task(
    task_id: str,
    db: Session = Depends(get_db)
):
    """
    Cancel audit task (if still pending).

    Args:
        task_id: Task identifier
        db: Database session

    Returns:
        dict: Cancellation result
    """
    try:
        task_service = TaskService(db)
        task_status = task_service.get_task_status(task_id)

        if not task_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Task {task_id} not found"
            )

        if task_status.status != TASK_PENDING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot cancel task in {task_status.status} status"
            )

        # Update status to failed (cancelled)
        task_service.update_task_status(
            task_id,
            TASK_FAILED,
            exception_info="Task cancelled by user"
        )

        # Clean up task data
        task_service.delete_task_data(task_id)

        logger.info(f"Cancelled task {task_id}")

        return {"message": f"Task {task_id} cancelled successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消任务出错: {str(e)}"
        )
