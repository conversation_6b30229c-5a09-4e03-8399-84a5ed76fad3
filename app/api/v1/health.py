"""
健康检查端点

提供服务健康状态检查功能，用于监控系统各组件的运行状态。
包括数据库连接、Redis缓存、Celery任务队列等关键依赖的健康检查。
"""
from datetime import datetime
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.core.config import get_settings
from app.core.database import get_db
from app.core.redis import get_redis
from app.models.schemas import HealthResponse

router = APIRouter()
settings = get_settings()


@router.get("/health", response_model=HealthResponse)
async def health_check(db: Session = Depends(get_db)):
    """
    服务健康检查端点

    检查审计服务及其依赖组件的健康状态，包括：
    - 数据库连接状态（MySQL）
    - 缓存服务状态（Redis）
    - 任务队列状态（Celery）

    健康状态分类：
    - healthy: 所有关键服务正常
    - degraded: 部分非关键服务异常
    - unhealthy: 关键服务异常

    Args:
        db: 数据库会话依赖注入

    Returns:
        HealthResponse: 服务健康状态响应，包含各组件状态详情
    """
    dependencies = {}

    # Check database
    try:
        db.execute(text("SELECT 1"))
        dependencies["database"] = "healthy"
    except Exception as e:
        dependencies["database"] = f"unhealthy: {str(e)}"

    # Check Redis
    try:
        redis_client = get_redis()
        # Use sync ping for health check
        if hasattr(redis_client, 'ping'):
            redis_client.ping()
            dependencies["redis"] = "healthy"
        else:
            dependencies["redis"] = "healthy"  # Assume healthy if no ping method
    except Exception as e:
        dependencies["redis"] = f"unhealthy: {str(e)}"

    # Check Celery (basic check) - make it optional
    try:
        from app.tasks.celery_tasks import celery_app
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        if stats:
            dependencies["celery"] = "healthy"
        else:
            dependencies["celery"] = "no workers available"
    except ImportError:
        dependencies["celery"] = "not configured"
    except Exception as e:
        dependencies["celery"] = f"unhealthy: {str(e)}"

    # Determine overall status - only require database to be healthy
    critical_services = ["database"]
    overall_status = "healthy" if all(
        dependencies.get(service) == "healthy" for service in critical_services
    ) else "degraded"

    return HealthResponse(
        status=overall_status,
        timestamp=datetime.now(),
        version=settings.app_version,
        dependencies=dependencies
    )
