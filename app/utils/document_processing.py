"""
Document processing utilities for audit service.

This module contains utilities for processing different document types,
file conversion, and document structure analysis.
"""

import os
import logging
import base64
import json
import re
import pandas as pd
from typing import Tuple, List, Dict, Any, Optional, Union
from io import BytesIO
from PIL import Image
from decimal import Decimal, InvalidOperation

from app.utils.doc_type import DocType


def convert_to_pdf(word_path: str, output_dir: str) -> str:
    """
    Convert Word document to PDF.

    TODO: Implement Word to PDF conversion using:
    - python-docx2pdf
    - LibreOffice command line
    - Microsoft Office COM (Windows)
    - Online conversion services

    Args:
        word_path (str): Path to Word document
        output_dir (str): Directory to save PDF

    Returns:
        str: Path to converted PDF file

    Raises:
        NotImplementedError: This function needs to be implemented
    """
    logging.warning("convert_to_pdf is not implemented - using placeholder")

    # TODO: Implement actual conversion logic
    # For now, return the original path with .pdf extension
    base_name = os.path.splitext(os.path.basename(word_path))[0]
    pdf_path = os.path.join(output_dir, f"{base_name}.pdf")

    # Placeholder - copy the file (this won't work for real conversion)
    logging.error(f"Word to PDF conversion not implemented. Would convert {word_path} to {pdf_path}")

    raise NotImplementedError("Word to PDF conversion needs to be implemented")


def parse_excel(doc_path: str, rule: dict) -> Tuple[List[str], List[Dict], List[str], List[str]]:
    """
    Parse Excel document according to specified rules.

    Args:
        doc_path (str): Path to Excel file
        rule (dict): Parsing rules containing level and other parameters

    Returns:
        Tuple[List[str], List[Dict], List[str], List[str]]:
        (texts, data, titles, contents)
    """
    try:
        level = rule.get('level', 0)

        if level == -4:
            # Process by sheets
            xls = pd.ExcelFile(doc_path)
            sheet_names = xls.sheet_names
            texts = []
            content = []

            for sheet in sheet_names:
                # Force all columns to be read as strings
                df = xls.parse(sheet, dtype=str, na_filter=False)
                df = df.fillna("")

                # Calculate decimal places mode for each column
                col_decimal_places = {}
                for col in df.columns:
                    decimal_places_list = []
                    for val in df[col]:
                        val_str = str(val).replace(',', '')
                        if re.match(r'^-?\d+(\.\d+)?$', val_str):
                            if '.' in val_str:
                                decimal_places_list.append(len(val_str.split('.')[-1]))
                            else:
                                decimal_places_list.append(0)

                    if decimal_places_list:
                        col_decimal_places[col] = max(set(decimal_places_list), key=decimal_places_list.count)
                    else:
                        col_decimal_places[col] = 2

                # Format numbers with thousand separators
                def format_thousand(x, decimal_places):
                    if x is None:
                        return ""
                    x_str = str(x)
                    try:
                        d = Decimal(x_str.replace(',', ''))
                        int_part = x_str.replace(',', '').split('.')[0].lstrip('-')
                        if len(int_part) >= 4:
                            fmt = "{:,." + str(decimal_places) + "f}"
                            return fmt.format(d)
                    except InvalidOperation:
                        pass
                    return x_str

                for col in df.columns:
                    dp = col_decimal_places.get(col, 2)
                    df[col] = df[col].apply(lambda x: format_thousand(x, dp))

                markdown_text = df.to_markdown(index=False)
                texts.append(markdown_text)
                content.append(markdown_text)

            data = [{"text_position": sn} for sn in sheet_names]
            title = sheet_names
            return texts, data, title, content
        else:
            return [], [], [], []

    except Exception as e:
        logging.error(f"Error parsing Excel file {doc_path}: {e}")
        return [], [], [], []


def parse_image(doc_path: str) -> Tuple[List[str], List[Dict], List[str], List[str], List[Dict]]:
    """
    Parse image file and convert to base64 format.

    Args:
        doc_path (str): Path to image file

    Returns:
        Tuple containing texts, data, titles, contents, and references
    """
    try:
        image = Image.open(doc_path).convert("RGB")
        buffer = BytesIO()
        image.save(buffer, format="PNG")
        base64_code = base64.b64encode(buffer.getvalue()).decode("utf-8")

        base64_image = f"data:image/png;base64,{base64_code}"

        texts = [base64_image]
        content = [base64_image]
        data = [{}]
        title = [os.path.basename(doc_path)]
        references = [{}]

        return texts, data, title, content, references

    except Exception as e:
        logging.error(f"Error parsing image file {doc_path}: {e}")
        return [], [], [], [], []


def process_document(doc_path: str, parse_save_path: str, rule: dict,
                    use_ocr: bool = False, ocr_type: str = "paddle") -> Tuple[List, List, List, List, Optional[str]]:
    """
    Process document based on its type and extraction rules.

    TODO: Implement full document processing including:
    - PDF processing with OCR
    - Word document handling
    - Advanced text extraction
    - Structure analysis

    Args:
        doc_path (str): Path to document
        parse_save_path (str): Path to save parsing results
        rule (dict): Processing rules
        use_ocr (bool): Whether to use OCR
        ocr_type (str): Type of OCR to use

    Returns:
        Tuple containing texts, data, titles, contents, and error message
    """
    try:
        doc_type = DocType.from_doc_path(doc_path)

        if doc_type in [DocType.WORD, DocType.PDF]:
            if doc_type == DocType.WORD:
                # Convert Word to PDF first
                word_path = doc_path
                if doc_path.endswith('.docx'):
                    doc_path = doc_path.replace('.docx', '.pdf')
                elif doc_path.endswith('.doc'):
                    doc_path = doc_path.replace('.doc', '.pdf')

                try:
                    convert_to_pdf(word_path, os.path.dirname(doc_path))
                    logging.info(f'Word converted to PDF: {doc_path}')
                except NotImplementedError:
                    logging.error("Word to PDF conversion not available")
                    return [], [], [], [], "Word to PDF conversion not implemented"

            # TODO: Implement PDF processing with document tree generation
            logging.warning("PDF processing with document tree not implemented")
            return [], [], [], [], "PDF processing not fully implemented"

        elif doc_type == DocType.EXCEL:
            return parse_excel(doc_path, rule)

        elif doc_type == DocType.IMAGE:
            texts, data, title, content, _ = parse_image(doc_path)
            return texts, data, title, content, None

        else:
            return [], [], [], [], f"Unsupported document type: {doc_type}"

    except Exception as e:
        logging.error(f"Error processing document {doc_path}: {e}")
        return [], [], [], [], f"Document processing error: {str(e)}"


def load_data(doc_type: DocType, doc_temp: Any, parse_save_path: str = None,
              use_ocr: bool = False, ocr_type: str = "paddle") -> Tuple[bool, Any]:
    """
    Load and process document data based on type.

    TODO: Implement comprehensive data loading including:
    - Excel file processing
    - JSON data handling
    - PDF document tree generation
    - Error handling and validation

    Args:
        doc_type (DocType): Type of document
        doc_temp: Document data or path
        parse_save_path (str): Path for saving parsing results
        use_ocr (bool): Whether to use OCR
        ocr_type (str): OCR type to use

    Returns:
        Tuple[bool, Any]: Success flag and processed data
    """
    try:
        if doc_type == DocType.EXCEL:
            if isinstance(doc_temp, str):
                res = pd.read_excel(doc_temp).fillna(method='ffill')
            else:
                res = doc_temp

        elif doc_type == 6:  # JSON type
            if isinstance(doc_temp, str):
                res = json.loads(doc_temp)
            else:
                res = doc_temp

        else:
            if doc_type in [DocType.WORD, DocType.PDF]:
                if doc_type == DocType.WORD:
                    word_path = doc_temp
                    if doc_temp.endswith('.docx'):
                        doc_temp = doc_temp.replace('.docx', '.pdf')
                    elif doc_temp.endswith('.doc'):
                        doc_temp = doc_temp.replace('.doc', '.pdf')

                    try:
                        convert_to_pdf(word_path, os.path.dirname(word_path))
                    except NotImplementedError:
                        return False, "Word to PDF conversion not implemented"

                # TODO: Implement document tree generation
                logging.warning("Document tree generation not implemented")
                return False, "Document tree generation not implemented"
            else:
                res = doc_temp

        return True, res

    except Exception as e:
        logging.error(f"Error loading data: {e}")
        return False, f"Data loading error: {str(e)}"


class Doc_:
    """
    Simple document wrapper class for dynamic attribute access.
    """

    def __init__(self, **kwargs) -> None:
        """Initialize with dynamic attributes from kwargs."""
        for key, value in kwargs.items():
            setattr(self, key, value)

    def __repr__(self) -> str:
        attrs = ', '.join(f"{k}={v!r}" for k, v in self.__dict__.items())
        return f"Doc_({attrs})"


# TODO: Add more document processing utilities:
# - Document structure analysis
# - Content extraction strategies
# - Format conversion utilities
# - Metadata extraction
# - Document validation functions
