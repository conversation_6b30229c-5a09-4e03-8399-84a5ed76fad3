"""
ELK (Elasticsearch, Logstash, Kibana) logging configuration for the audit service.
"""
import json
import logging
import logging.handlers
import socket
import sys
import traceback
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, Any, Optional

from app.core.config import get_settings

settings = get_settings()


class BeijingTimeFormatter(logging.Formatter):
    """
    使用北京时间的格式化器
    """

    def formatTime(self, record, datefmt=None):
        """
        格式化时间为北京时间
        """
        beijing_tz = timezone(timedelta(hours=8))
        ct = datetime.fromtimestamp(record.created, tz=beijing_tz)
        if datefmt:
            s = ct.strftime(datefmt)
        else:
            s = ct.strftime('%Y-%m-%d %H:%M:%S')
            s = f"{s},{int(record.msecs):03d}"
        return s


class JSONFormatter(logging.Formatter):
    """
    增强的JSON格式化器，用于结构化日志记录

    提供完整的上下文信息和标准化字段，便于ELK系统解析和分析。
    """

    def __init__(self, service_name: str = "audit-service"):
        super().__init__()
        self.service_name = service_name
        self.hostname = socket.gethostname()

    def format(self, record: logging.LogRecord) -> str:
        """
        将日志记录格式化为JSON格式

        Args:
            record: 日志记录对象

        Returns:
            str: JSON格式的日志字符串
        """
        # 基础日志数据
        # 使用北京时间 (UTC+8)
        beijing_tz = timezone(timedelta(hours=8))
        beijing_time = datetime.fromtimestamp(record.created, tz=beijing_tz)
        log_data = {
            "@timestamp": beijing_time.isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "service": self.service_name,
            "hostname": self.hostname,
            "thread": record.thread,
            "thread_name": record.threadName,
            "process": record.process,
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "file": record.filename,
            "path": record.pathname,
            "environment": settings.environment if hasattr(settings, 'environment') else "development",
            "version": settings.app_version if hasattr(settings, 'app_version') else "1.0.0"
        }
        
        # 添加异常信息（如果存在）
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": traceback.format_exception(*record.exc_info)
            }

        # 添加业务上下文字段
        business_fields = {}
        standard_fields = {
            'name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 'filename',
            'module', 'exc_info', 'exc_text', 'stack_info', 'lineno', 'funcName',
            'created', 'msecs', 'relativeCreated', 'thread', 'threadName',
            'processName', 'process', 'message'
        }

        for key, value in record.__dict__.items():
            if key not in standard_fields:
                business_fields[key] = value

        # 分类业务字段
        if business_fields:
            # 任务相关字段
            task_fields = {}
            for key in ['task_id', 'task_name', 'task_status', 'task_type']:
                if key in business_fields:
                    task_fields[key] = business_fields.pop(key)
            if task_fields:
                log_data["task"] = task_fields

            # 用户相关字段
            user_fields = {}
            for key in ['user_id', 'username', 'user_role']:
                if key in business_fields:
                    user_fields[key] = business_fields.pop(key)
            if user_fields:
                log_data["user"] = user_fields

            # HTTP请求相关字段
            http_fields = {}
            for key in ['request_id', 'http_method', 'url', 'status_code', 'duration_ms', 'client_ip']:
                if key in business_fields:
                    http_fields[key] = business_fields.pop(key)
            if http_fields:
                log_data["http"] = http_fields

            # 性能相关字段
            performance_fields = {}
            for key in ['duration_ms', 'memory_usage', 'cpu_usage', 'response_time']:
                if key in business_fields:
                    performance_fields[key] = business_fields.pop(key)
            if performance_fields:
                log_data["performance"] = performance_fields

            # 其他额外字段
            if business_fields:
                log_data["extra"] = business_fields

        return json.dumps(log_data, ensure_ascii=False, default=str)


class LogstashHandler(logging.handlers.SocketHandler):
    """Custom handler to send logs to Logstash."""
    
    def __init__(self, host: str, port: int):
        super().__init__(host, port)
        self.formatter = JSONFormatter()
    
    def emit(self, record: logging.LogRecord):
        """Emit a log record to Logstash."""
        try:
            msg = self.format(record)
            self.send(msg.encode('utf-8') + b'\n')
        except Exception:
            self.handleError(record)


class ElasticsearchHandler(logging.Handler):
    """Custom handler to send logs directly to Elasticsearch."""
    
    def __init__(self, host: str, port: int, index: str):
        super().__init__()
        self.host = host
        self.port = port
        self.index = index
        self.formatter = JSONFormatter()
        
        # Try to import elasticsearch
        try:
            from elasticsearch import Elasticsearch
            self.es = Elasticsearch([f"http://{host}:{port}"])
        except ImportError:
            self.es = None
            print("Warning: elasticsearch package not installed. Install with: pip install elasticsearch")
    
    def emit(self, record: logging.LogRecord):
        """Emit a log record to Elasticsearch."""
        if not self.es:
            return
        
        try:
            log_data = json.loads(self.format(record))
            
            # Create index with date suffix
            date_suffix = datetime.now().strftime("%Y.%m.%d")
            index_name = f"{self.index}-{date_suffix}"
            
            self.es.index(
                index=index_name,
                body=log_data
            )
        except Exception as e:
            print(f"Error sending log to Elasticsearch: {e}")
            self.handleError(record)


def setup_elk_logging(
    service_name: str = "audit-service",
    log_level: str = "INFO",
    enable_console: bool = True,
    enable_file: bool = True,
    enable_logstash: bool = True,
    enable_elasticsearch: bool = False,
    logger_name: str = ""
) -> logging.Logger:
    """
    Set up ELK logging configuration.
    
    Args:
        service_name: Name of the service
        log_level: Logging level
        enable_console: Enable console logging
        enable_file: Enable file logging
        enable_logstash: Enable Logstash logging
        enable_elasticsearch: Enable direct Elasticsearch logging
        logger_name: Name of the logger
        
    Returns:
        logging.Logger: Configured logger
    """
    # Create logger
    logger = logging.getLogger(logger_name)
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Set log level
    log_level_obj = getattr(logging, log_level.upper(), logging.INFO)
    logger.setLevel(log_level_obj)
    
    # JSON formatter for structured logging
    json_formatter = JSONFormatter(service_name)
    
    # Text formatter for console (more readable) with Beijing time
    text_formatter = BeijingTimeFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Console handler
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        if settings.log_format == "json":
            console_handler.setFormatter(json_formatter)
        else:
            console_handler.setFormatter(text_formatter)
        console_handler.setLevel(log_level_obj)
        logger.addHandler(console_handler)
    
    # File handler
    if enable_file and settings.log_file_path:
        # Ensure log directory exists
        log_path = Path(settings.log_file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create rotating file handler
        file_handler = logging.handlers.TimedRotatingFileHandler(
            settings.log_file_path,
            when="midnight",
            interval=1,
            backupCount=30,  # Keep 30 days of logs
            encoding='utf-8'
        )
        file_handler.setFormatter(json_formatter)
        file_handler.setLevel(log_level_obj)
        logger.addHandler(file_handler)
    
    # Logstash handler
    if enable_logstash and settings.elk_enabled:
        try:
            logstash_handler = LogstashHandler(
                settings.logstash_host,
                settings.logstash_port
            )
            logstash_handler.setLevel(log_level_obj)
            logger.addHandler(logstash_handler)
        except Exception as e:
            print(f"Warning: Could not connect to Logstash: {e}")
    
    # Elasticsearch handler (direct)
    if enable_elasticsearch and settings.elk_enabled:
        try:
            es_handler = ElasticsearchHandler(
                settings.elasticsearch_host,
                settings.elasticsearch_port,
                settings.elasticsearch_index
            )
            es_handler.setLevel(log_level_obj)
            logger.addHandler(es_handler)
        except Exception as e:
            print(f"Warning: Could not connect to Elasticsearch: {e}")
    
    # Prevent propagation to root logger
    logger.propagate = False
    
    return logger


def get_elk_logger(name: str = __name__) -> logging.Logger:
    """
    Get an ELK-configured logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        logging.Logger: ELK-configured logger instance
    """
    return setup_elk_logging(logger_name=name)


def log_request_response(
    logger: logging.Logger,
    request_id: str,
    method: str,
    url: str,
    status_code: int,
    duration: float,
    request_data: Optional[Dict[str, Any]] = None,
    response_data: Optional[Dict[str, Any]] = None,
    error: Optional[str] = None
):
    """
    Log HTTP request/response with structured data.
    
    Args:
        logger: Logger instance
        request_id: Unique request identifier
        method: HTTP method
        url: Request URL
        status_code: HTTP status code
        duration: Request duration in seconds
        request_data: Request data (optional)
        response_data: Response data (optional)
        error: Error message (optional)
    """
    log_data = {
        "request_id": request_id,
        "http_method": method,
        "url": url,
        "status_code": status_code,
        "duration_ms": round(duration * 1000, 2),
        "type": "http_request"
    }
    
    if request_data:
        log_data["request_data"] = request_data
    
    if response_data:
        log_data["response_data"] = response_data
    
    if error:
        log_data["error"] = error
        logger.error("HTTP request failed", extra=log_data)
    else:
        logger.info("HTTP request completed", extra=log_data)


def log_task_execution(
    logger: logging.Logger,
    task_id: str,
    task_name: str,
    status: str,
    duration: Optional[float] = None,
    result: Optional[Dict[str, Any]] = None,
    error: Optional[str] = None
):
    """
    Log task execution with structured data.
    
    Args:
        logger: Logger instance
        task_id: Task identifier
        task_name: Task name
        status: Task status (started, completed, failed)
        duration: Task duration in seconds (optional)
        result: Task result (optional)
        error: Error message (optional)
    """
    log_data = {
        "task_id": task_id,
        "task_name": task_name,
        "task_status": status,
        "type": "task_execution"
    }
    
    if duration is not None:
        log_data["duration_ms"] = round(duration * 1000, 2)
    
    if result:
        log_data["result"] = result
    
    if error:
        log_data["error"] = error
        logger.error(f"Task {status}", extra=log_data)
    else:
        logger.info(f"Task {status}", extra=log_data)
