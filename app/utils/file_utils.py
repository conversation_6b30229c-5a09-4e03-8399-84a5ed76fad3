"""
File processing utilities extracted from the original audit service.
"""
import os
import re
import json
import base64
import logging
from decimal import InvalidOperation, Decimal
from typing import Tuple, Optional, Union
from urllib.parse import unquote
from pathlib import Path

import pandas as pd
from PIL import Image
from io import BytesIO

from app.core.config import get_settings
from app.services.http_service import get_http_service, HTTPServiceError
from app.utils.doc_type import DocType

settings = get_settings()
logger = logging.getLogger(__name__)


def download_and_process_doc(doc_url: str, root_file_path: str) -> Tuple[Optional[str], Optional[str]]:
    """
    从URL下载文档并保存到本地路径

    支持从HTTP/HTTPS URL下载各种格式的文档文件，
    自动处理文件名提取、路径创建和错误处理。

    Args:
        doc_url: 要下载的文档URL地址
        root_file_path: 保存文件的根目录路径

    Returns:
        Tuple[Optional[str], Optional[str]]: (本地文件路径, 错误信息)
        - 成功时返回 (文件路径, None)
        - 失败时返回 (None, 错误信息)
    """
    try:
        http_service = get_http_service()
        response = http_service.get(doc_url, timeout=30)
        logger.debug(f"Download response headers: {response.headers}")

        # Check if response contains error message
        try:
            if response.json().get('msg') == '程序出错':
                return None, response.json().get('detail', 'Unknown error')
        except:
            logger.info('Document URL is valid')

        # Extract filename from response headers or URL
        filename = None
        content_disposition = response.headers.get('Content-Disposition')

        if content_disposition:
            filename_match = re.search(r'filename="?([^";]+)"?', content_disposition)
            if filename_match:
                filename = unquote(filename_match.group(1))
                logger.info(f"Filename from Content-Disposition: {filename}")

        if not filename:
            # Extract from URL
            filename = os.path.basename(doc_url.split('?')[0])
            if not filename or '.' not in filename:
                filename = f"document_{hash(doc_url) % 10000}.pdf"
            logger.info(f"Filename from URL: {filename}")

        # Ensure root directory exists
        os.makedirs(root_file_path, exist_ok=True)

        # Save file
        local_path = os.path.join(root_file_path, filename)
        with open(local_path, "wb") as file:
            file.write(response.content)

        logger.info(f"File saved to {local_path}")
        return local_path, None

    except HTTPServiceError as e:
        logger.error(f"HTTP service error downloading {doc_url}: {e}")
        return None, f"HTTP service error: {str(e)}"
    except Exception as e:
        logger.error(f"Error downloading {doc_url}: {e}")
        return None, f"Download error: {str(e)}"

def parse_image(doc_path: str) -> Tuple[list, list, list, list, list]:
    """
    解析图片文件并转换为base64格式

    将图片文件转换为base64编码的数据URI格式，
    便于在Web界面中显示和传输。

    Args:
        doc_path: 图片文件的路径

    Returns:
        Tuple[list, list, list, list, list]: (文本列表, 数据列表, 标题列表, 内容列表, 引用列表)
        - texts: 包含base64图片数据的列表
        - data: 空数据字典列表
        - title: 文件路径列表
        - content: base64图片内容列表
        - references: 空引用字典列表
    """
    try:
        image = Image.open(doc_path).convert("RGB")
        buffer = BytesIO()
        image.save(buffer, format="PNG")
        base64_code = base64.b64encode(buffer.getvalue()).decode("utf-8")

        image_data = f"data:image/png;base64,{base64_code}"

        texts = [image_data]
        content = [image_data]
        data = [{}]
        title = [doc_path]
        references = [{}]

        return texts, data, title, content, references

    except Exception as e:
        logger.error(f"Error parsing image {doc_path}: {e}")
        return [], [], [], [], []


def parse_excel(doc_path: str, rule: dict):
    """
    Parse Excel file and extract relevant data.

    Args:
        doc_path: Path to the Excel file
        rule: Extraction rule configuration

    Returns:
        Tuple[list, list, list, list, str]: (texts, data, title, content, error_message)
    """
    level = rule.get('level')

    if level == -4:
        # 以sheet为分割读取excel
        xls = pd.ExcelFile(doc_path)
        sheet_names = xls.sheet_names
        texts = []
        content = []
        for sheet in sheet_names:
            # 强制所有列都按字符串读取，防止长数字被转为float
            df = xls.parse(sheet, dtype=str, converters={col: str for col in xls.parse(sheet, nrows=0).columns},
                           na_filter=False)
            # 将所有空值替换为""
            df = df.fillna("")
            # 统计每列小数位数的众数
            col_decimal_places = {}
            for col in df.columns:
                decimal_places_list = []
                for val in df[col]:
                    val_str = str(val).replace(',', '')
                    if re.match(r'^-?\d+(\.\d+)?$', val_str):
                        if '.' in val_str:
                            decimal_places_list.append(len(val_str.split('.')[-1]))
                        else:
                            decimal_places_list.append(0)
                # 计算众数，若无数据则默认2位
                if decimal_places_list:
                    col_decimal_places[col] = max(set(decimal_places_list), key=decimal_places_list.count)
                else:
                    col_decimal_places[col] = 2

            # 数字字符串（长度>=4的纯数字）格式化为千分位，按众数小数位
            def format_thousand(x, decimal_places):
                if x is None:
                    return ""
                x_str = str(x)
                try:
                    d = Decimal(x_str.replace(',', ''))
                    int_part = x_str.replace(',', '').split('.')[0].lstrip('-')
                    if len(int_part) >= 4:
                        fmt = "{:,." + str(decimal_places) + "f}"
                        return fmt.format(d)
                except InvalidOperation:
                    pass
                return x_str

            for col in df.columns:
                dp = col_decimal_places.get(col, 2)
                df[col] = df[col].apply(lambda x: format_thousand(x, dp))
            markdown_text = df.to_markdown(index=False)
            texts.append(markdown_text)
            content.append(markdown_text)
        data = [{"text_position": sn} for sn in sheet_names]
        title = sheet_names
        return texts, data, title, content, None
    else:
        return [], [], [], [], None


def create_resp(data: Union[str, dict, list], flag: str) -> dict:
    """
    创建标准化响应格式

    生成统一的API响应格式，包含数据和状态标识。

    Args:
        data: 响应数据，可以是字符串、字典或列表
        flag: 成功标识，'true'表示成功，'false'表示失败

    Returns:
        dict: 标准化的响应字典，包含'data'和'ok'字段
    """
    return {'data': data, 'ok': flag}


def ensure_directory(path: str) -> None:
    """
    确保目录存在，不存在则创建

    递归创建目录路径，如果目录已存在则不做任何操作。

    Args:
        path: 需要确保存在的目录路径
    """
    Path(path).mkdir(parents=True, exist_ok=True)


def get_file_size(file_path: str) -> int:
    """
    获取文件大小（字节）

    Args:
        file_path: 文件路径

    Returns:
        int: 文件大小（字节），如果文件不存在返回0
    """
    try:
        return os.path.getsize(file_path)
    except OSError:
        return 0


def is_file_too_large(file_path: str, max_size: Optional[int] = None) -> bool:
    """
    Check if file exceeds maximum size limit.

    Args:
        file_path: Path to the file
        max_size: Maximum size in bytes (uses settings if None)

    Returns:
        bool: True if file is too large
    """
    if max_size is None:
        max_size = settings.max_file_size

    file_size = get_file_size(file_path)
    return file_size > max_size
