"""
Document type utilities extracted from the original audit service.
"""
import os
from enum import IntEnum
from typing import Optional


class DocType(IntEnum):
    """Document type enumeration."""
    UNKNOWN = 0
    PDF = 1
    WORD = 2
    EXCEL = 3
    IMAGE = 4
    HTML = 5
    JSON = 6
    
    @classmethod
    def from_doc_path(cls, doc_path: str) -> 'DocType':
        """
        Determine document type from file path.
        
        Args:
            doc_path: Path to the document file
            
        Returns:
            DocType: Document type enum value
        """
        if not doc_path:
            return cls.UNKNOWN
            
        # Get file extension
        _, ext = os.path.splitext(doc_path.lower())
        ext = ext.lstrip('.')
        
        # Map extensions to document types
        extension_map = {
            'pdf': cls.PDF,
            'doc': cls.WORD,
            'docx': cls.WORD,
            'docm': cls.WORD,
            'wps': cls.WORD,
            'xls': cls.EXCEL,
            'xlsx': cls.EXCEL,
            'xlsm': cls.EXCEL,
            'csv': cls.EXCEL,
            'jpg': cls.IMAGE,
            'jpeg': cls.IMAGE,
            'png': cls.IMAGE,
            'gif': cls.IMAGE,
            'bmp': cls.IMAGE,
            'tiff': cls.IMAGE,
            'tif': cls.IMAGE,
            'webp': cls.IMAGE,
            'html': cls.HTML,
            'htm': cls.HTML,
            'json': cls.JSON,
        }
        
        return extension_map.get(ext, cls.UNKNOWN)
    
    @classmethod
    def is_supported(cls, doc_type: 'DocType') -> bool:
        """
        Check if document type is supported for processing.
        
        Args:
            doc_type: Document type to check
            
        Returns:
            bool: True if supported, False otherwise
        """
        supported_types = {cls.PDF, cls.WORD, cls.EXCEL, cls.IMAGE, cls.JSON}
        return doc_type in supported_types
    
    def __str__(self) -> str:
        """String representation of document type."""
        type_names = {
            self.UNKNOWN: "Unknown",
            self.PDF: "PDF",
            self.WORD: "Word Document",
            self.EXCEL: "Excel Spreadsheet",
            self.IMAGE: "Image",
            self.HTML: "HTML",
            self.JSON: "JSON",
        }
        return type_names.get(self, "Unknown")
