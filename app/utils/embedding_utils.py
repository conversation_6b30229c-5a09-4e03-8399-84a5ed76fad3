"""
Embedding and similarity utilities for audit service.

This module contains utilities for text embedding, similarity calculation,
and vector operations used in document retrieval and matching.
"""

import logging
import json
import numpy as np
from typing import Union, List, Optional, Tuple, Dict, Any

try:
    import faiss
except ImportError:
    faiss = None

from app.services.model_service import get_model_service, EmbeddingServiceError

logger = logging.getLogger(__name__)

def configure_embedding_service(api_key: str = None,
                               api_base: str = None,
                               model: str = None) -> Dict[str, str]:
    """
    Configure the embedding service with custom parameters.

    Note: This function is deprecated. Configuration is now handled
    through the unified HTTP service and settings.

    Args:
        api_key: API key (deprecated)
        api_base: Base URL (deprecated)
        model: Model name (deprecated)

    Returns:
        Dict[str, str]: Current configuration from settings
    """
    logger.warning("configure_embedding_service is deprecated. Use settings configuration instead.")

    from app.core.config import get_settings
    settings = get_settings()

    return {
        "api_key": settings.embedding_api_key,
        "api_base": settings.embedding_api_url,
        "model": settings.embedding_model
    }


def get_embedding(chunks: Union[str, List[str]],
                  normalize_embeddings: bool = True,
                  api_key: str = None,
                  api_base: str = None,
                  model: str = None) -> np.ndarray:
    """
    Get embeddings for text chunks using HTTP service.

    This function uses the project's unified HTTP service to call
    embedding APIs with proper error handling and fallback mechanisms.

    Args:
        chunks: Single string or list of strings to embed
        normalize_embeddings: Whether to normalize the embeddings
        api_key: API key (deprecated, uses service configuration)
        api_base: Base URL (deprecated, uses service configuration)
        model: Model name to use

    Returns:
        np.ndarray: Embedding vectors

    Raises:
        EmbeddingServiceError: If embedding service call fails
    """
    try:
        model_service = get_model_service()

        # Call the model service
        embeddings = model_service.get_embeddings(
            texts=chunks,
            model=model,
            normalize=normalize_embeddings
        )

        # Convert to numpy array
        if isinstance(chunks, str):
            # Single input, single embedding
            return np.array(embeddings, dtype=np.float32)
        else:
            # Multiple inputs, multiple embeddings
            return np.array(embeddings, dtype=np.float32)

    except Exception as e:
        logger.error(f"Error getting embeddings: {e}")
        # Generate fallback embeddings
        return _generate_fallback_embeddings(chunks, normalize_embeddings)


def _extract_embeddings_from_response(response_data: Dict[str, Any],
                                     normalize_embeddings: bool = True) -> List[np.ndarray]:
    """
    Extract embedding vectors from OpenAI API response.

    Args:
        response_data: JSON response from embedding API
        normalize_embeddings: Whether to normalize the embeddings

    Returns:
        List[np.ndarray]: List of embedding vectors
    """
    try:
        embeddings = []

        # Extract embeddings from response
        if "data" in response_data:
            for item in response_data["data"]:
                if "embedding" in item:
                    embedding = np.array(item["embedding"], dtype=np.float32)

                    if normalize_embeddings:
                        norm = np.linalg.norm(embedding)
                        if norm > 0:
                            embedding = embedding / norm

                    embeddings.append(embedding)

        if not embeddings:
            raise ValueError("No embeddings found in API response")

        logging.info(f"Extracted {len(embeddings)} embeddings from API response")
        return embeddings

    except Exception as e:
        logging.error(f"Error extracting embeddings from response: {e}")
        raise


def _generate_fallback_embeddings(chunks: Union[str, List[str]],
                                 normalize_embeddings: bool = True) -> np.ndarray:
    """
    Generate fallback embeddings when service is unavailable.

    This creates deterministic pseudo-random embeddings based on text content
    for testing and fallback purposes.

    Args:
        chunks: Single string or list of text chunks
        normalize_embeddings: Whether to normalize the embeddings

    Returns:
        np.ndarray: Fallback embedding vectors
    """
    # Normalize input
    if isinstance(chunks, str):
        chunks = [chunks]
        single_input = True
    else:
        single_input = False

    logger.warning(f"Generating fallback embeddings for {len(chunks)} chunks")

    embedding_dim = 1024  # Standard embedding dimension
    embeddings = []

    for chunk in chunks:
        # Generate deterministic embedding based on text hash
        text_hash = hash(chunk) % (2**32)
        np.random.seed(text_hash)
        embedding = np.random.normal(0, 1, embedding_dim).astype(np.float32)

        if normalize_embeddings:
            norm = np.linalg.norm(embedding)
            if norm > 0:
                embedding = embedding / norm

        embeddings.append(embedding)

    result = np.array(embeddings)

    if single_input:
        return result[0]
    else:
        return result


def get_embedding_batch(chunks: List[str],
                       batch_size: int = 100,
                       normalize_embeddings: bool = True,
                       api_key: str = None,
                       api_base: str = None,
                       model: str = None) -> np.ndarray:
    """
    Get embeddings for large lists of chunks with batch processing.

    This function automatically splits large requests into smaller batches
    to avoid API limits and improve reliability using HTTP service.

    Args:
        chunks: List of text chunks to embed
        batch_size: Maximum number of chunks per API call
        normalize_embeddings: Whether to normalize the embeddings
        api_key: API key (deprecated, uses service configuration)
        api_base: Base URL (deprecated, uses service configuration)
        model: Model name to use

    Returns:
        np.ndarray: Array of embedding vectors
    """
    if not chunks:
        return np.array([])

    if len(chunks) <= batch_size:
        # Single batch
        return get_embedding(chunks, normalize_embeddings, model=model)

    # Process in batches
    all_embeddings = []

    for i in range(0, len(chunks), batch_size):
        batch = chunks[i:i + batch_size]
        logger.info(f"Processing batch {i//batch_size + 1}/{(len(chunks) + batch_size - 1)//batch_size}")

        try:
            batch_embeddings = get_embedding(batch, normalize_embeddings, model=model)

            # Ensure batch_embeddings is 2D
            if batch_embeddings.ndim == 1:
                batch_embeddings = batch_embeddings.reshape(1, -1)

            all_embeddings.append(batch_embeddings)

        except Exception as e:
            logger.error(f"Error processing batch {i//batch_size + 1}: {e}")
            # Generate fallback for this batch
            fallback = _generate_fallback_embeddings(batch, normalize_embeddings)
            all_embeddings.append(fallback)

    # Concatenate all batches
    result = np.vstack(all_embeddings)
    logger.info(f"Completed batch processing: {result.shape}")

    return result


def build_index(blocks: List[List[str]]) -> List[Optional['faiss.IndexFlatIP']]:
    """
    Build FAISS indices for document blocks with optimized processing.

    This function creates FAISS indices for each group of document blocks,
    processing them in batches for efficiency. It handles image data by
    truncating base64 strings and uses the embedding service for vectorization.

    Args:
        blocks: List of document block groups, where each group contains text blocks

    Returns:
        List of FAISS IndexFlatIP indices (or None for empty groups)

    Raises:
        ImportError: If FAISS is not available
        Exception: If embedding generation fails
    """
    if faiss is None:
        raise ImportError("FAISS is not available. Please install faiss-cpu or faiss-gpu.")

    logger.info(f"Building FAISS indices for {len(blocks)} block groups")

    # Initialize indices list
    indices = []

    for i, blocks_group in enumerate(blocks):
        if blocks_group:
            logger.info(f"Building index for group {i} with {len(blocks_group)} blocks")

            # Create FAISS index with inner product similarity (1024 dimensions)
            index = faiss.IndexFlatIP(1024)

            # Process blocks in batches of 10 for efficiency
            batch_size = 10
            for j in range(0, len(blocks_group), batch_size):
                batch = blocks_group[j:min(len(blocks_group), j + batch_size)]

                # Preprocess batch: truncate image data to first 50 characters
                processed_batch = []
                for text in batch:
                    if text.startswith("data:image/png;base64,"):
                        # Truncate image data to reduce processing overhead
                        processed_text = text[:50]
                    else:
                        processed_text = text
                    processed_batch.append(processed_text)

                try:
                    # Get embeddings for the batch
                    embeddings = get_embedding(processed_batch, normalize_embeddings=True)

                    # Ensure embeddings are in the correct format for FAISS
                    if embeddings.ndim == 1:
                        embeddings = embeddings.reshape(1, -1)

                    # Add embeddings to the index
                    index.add(embeddings.astype(np.float32))

                    # Clean up memory
                    del embeddings

                    logger.debug(f"Added batch {j//batch_size + 1} to index {i}")

                except Exception as e:
                    logger.error(f"Error processing batch {j//batch_size + 1} in group {i}: {e}")
                    # Continue with next batch instead of failing completely
                    continue

            # Store the completed index
            indices.append(index)
            logger.info(f"Completed index {i} with {index.ntotal} vectors")

        else:
            # Empty group, store None
            indices.append(None)
            logger.debug(f"Group {i} is empty, storing None")

    logger.info(f"Successfully built {sum(1 for idx in indices if idx is not None)} indices")
    return indices


def search_index(index: 'faiss.IndexFlatIP',
                query_embedding: np.ndarray,
                top_k: int = 5) -> Tuple[np.ndarray, np.ndarray]:
    """
    Search FAISS index for similar vectors.

    Args:
        index: FAISS index to search
        query_embedding: Query embedding vector
        top_k: Number of top results to return

    Returns:
        Tuple of (similarities, indices) arrays

    Raises:
        ImportError: If FAISS is not available
        ValueError: If index is None or query_embedding is invalid
    """
    if faiss is None:
        raise ImportError("FAISS is not available. Please install faiss-cpu or faiss-gpu.")

    if index is None:
        raise ValueError("Index cannot be None")

    if query_embedding is None or query_embedding.size == 0:
        raise ValueError("Query embedding cannot be None or empty")

    try:
        # Ensure query embedding is in correct format
        if query_embedding.ndim == 1:
            query_embedding = query_embedding.reshape(1, -1)

        # Search the index
        similarities, indices = index.search(query_embedding.astype(np.float32), top_k)

        logger.debug(f"Found {len(indices[0])} results for query")
        return similarities, indices

    except Exception as e:
        logger.error(f"Error searching FAISS index: {e}")
        raise


def save_index(index: 'faiss.IndexFlatIP', filepath: str) -> bool:
    """
    Save FAISS index to disk.

    Args:
        index: FAISS index to save
        filepath: Path to save the index

    Returns:
        bool: True if successful, False otherwise
    """
    if faiss is None:
        logger.error("FAISS is not available")
        return False

    if index is None:
        logger.error("Cannot save None index")
        return False

    try:
        faiss.write_index(index, filepath)
        logger.info(f"Successfully saved index to {filepath}")
        return True
    except Exception as e:
        logger.error(f"Error saving index to {filepath}: {e}")
        return False


def load_index(filepath: str) -> Optional['faiss.IndexFlatIP']:
    """
    Load FAISS index from disk.

    Args:
        filepath: Path to load the index from

    Returns:
        FAISS index or None if loading fails
    """
    if faiss is None:
        logger.error("FAISS is not available")
        return None

    try:
        index = faiss.read_index(filepath)
        logger.info(f"Successfully loaded index from {filepath}")
        return index
    except Exception as e:
        logger.error(f"Error loading index from {filepath}: {e}")
        return None


def rerank(query: str, documents: List[str], top_k: int) -> List[int]:
    """
    Rerank documents based on relevance to query.

    TODO: Implement document reranking including:
    - Integration with reranking service
    - Local reranking models
    - Multiple reranking strategies
    - Performance optimization

    Args:
        query: Search query
        documents: List of documents to rerank
        top_k: Number of top results to return

    Returns:
        List of document indices in ranked order
    """
    logging.warning("rerank is not fully implemented - using placeholder")

    # TODO: Implement actual reranking service call
    # This is a placeholder that returns documents in original order

    try:
        # Placeholder reranking logic - simple text similarity
        scores = []
        query_lower = query.lower()

        for i, doc in enumerate(documents):
            doc_lower = doc.lower()
            # Simple word overlap scoring
            query_words = set(query_lower.split())
            doc_words = set(doc_lower.split())
            overlap = len(query_words.intersection(doc_words))
            score = overlap / max(len(query_words), 1)
            scores.append((i, score))

        # Sort by score descending
        scores.sort(key=lambda x: x[1], reverse=True)

        # Return top_k indices
        result = [idx for idx, score in scores[:top_k]]

        logging.info(f"Placeholder reranking: query='{query}', top_k={top_k}, result={result}")
        return result

    except Exception as e:
        logging.error(f"Error in reranking: {e}")
        # Fallback to original order
        return list(range(min(top_k, len(documents))))


def calculate_similarity(embedding1: np.ndarray, embedding2: np.ndarray) -> float:
    """
    Calculate cosine similarity between two embeddings.

    Args:
        embedding1: First embedding vector
        embedding2: Second embedding vector

    Returns:
        float: Cosine similarity score
    """
    try:
        # Normalize embeddings
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        # Calculate cosine similarity
        similarity = np.dot(embedding1, embedding2) / (norm1 * norm2)
        return float(similarity)

    except Exception as e:
        logging.error(f"Error calculating similarity: {e}")
        return 0.0


def search_similar_documents(query_embedding: np.ndarray,
                           document_embeddings: List[np.ndarray],
                           top_k: int = 5,
                           threshold: float = 0.0) -> List[Tuple[int, float]]:
    """
    Search for similar documents based on embedding similarity.

    Args:
        query_embedding: Query embedding vector
        document_embeddings: List of document embedding vectors
        top_k: Number of top results to return
        threshold: Minimum similarity threshold

    Returns:
        List of (index, similarity_score) tuples
    """
    try:
        similarities = []

        for i, doc_embedding in enumerate(document_embeddings):
            similarity = calculate_similarity(query_embedding, doc_embedding)
            if similarity >= threshold:
                similarities.append((i, similarity))

        # Sort by similarity descending
        similarities.sort(key=lambda x: x[1], reverse=True)

        # Return top_k results
        return similarities[:top_k]

    except Exception as e:
        logging.error(f"Error searching similar documents: {e}")
        return []


def batch_embed_documents(documents: List[str],
                         batch_size: int = 10,
                         **kwargs) -> List[np.ndarray]:
    """
    Embed documents in batches for efficiency.

    Args:
        documents: List of documents to embed
        batch_size: Number of documents to process in each batch
        **kwargs: Additional arguments for get_embedding

    Returns:
        List of embedding vectors
    """
    try:
        all_embeddings = []

        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size]
            batch_embeddings = get_embedding(batch, **kwargs)

            if batch_embeddings.ndim == 1:
                # Single embedding returned
                all_embeddings.append(batch_embeddings)
            else:
                # Multiple embeddings returned
                all_embeddings.extend(batch_embeddings)

        return all_embeddings

    except Exception as e:
        logging.error(f"Error in batch embedding: {e}")
        return []


# TODO: Add more embedding utilities:
# - Embedding caching mechanisms
# - Different similarity metrics
# - Embedding dimension reduction
# - Cross-lingual embedding support
# - Embedding quality evaluation
