"""
Logging configuration for the audit service.
"""
import logging
import sys
from logging.handlers import TimedRotatingFileHandler
from pathlib import Path
from typing import Optional

from app.core.config import get_settings

settings = get_settings()

# Import ELK logging if available
try:
    from app.utils.elk_logging import setup_elk_logging, get_elk_logger
    ELK_AVAILABLE = True
except ImportError:
    ELK_AVAILABLE = False


def setup_logging(
    log_file_path: Optional[str] = None,
    debug: bool = False,
    logger_name: str = "",
    use_elk: bool = None
) -> logging.Logger:
    """
    Set up logging configuration with optional ELK support.

    Args:
        log_file_path: Path to log file (optional)
        debug: Enable debug logging
        logger_name: Name of the logger
        use_elk: Use ELK logging if available (auto-detect if None)

    Returns:
        logging.Logger: Configured logger
    """
    # Determine if we should use ELK
    if use_elk is None:
        use_elk = ELK_AVAILABLE and settings.elk_enabled

    # Use ELK logging if available and enabled
    if use_elk and ELK_AVAILABLE:
        log_level = "DEBUG" if debug else settings.log_level
        return setup_elk_logging(
            service_name="audit-service",
            log_level=log_level,
            logger_name=logger_name
        )

    # Fallback to traditional logging
    # Create logger
    logger = logging.getLogger(logger_name)

    # Clear existing handlers
    logger.handlers.clear()

    # Set log level
    log_level = logging.DEBUG if debug else getattr(logging, settings.log_level.upper())
    logger.setLevel(log_level)

    # Create formatter
    formatter = logging.Formatter(
        '%(levelname)s %(asctime)s '
        '[%(filename)12s:%(lineno)3s - %(funcName)15s()] %(message)s'
    )

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(log_level)
    logger.addHandler(console_handler)

    # File handler (if log file path is provided)
    if log_file_path:
        # Ensure log directory exists
        log_path = Path(log_file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        # Create rotating file handler
        file_handler = TimedRotatingFileHandler(
            log_file_path,
            when="midnight",
            interval=1,
            backupCount=7,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(log_level)
        logger.addHandler(file_handler)

    # Prevent propagation to root logger
    logger.propagate = False

    return logger


def get_logger(name: str = __name__) -> logging.Logger:
    """
    Get a logger instance with ELK support if available.

    Args:
        name: Logger name

    Returns:
        logging.Logger: Logger instance
    """
    # Use ELK logger if available and enabled
    if ELK_AVAILABLE and settings.elk_enabled:
        return get_elk_logger(name)

    # Fallback to standard logger
    return logging.getLogger(name)
