"""
Text processing utilities for audit service.

This module contains utility functions for text cleaning, processing,
and manipulation used throughout the audit service.
"""

import re
import logging
from typing import List, Union


def auto_text_clean(text: str) -> str:
    """
    Automatically clean text by intelligently removing spaces and newlines.

    Rules:
    - Remove spaces/newlines at the beginning and end of text
    - Preserve spaces between English letters (a b -> a b)
    - Replace newlines between English letters with spaces (a\nb -> a b)
    - Preserve spaces between punctuation and English letters ('hello -> 'hello)
    - Preserve spaces between digits and English letters (3 a -> 3 a)
    - Remove spaces between Chinese characters (你好 吗 -> 你好吗)
    - Remove spaces between Chinese and other characters (你好 123 -> 你好123)

    Examples:
        "你好 吗ab cd ef" -> "你好吗ab cd ef"
        "hello world" -> "hello world"
        "hello\nworld" -> "hello world"
        "数字3 和字母a" -> "数字3和字母a"

    Args:
        text (str): Raw text to clean

    Returns:
        str: Cleaned text with intelligent space handling
    """
    if not text or not isinstance(text, str):
        return ""

    def is_english_letter(char):
        """Check if character is an English letter (a-z, A-Z)"""
        return char.isalpha() and ord(char) < 256

    def is_chinese_char(char):
        """Check if character is a Chinese character"""
        return '\u4e00' <= char <= '\u9fff'

    # Convert to list for efficient processing
    text_list = list(text)
    result = []

    # Define punctuation that should preserve spaces with English letters
    preserve_punctuation = {"'", "'", "-", ".", ","}

    i = 0
    while i < len(text_list):
        char = text_list[i]

        # Handle spaces and newlines
        if char in {' ', '\n'}:
            # Skip spaces/newlines at the beginning
            if not result:
                i += 1
                continue

            # Skip spaces/newlines at the end
            if i == len(text_list) - 1:
                i += 1
                continue

            # Get previous and next characters
            prev_char = result[-1] if result else ''
            next_char = text_list[i + 1] if i + 1 < len(text_list) else ''

            # Skip consecutive spaces/newlines
            if next_char in {' ', '\n'}:
                i += 1
                continue

            # Determine whether to keep the space/newline
            should_keep_space = False

            # Keep space/newline between English letters (convert newline to space)
            if is_english_letter(prev_char) and is_english_letter(next_char):
                should_keep_space = True
            # Keep space between punctuation and English letters
            elif prev_char in preserve_punctuation and is_english_letter(next_char):
                should_keep_space = True
            elif is_english_letter(prev_char) and next_char in preserve_punctuation:
                should_keep_space = True
            # Keep space between digits and English letters
            elif prev_char.isdigit() and is_english_letter(next_char):
                should_keep_space = True
            elif is_english_letter(prev_char) and next_char.isdigit():
                should_keep_space = True

            # Add space if needed (always use space, not newline)
            if should_keep_space:
                result.append(' ')

        else:
            # Regular character, add to result
            result.append(char)

        i += 1

    return ''.join(result)


def adjust_indent(text: str, num_spaces: int = 1) -> str:
    """
    Adjust indentation of text by adding tabs to each line.

    Args:
        text (str): Text to adjust
        num_spaces (int): Number of tab spaces to add

    Returns:
        str: Text with adjusted indentation
    """
    if not text:
        return ""

    lines = text.splitlines()
    return '\n'.join([('\t' * num_spaces) + line.lstrip() for line in lines])


def extract_json(text: str) -> str:
    """
    Extract JSON content from text response with improved logic.

    This function tries multiple extraction strategies in order of preference:
    1. JSON code blocks (```json ... ```)
    2. Generic code blocks (``` ... ```)
    3. Task-specific JSON arrays
    4. Standalone JSON arrays
    5. Standalone JSON objects
    6. Fallback to original text

    Args:
        text (str): Text containing JSON

    Returns:
        str: Extracted JSON string or original text if no JSON found
    """
    if not text or not isinstance(text, str):
        return ""

    import json

    # 1. JSON code blocks with explicit json tag (most reliable)
    json_content = re.findall(r"```json\s*\n?(.*?)```", text, re.DOTALL)
    if json_content:
        candidate = json_content[-1].strip()
        if candidate and _is_valid_json(candidate):
            return candidate

    # 2. Generic code blocks (might contain JSON)
    json_content = re.findall(r"```\s*\n?(.*?)```", text, re.DOTALL)
    if json_content:
        candidate = json_content[-1].strip()
        if candidate and _is_valid_json(candidate):
            return candidate

    # 3. Task-specific JSON arrays (specific to audit responses)
    json_content = re.findall(r"任务：(\[.*?\])", text, re.DOTALL)
    if json_content:
        candidate = json_content[-1].strip()
        if candidate and _is_valid_json(candidate):
            return candidate

    # 4. Standalone JSON objects (find all possible objects)
    all_json_objects = _find_all_json_objects(text)
    if all_json_objects:
        # Try from longest to shortest
        candidates = sorted(all_json_objects, key=len, reverse=True)
        for candidate in candidates:
            candidate = candidate.strip()
            if candidate and _is_valid_json(candidate):
                return candidate

    # 5. Standalone JSON arrays (find all possible arrays)
    all_json_arrays = _find_all_json_arrays(text)
    if all_json_arrays:
        # Try from longest to shortest
        candidates = sorted(all_json_arrays, key=len, reverse=True)
        for candidate in candidates:
            candidate = candidate.strip()
            if candidate and _is_valid_json(candidate):
                return candidate

    # If no JSON patterns found, return original text
    return text


def _is_valid_json(text: str) -> bool:
    """
    Check if text is valid JSON by attempting to parse it.

    Args:
        text (str): Text to validate

    Returns:
        bool: True if text is valid JSON
    """
    if not text:
        return False

    try:
        import json
        json.loads(text)
        return True
    except (json.JSONDecodeError, ValueError):
        return False


def _find_all_json_objects(text: str) -> List[str]:
    """
    Find all valid JSON objects in text using bracket matching.

    Args:
        text (str): Text to search

    Returns:
        List[str]: List of potential JSON object strings
    """
    objects = []
    i = 0

    while i < len(text):
        if text[i] == '{':
            # Found opening brace, find matching closing brace
            bracket_count = 1
            start = i
            i += 1
            in_string = False
            escape_next = False

            while i < len(text) and bracket_count > 0:
                char = text[i]

                if escape_next:
                    escape_next = False
                elif char == '\\':
                    escape_next = True
                elif char == '"' and not escape_next:
                    in_string = not in_string
                elif not in_string:
                    if char == '{':
                        bracket_count += 1
                    elif char == '}':
                        bracket_count -= 1

                i += 1

            if bracket_count == 0:
                # Found balanced braces
                candidate = text[start:i]
                if len(candidate) > 2:  # Must be more than just braces
                    objects.append(candidate)
        else:
            i += 1

    return objects


def _find_all_json_arrays(text: str) -> List[str]:
    """
    Find all valid JSON arrays in text using bracket matching.

    Args:
        text (str): Text to search

    Returns:
        List[str]: List of potential JSON array strings
    """
    arrays = []
    i = 0

    while i < len(text):
        if text[i] == '[':
            # Found opening bracket, find matching closing bracket
            bracket_count = 1
            start = i
            i += 1
            in_string = False
            escape_next = False

            while i < len(text) and bracket_count > 0:
                char = text[i]

                if escape_next:
                    escape_next = False
                elif char == '\\':
                    escape_next = True
                elif char == '"' and not escape_next:
                    in_string = not in_string
                elif not in_string:
                    if char == '[':
                        bracket_count += 1
                    elif char == ']':
                        bracket_count -= 1

                i += 1

            if bracket_count == 0:
                # Found balanced brackets
                candidate = text[start:i]
                if len(candidate) > 2:  # Must be more than just brackets
                    arrays.append(candidate)
        else:
            i += 1

    return arrays


def _is_likely_json(text: str) -> bool:
    """
    Quick check if text looks like valid JSON without full parsing.

    Args:
        text (str): Text to check

    Returns:
        bool: True if text looks like JSON
    """
    if not text:
        return False

    text = text.strip()

    # Check for basic JSON structure
    if (text.startswith('{') and text.endswith('}')) or \
       (text.startswith('[') and text.endswith(']')):
        # Basic bracket matching
        if text.startswith('{'):
            return text.count('{') >= text.count('}')
        else:
            return text.count('[') >= text.count(']')

    return False


def extract_and_validate_json(text: str, fix_common_issues: bool = True) -> str:
    """
    Enhanced JSON extraction with validation and common issue fixing.

    Args:
        text (str): Text containing JSON
        fix_common_issues (bool): Whether to attempt fixing common JSON issues

    Returns:
        str: Valid JSON string or original text if no valid JSON found
    """
    import json

    # First try basic extraction
    extracted = extract_json(text)

    # Try to validate the extracted JSON (whether it's from extraction or original text)
    try:
        json.loads(extracted)
        return extracted  # Valid JSON, return as-is
    except json.JSONDecodeError:
        if not fix_common_issues:
            return extracted  # Return as-is if not fixing issues

        # Try to fix common JSON issues
        fixed_json = _fix_common_json_issues(extracted)

        try:
            json.loads(fixed_json)
            return fixed_json  # Successfully fixed
        except json.JSONDecodeError:
            return extracted  # Return original extraction if can't fix


def _fix_common_json_issues(json_str: str) -> str:
    """
    Fix common JSON formatting issues.

    Args:
        json_str (str): Potentially malformed JSON string

    Returns:
        str: JSON string with common issues fixed
    """
    if not json_str:
        return json_str

    # Remove leading/trailing whitespace
    json_str = json_str.strip()

    # Fix common issues
    fixes = [
        # Fix single quotes to double quotes (but preserve quotes in strings)
        (r"(?<!\\)'([^']*)'", r'"\1"'),

        # Fix trailing commas before closing brackets/braces
        (r',\s*([}\]])', r'\1'),

        # Fix missing quotes around keys (simple cases)
        (r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":'),

        # Fix Python-style True/False/None to JSON equivalents
        (r'\bTrue\b', 'true'),
        (r'\bFalse\b', 'false'),
        (r'\bNone\b', 'null'),

        # Remove extra commas
        (r',+', ','),

        # Fix spacing around colons and commas
        (r'\s*:\s*', ':'),
        (r'\s*,\s*', ','),
    ]

    for pattern, replacement in fixes:
        try:
            json_str = re.sub(pattern, replacement, json_str)
        except re.error:
            continue  # Skip invalid patterns

    return json_str


def extract_json_robust(text: str) -> str:
    """
    Robust JSON extraction using bracket matching for nested structures.

    This function uses a more sophisticated approach to handle deeply nested
    JSON objects and arrays by counting brackets and finding balanced pairs.

    Args:
        text (str): Text containing JSON

    Returns:
        str: Extracted JSON string or original text if no JSON found
    """
    if not text or not isinstance(text, str):
        return ""

    # First try the standard patterns
    standard_result = extract_json(text)
    if standard_result != text:
        return standard_result

    # Try bracket-based extraction for complex nested structures
    json_candidates = []

    # Find JSON objects using bracket matching
    obj_candidates = _find_balanced_json(text, '{', '}')
    json_candidates.extend(obj_candidates)

    # Find JSON arrays using bracket matching
    array_candidates = _find_balanced_json(text, '[', ']')
    json_candidates.extend(array_candidates)

    # Return the longest valid JSON candidate
    if json_candidates:
        # Sort by length (longest first) and return the first valid one
        json_candidates.sort(key=len, reverse=True)
        for candidate in json_candidates:
            candidate = candidate.strip()
            if candidate and _is_likely_json(candidate):
                # Try to parse to ensure it's valid JSON
                try:
                    import json
                    json.loads(candidate)
                    return candidate
                except json.JSONDecodeError:
                    # Try the next candidate
                    continue

    return text


def _find_balanced_json(text: str, open_char: str, close_char: str) -> List[str]:
    """
    Find balanced JSON structures using bracket counting.

    Args:
        text (str): Text to search
        open_char (str): Opening bracket character ('{' or '[')
        close_char (str): Closing bracket character ('}' or ']')

    Returns:
        List[str]: List of potential JSON strings
    """
    candidates = []
    i = 0

    while i < len(text):
        if text[i] == open_char:
            # Found opening bracket, find matching closing bracket
            bracket_count = 1
            start = i
            i += 1
            in_string = False
            escape_next = False

            while i < len(text) and bracket_count > 0:
                char = text[i]

                if escape_next:
                    escape_next = False
                elif char == '\\':
                    escape_next = True
                elif char == '"' and not escape_next:
                    in_string = not in_string
                elif not in_string:
                    if char == open_char:
                        bracket_count += 1
                    elif char == close_char:
                        bracket_count -= 1

                i += 1

            if bracket_count == 0:
                # Found balanced brackets
                candidate = text[start:i]
                if len(candidate) > 2:  # Must be more than just brackets
                    candidates.append(candidate)
        else:
            i += 1

    return candidates


def extract_json_optimized(text: str) -> str:
    """
    Optimized JSON extraction that combines the best of both approaches.

    This function first tries the pattern-based approach, then falls back
    to bracket matching for complex cases.

    Args:
        text (str): Text containing JSON

    Returns:
        str: Extracted JSON string or original text if no JSON found
    """
    if not text or not isinstance(text, str):
        return ""

    import json

    # First try the standard pattern-based extraction
    standard_result = extract_json(text)
    if standard_result != text:
        # Validate the extracted JSON
        try:
            json.loads(standard_result)
            return standard_result
        except json.JSONDecodeError:
            pass  # Continue to bracket-based extraction

    # Try bracket-based extraction for complex cases
    # Look for all possible JSON structures and return the longest valid one
    candidates = []

    for start_char, end_char in [('{', '}'), ('[', ']')]:
        i = 0
        while i < len(text):
            first_pos = text.find(start_char, i)
            if first_pos == -1:
                break

            # Find the matching closing bracket
            bracket_count = 0
            in_string = False
            escape_next = False

            for j in range(first_pos, len(text)):
                char = text[j]

                if escape_next:
                    escape_next = False
                    continue

                if char == '\\':
                    escape_next = True
                    continue

                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue

                if not in_string:
                    if char == start_char:
                        bracket_count += 1
                    elif char == end_char:
                        bracket_count -= 1
                        if bracket_count == 0:
                            # Found complete JSON structure
                            candidate = text[first_pos:j+1]
                            try:
                                json.loads(candidate)
                                candidates.append(candidate)
                            except json.JSONDecodeError:
                                pass  # Invalid JSON, skip
                            break

            i = first_pos + 1

    # Return the longest valid JSON candidate
    if candidates:
        return max(candidates, key=len)

    # If no valid JSON found, return original text
    return text



class SimpleTextSplitter:
    """
    A custom text splitter that allows dynamic adjustment of chunk size during splitting.

    TODO: Implement full text splitting functionality including:
    - Sentence-aware splitting
    - Overlap handling
    - Multiple splitting strategies
    - Token-based splitting
    """

    def __init__(self,
                 chunk_size: int = 4000,
                 chunk_overlap: int = 200,
                 length_function=len,
                 keep_separator: bool = False,
                 add_start_index: bool = False):
        """
        Initialize the SimpleTextSplitter.

        Args:
            chunk_size (int): The maximum size of each chunk.
            chunk_overlap (int): The number of overlapping characters between chunks.
            length_function (callable): Function to calculate the length of text (default is len).
            keep_separator (bool): Whether to keep the separator in chunks.
            add_start_index (bool): Whether to include the start index of chunks.
        """
        self._chunk_size = chunk_size
        self._chunk_overlap = chunk_overlap
        self._length_function = length_function
        self._keep_separator = keep_separator
        self._add_start_index = add_start_index

    def split_text(self, text: str, chunk_size: int = None, overlap: int = None) -> List[str]:
        """
        Split text into chunks, optionally adjusting the chunk size dynamically.

        TODO: Implement sophisticated text splitting logic including:
        - Respect sentence boundaries
        - Handle different document types
        - Maintain context across chunks
        - Support multiple splitting strategies

        Args:
            text (str): The text to be split.
            chunk_size (int, optional): The chunk size to use for this split.
            overlap (int, optional): The overlap size to use for this split.

        Returns:
            List[str]: A list of text chunks.
        """
        if not text:
            return []

        # Use provided chunk_size or default
        effective_chunk_size = chunk_size or self._chunk_size
        effective_overlap = overlap or self._chunk_overlap

        if len(text) <= effective_chunk_size:
            return [text]

        # Simple splitting implementation
        chunks = []
        start = 0

        while start < len(text):
            end = start + effective_chunk_size

            if end >= len(text):
                chunks.append(text[start:])
                break

            # Try to find a good break point (sentence end, paragraph, etc.)
            chunk_text = text[start:end]

            # Look for sentence endings near the end of the chunk
            sentence_ends = [m.end() for m in re.finditer(r'[.!?]\s+', chunk_text)]
            if sentence_ends:
                # Use the last sentence ending if it's in the latter half of the chunk
                last_sentence_end = sentence_ends[-1]
                if last_sentence_end > effective_chunk_size // 2:
                    end = start + last_sentence_end

            chunks.append(text[start:end])
            start = end - effective_overlap

            # Ensure we make progress
            if start <= chunks[-1].__len__() - effective_chunk_size:
                start = end

        return [chunk.strip() for chunk in chunks if chunk.strip()]


def format_thousand(value: Union[str, int, float], decimal_places: int = 2) -> str:
    """
    Format numbers with thousand separators.

    TODO: Implement comprehensive number formatting including:
    - Handle different number formats
    - Support various locales
    - Handle edge cases and invalid inputs

    Args:
        value: Value to format
        decimal_places: Number of decimal places

    Returns:
        str: Formatted number string
    """
    if value is None:
        return ""

    try:
        # Basic implementation
        if isinstance(value, str):
            # Remove existing commas and try to convert
            clean_value = value.replace(',', '')
            num_value = float(clean_value)
        else:
            num_value = float(value)

        # Format with thousand separators
        return f"{num_value:,.{decimal_places}f}"

    except (ValueError, TypeError):
        return str(value)


def clean_markdown_table(text: str) -> str:
    """
    Clean and format markdown table text.

    TODO: Implement markdown table cleaning including:
    - Fix table alignment
    - Handle malformed tables
    - Normalize cell content
    - Remove extra formatting

    Args:
        text (str): Markdown table text

    Returns:
        str: Cleaned markdown table
    """
    if not text:
        return ""

    # Basic cleaning - remove extra whitespace around pipes
    lines = text.split('\n')
    cleaned_lines = []

    for line in lines:
        if '|' in line:
            # Clean up table row
            cells = [cell.strip() for cell in line.split('|')]
            cleaned_line = '| ' + ' | '.join(cells[1:-1]) + ' |'
            cleaned_lines.append(cleaned_line)
        else:
            cleaned_lines.append(line.strip())

    return '\n'.join(cleaned_lines)


# TODO: Add more text processing utilities as needed:
# - Text normalization functions
# - Language detection
# - Text similarity calculations
# - Content extraction utilities
# - Format conversion functions
