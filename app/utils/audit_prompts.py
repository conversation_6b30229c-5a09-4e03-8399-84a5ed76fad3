"""
Document audit prompt templates and utilities.

This module contains prompt templates and utilities for generating
audit prompts for different types of document analysis tasks.
"""

import re
import logging
from typing import Dict, Any, List


class DocumentAuditPrompt:
    """Template manager for document audit prompts"""

    @staticmethod
    def get_base_template() -> str:
        """
        Get the base audit prompt template.
        
        Returns:
            str: Base prompt template with placeholders
        """
        return """
你的任务是对一个原文档进行审核，并根据提供的目标文档标准来检查其一致性。请按照以下提供的任务描述、相关知识、示例和输出格式的指示，完成审核工作。

任务描述:
{task}

相关知识:
{knowledge}

输出格式的说明:
{output}

请使用 JSON 格式来呈现审核结果，并且只包括以下字段：
{output}

根据输出格式的说明，请使用 JSON 格式来呈现审核结果，输出示例结构如下：
'''json
{{"...": "...",
  "...": "...",
  "...": "...",
  "...": "..."}}
'''

请注意，如果某些字段不在{output}中，可以省略这些字段，反之则增加这些字段。
下面是一些示例以供参考：

示例:
    相关知识:
        背景：你是一个专业是文档复核专家
        目标：请仔细对比模板内容与原文内容是否匹配
        审核要求：模板内容如下：
        We have audited the financial statements of [ABC Co] (the "Company"), which comprise the Company's balance sheet1 as at 31 December 202X, the Company's income statement, statement of cash flows and statement of changes in equityfor the year then ended and notes to the financial statements.​
        原文：We have audited the financial statements of  China Petroleum Logging Co.,Ltd. (hereinafter referred as  the "Company"), which comprise the consolidated and the Company's balance sheets as at 31 December 2023, the consolidated and the Company's income statements, statements of cash flows and statements of changes in equityfor the year then ended and notes to the financial statements.
    输出格式的说明:
        分析：针对披露要求逐条分析是否被满足，招股说明书是否按要求进行披露，是否有遗漏的内容，以及判断依据。
        总结：基于以上分析，总结是否存在遗漏，如果存在遗漏，则按照披露要求的列表及序号，逐条列出所有遗漏的内容点。
        审核结论：基于以上分析，输出"审核通过"或"审核不通过"。
        审核不通过内容：基于以上分析，将所有存在遗漏的内容，按照披露要求的列表及序号，逐条列出所有遗漏的内容点。
    输出：'''json
        {{"分析":"1.公司名称:\n\t模板内容:[ABC Co]\n\t原文内容:China Petroleum Logging Co., Ltd.\n\t判断依据:原文中的公司名称与模板中的占位符不同，但这是正常的，因为模板中的占位符需要替换为具体的公司名称。因此，这一部分符合要求。\n2.财务报表类型：\n\t模板内容:balance sheet, income statement, statement of cash flows, statement of changes in equity\n\t原文内容:consolidated and the Company's balance sheets, consolidated and the Company's income statements, statements of cash flows, statements of changes in equity\n\t判断依据:原文中增加了"consolidated and the Company's"这一表述,这表明原文不仅包括了公司的单独财务报表，还包括了合并财务报表。这一部分符合要求，但需要注意的是，模板中没有提到合并财务报表，这可能是额外的信息。\n3.时间范围:\n\t模板内容：as at 31 December 202X, for the year then ended\n\t原文内容:as at 31 December 2023, for the year then ended\n\t判断依据:原文中的具体年份（2023）替换了模板中的占位符（202X），这是正常的。因此，这一部分符合要求。\n4.附注:\n\t模板内容：notes to the financial statements\n\t原文内容:notes to the financial statements \n\t判断依据:原文和模板内容完全一致，这一部分符合要求。",
          "总结":"不存在遗漏",
          "审核结论":"审核通过",
          "审核不通过内容":"无"}}
        '''

    相关知识:
        背景：你是一个专业是文档复核专家
        目标：请仔细对比模板内容与原文内容是否匹配
        审核要求：模板内容如下：
        We have audited the financial statements of [ABC Co] (the "Company"), which comprise the Company's balance sheet1 as at 31 December 202X, the Company's income statement, statement of cash flows and statement of changes in equityfor the year then ended and notes to the financial statements.​
        原文:We have audited the financial statements of  China Petroleum Logging Co.,Ltd. (hereinafter referred as  the "Company"), which comprise the consolidated and the Company's balance sheets as at 31 December 2023, the consolidated and the Company's income statements, statements of cash flows and statements of changes in equityfor the year then ended and notes to the financial statements.
    输出格式的说明:
        分析：针对披露要求逐条分析是否被满足，招股说明书是否按要求进行披露，是否有遗漏的内容，以及判断依据。
        审核结论:基于以上分析，输出"审核通过"或"审核不通过"。
    输出：'''json
        {{"分析":"1.公司名称:\n\t模板内容:[ABC Co]\n\t原文内容:China Petroleum Logging Co., Ltd.\n\t判断依据:原文中的公司名称与模板中的占位符不同，但这是正常的，因为模板中的占位符需要替换为具体的公司名称。因此，这一部分符合要求。\n2.财务报表类型:\n\t模板内容：balance sheet, income statement, statement of cash flows, statement of changes in equity\n\t原文内容:consolidated and the Company's balance sheets, consolidated and the Company's income statements, statements of cash flows, statements of changes in equity\n\t判断依据:原文中增加了"consolidated and the Company's"这一表述,这表明原文不仅包括了公司的单独财务报表,还包括了合并财务报表。这一部分符合要求,但需要注意的是,模板中没有提到合并财务报表,这可能是额外的信息。\n3.时间范围:\n\t模板内容：as at 31 December 202X, for the year then ended\n\t原文内容:as at 31 December 2023, for the year then ended\n\t判断依据:原文中的具体年份（2023）替换了模板中的占位符（202X）,这是正常的。因此,这一部分符合要求。\n4.附注:\n\t模板内容:notes to the financial statements\n\t原文内容:notes to the financial statements \n\t判断依据:原文和模板内容完全一致，这一部分符合要求。",
          "审核结论":"审核通过"}}
         '''

    相关知识:
        背景：你是一个专业是文档复核专家
        目标：请仔细对比模板内容与原文内容是否匹配
        审核要求：模板内容如下：
        As part of an audit in accordance with CSAs, we exercise professional judgment and maintain professional skepticism throughout the audit. We also:​
        原文：As part of an audit in accordance with CSAs, we exercise professional judgment and maintain professional skepticism throughout the audit. We also:
    输出格式的说明:
        审核结论：基于以上分析，输出"审核通过"或"审核不通过"。
    输出：'''json
        {{"审核结论":"审核不通过"}}
        '''

{fewshots}

注意，只输出json结果，不要输出多余的内容。
""".strip()

    @staticmethod
    def format_prompt(task: str, knowledge: str, output: str, fewshots: str = "") -> str:
        """
        Format the prompt with the given parameters.

        Args:
            task (str): Task description
            knowledge (str): Background knowledge and requirements
            output (str): Output format specification
            fewshots (str, optional): Few-shot examples

        Returns:
            str: Formatted prompt
        """
        return DocumentAuditPrompt.get_base_template().format(
            task=task,
            knowledge=knowledge,
            output=output,
            fewshots=fewshots
        )

    @staticmethod
    def create_audit_prompt(prompt_config: Dict[str, Any], document_context: Dict[str, Any]) -> str:
        """
        Create an audit prompt from configuration and document context.
        
        Args:
            prompt_config (Dict[str, Any]): Prompt configuration containing task, knowledge, etc.
            document_context (Dict[str, Any]): Document context with main_doc, co_docs, etc.
            
        Returns:
            str: Generated audit prompt
        """
        try:
            # Extract prompt components
            task = prompt_config.get('任务', '无')
            task = adjust_indent(task, 1)
            
            knowledge_template = prompt_config.get('相关知识', '无')
            knowledge_template = adjust_indent(knowledge_template, 1)
            knowledge_template = re.sub(r'\{+', '{', knowledge_template)
            knowledge_template = re.sub(r'\}+', '}', knowledge_template)

            # Remove keys not in knowledge template
            docs = document_context.copy()
            keys_to_remove = [key for key in docs.keys() if key not in knowledge_template]
            for key in keys_to_remove:
                docs.pop(key)

            # Remove placeholders not in docs from template
            placeholders_in_template = re.findall(r'\{([^}]+)\}', knowledge_template)
            for placeholder in placeholders_in_template:
                if '.' in placeholder:
                    doc_name, doc_attr = placeholder.split(".", 1)
                    if (not docs.get(doc_name, None)) or (not getattr(docs[doc_name], doc_attr, None)):
                        knowledge_template = knowledge_template.replace(f'{{{placeholder}}}', '')

            # Format knowledge with document context
            knowledge = knowledge_template.format(**docs)
            
            # Process examples
            examples = prompt_config.get('fewshots', [])
            output = prompt_config.get('输出说明', '无')
            output = adjust_indent(output, 1)
            
            fewshots = []
            for i, fewshot in enumerate(examples):
                input_example = fewshot.get('input', '')
                context_example = fewshot.get('context', '')
                output_example = fewshot.get('output', '')
                fewshots.append(
                    '\n'.join([
                        f"示例{i+1}", 
                        f"输入:{input_example}", 
                        f"上下文:{context_example}", 
                        f"输出:{output_example}"
                    ])
                )
            
            if not fewshots:
                fewshots_text = ''
            else:
                fewshots_text = '\n\n'.join(fewshots)
                fewshots_text = adjust_indent(fewshots_text, 1)

            # Generate final prompt
            prompt = DocumentAuditPrompt.format_prompt(
                knowledge=knowledge, 
                output=output, 
                task=task, 
                fewshots=fewshots_text
            )
            
            logging.info(f"Generated prompt length: {len(prompt)}")
            logging.debug(f"Generated prompt: {prompt}")
            
            return prompt
            
        except Exception as e:
            logging.error(f"Error creating audit prompt: {e}")
            return f"Error generating prompt: {str(e)}"


def adjust_indent(text: str, num_spaces: int = 1) -> str:
    """
    Adjust indentation of text by adding tabs to each line.
    
    Args:
        text (str): Text to adjust
        num_spaces (int): Number of tab spaces to add
        
    Returns:
        str: Text with adjusted indentation
    """
    if not text:
        return ""
        
    lines = text.splitlines()
    return '\n'.join([('\t' * num_spaces) + line.lstrip() for line in lines])


def create_simple_audit_prompt(main_content: str, 
                              reference_content: str = "",
                              task_description: str = "文档审核") -> str:
    """
    Create a simple audit prompt for basic document review.
    
    Args:
        main_content (str): Main document content to audit
        reference_content (str): Reference content for comparison
        task_description (str): Description of the audit task
        
    Returns:
        str: Simple audit prompt
    """
    knowledge = f"""
背景：你是一个专业的文档审核专家
目标：请仔细审核以下文档内容
审核要求：
主要内容：{main_content}
{f"参考内容：{reference_content}" if reference_content else ""}
"""
    
    output = """
审核结论：基于分析，输出"审核通过"或"审核不通过"
分析：详细说明审核的依据和发现的问题
"""
    
    return DocumentAuditPrompt.format_prompt(
        task=task_description,
        knowledge=knowledge,
        output=output,
        fewshots=""
    )


# TODO: Add more prompt utilities:
# - Domain-specific prompt templates
# - Prompt optimization and testing
# - Multi-language prompt support
# - Dynamic prompt generation
# - Prompt performance analytics
