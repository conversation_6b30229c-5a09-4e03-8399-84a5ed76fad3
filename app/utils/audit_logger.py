"""
审计服务专用日志工具

提供标准化的日志记录接口，确保所有日志包含必要的上下文信息，
便于ELK系统进行解析、搜索和分析。

主要功能：
- 标准化日志格式
- 业务上下文自动注入
- 性能监控日志
- 错误追踪日志
- 审计事件日志
"""

import logging
import time
import uuid
from contextlib import contextmanager
from datetime import datetime
import pytz
from typing import Dict, Any, Optional, Union
from functools import wraps

from app.utils.elk_logging import setup_elk_logging, log_request_response, log_task_execution


class AuditLogger:
    """
    审计服务专用日志记录器
    
    提供结构化的日志记录功能，自动添加业务上下文信息，
    支持任务跟踪、性能监控、错误追踪等功能。
    """
    
    def __init__(self, name: str = __name__, **context):
        """
        初始化审计日志记录器
        
        Args:
            name: 日志记录器名称
            **context: 默认上下文信息
        """
        self.logger = setup_elk_logging(logger_name=name)
        self.default_context = context
        self._request_id = None
        self._task_id = None
        self._user_id = None
    
    def set_context(self, **context):
        """
        设置日志上下文信息
        
        Args:
            **context: 上下文键值对
        """
        self.default_context.update(context)
    
    def set_request_id(self, request_id: str):
        """设置请求ID"""
        self._request_id = request_id
        self.default_context['request_id'] = request_id
    
    def set_task_id(self, task_id: str):
        """设置任务ID"""
        self._task_id = task_id
        self.default_context['task_id'] = task_id
    
    def set_user_id(self, user_id: str):
        """设置用户ID"""
        self._user_id = user_id
        self.default_context['user_id'] = user_id
    
    def _log_with_context(self, level: str, message: str, **extra):
        """
        带上下文的日志记录
        
        Args:
            level: 日志级别
            message: 日志消息
            **extra: 额外的上下文信息
        """
        # 合并默认上下文和额外信息
        context = {**self.default_context, **extra}
        
        # 添加时间戳 (使用北京时间)
        beijing_tz = pytz.timezone('Asia/Shanghai')
        beijing_time = datetime.now(beijing_tz)
        context['log_timestamp'] = beijing_time.isoformat()
        
        # 记录日志
        getattr(self.logger, level.lower())(message, extra=context)
    
    def debug(self, message: str, **extra):
        """记录调试日志"""
        self._log_with_context('DEBUG', message, **extra)
    
    def info(self, message: str, **extra):
        """记录信息日志"""
        self._log_with_context('INFO', message, **extra)
    
    def warning(self, message: str, **extra):
        """记录警告日志"""
        self._log_with_context('WARNING', message, **extra)
    
    def error(self, message: str, **extra):
        """记录错误日志"""
        self._log_with_context('ERROR', message, **extra)
    
    def critical(self, message: str, **extra):
        """记录严重错误日志"""
        self._log_with_context('CRITICAL', message, **extra)
    
    def audit_event(self, event_type: str, description: str, **extra):
        """
        记录审计事件日志
        
        Args:
            event_type: 事件类型 (CREATE, UPDATE, DELETE, ACCESS, etc.)
            description: 事件描述
            **extra: 额外的事件信息
        """
        context = {
            'event_type': event_type,
            'event_description': description,
            'log_category': 'audit_event',
            **extra
        }
        self._log_with_context('INFO', f"审计事件: {event_type} - {description}", **context)
    
    def performance_log(self, operation: str, duration: float, **extra):
        """
        记录性能日志
        
        Args:
            operation: 操作名称
            duration: 执行时间（秒）
            **extra: 额外的性能信息
        """
        context = {
            'operation': operation,
            'duration_ms': round(duration * 1000, 2),
            'log_category': 'performance',
            **extra
        }
        
        # 根据执行时间判断日志级别
        if duration > 10:  # 超过10秒
            level = 'WARNING'
            message = f"性能警告: {operation} 执行时间过长 ({duration:.2f}s)"
        elif duration > 5:  # 超过5秒
            level = 'INFO'
            message = f"性能监控: {operation} 执行时间较长 ({duration:.2f}s)"
        else:
            level = 'DEBUG'
            message = f"性能监控: {operation} 执行完成 ({duration:.2f}s)"
        
        self._log_with_context(level, message, **context)
    
    def business_log(self, category: str, action: str, description: str, **extra):
        """
        记录业务日志
        
        Args:
            category: 业务分类 (document, task, user, etc.)
            action: 业务动作 (process, create, update, etc.)
            description: 业务描述
            **extra: 额外的业务信息
        """
        context = {
            'business_category': category,
            'business_action': action,
            'log_category': 'business',
            **extra
        }
        self._log_with_context('INFO', f"业务日志: {category}.{action} - {description}", **context)
    
    def error_with_traceback(self, message: str, exception: Exception, **extra):
        """
        记录带异常堆栈的错误日志
        
        Args:
            message: 错误消息
            exception: 异常对象
            **extra: 额外的错误信息
        """
        context = {
            'error_type': type(exception).__name__,
            'error_message': str(exception),
            'log_category': 'error',
            **extra
        }
        self.logger.error(message, exc_info=exception, extra=context)
    
    @contextmanager
    def operation_context(self, operation: str, **context):
        """
        操作上下文管理器，自动记录操作开始和结束
        
        Args:
            operation: 操作名称
            **context: 操作上下文
        """
        operation_id = str(uuid.uuid4())
        start_time = time.time()
        
        # 记录操作开始
        self.info(f"操作开始: {operation}", 
                 operation_id=operation_id, 
                 operation=operation,
                 log_category='operation_start',
                 **context)
        
        try:
            yield operation_id
            # 记录操作成功
            duration = time.time() - start_time
            self.performance_log(operation, duration, 
                               operation_id=operation_id,
                               status='success',
                               **context)
        except Exception as e:
            # 记录操作失败
            duration = time.time() - start_time
            self.error_with_traceback(f"操作失败: {operation}", e,
                                    operation_id=operation_id,
                                    operation=operation,
                                    duration_ms=round(duration * 1000, 2),
                                    status='failed',
                                    **context)
            raise


def get_audit_logger(name: str = __name__, **context) -> AuditLogger:
    """
    获取审计日志记录器实例
    
    Args:
        name: 日志记录器名称
        **context: 默认上下文信息
        
    Returns:
        AuditLogger: 审计日志记录器实例
    """
    return AuditLogger(name, **context)


def log_performance(operation_name: str = None):
    """
    性能监控装饰器，支持同步和异步函数

    Args:
        operation_name: 操作名称，默认使用函数名

    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            logger = get_audit_logger(func.__module__)
            op_name = operation_name or f"{func.__module__}.{func.__name__}"

            with logger.operation_context(op_name):
                return await func(*args, **kwargs)

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            logger = get_audit_logger(func.__module__)
            op_name = operation_name or f"{func.__module__}.{func.__name__}"

            with logger.operation_context(op_name):
                return func(*args, **kwargs)

        # 检查函数是否为异步函数
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    return decorator


def log_audit_event(event_type: str, description: str = None):
    """
    审计事件装饰器，支持同步和异步函数

    Args:
        event_type: 事件类型
        description: 事件描述，默认使用函数名

    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            logger = get_audit_logger(func.__module__)
            desc = description or f"执行函数 {func.__name__}"

            result = await func(*args, **kwargs)
            logger.audit_event(event_type, desc, function=func.__name__)
            return result

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            logger = get_audit_logger(func.__module__)
            desc = description or f"执行函数 {func.__name__}"

            result = func(*args, **kwargs)
            logger.audit_event(event_type, desc, function=func.__name__)
            return result

        # 检查函数是否为异步函数
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    return decorator
