# Application Settings
APP_NAME=Audit Service
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO

# Database Configuration
DATABASE_URL=mysql+pymysql://audit_user:audit_password@localhost:3306/audit_db
DB_HOST=localhost
DB_PORT=3306
DB_NAME=audit_db
DB_USER=audit_user
DB_PASSWORD=audit_password

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_TIMEZONE=UTC
CELERY_ENABLE_UTC=true

# File Storage
UPLOAD_DIR=./uploads
TEMP_DIR=./temp
MAX_FILE_SIZE=100MB

# External Services
EMBEDDING_API_URL=http://***********:8000/v1
EMBEDDING_API_KEY=EMPTY
EMBEDDING_MODEL=bge-m3
KBS_ADDRESS=http://***********:9159/query

# OCR Configuration
OCR_TYPE=paddle
USE_OCR=false

# Model Configuration
RELEVANCE_MODEL=qwen2-7b-8k-simple_tasks_v1
SUMMARY_MODEL=Qwen2-72B-Instruct-GPTQ-Int4
TEMPERATURE=1e-8

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=8001
