"""
智能文本核查服务设计方案

基于现有审计服务架构，新增智能文本核查功能模块。
采用三层处理架构：文档解析层 -> 原子任务处理层 -> 全局一致性校验层
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import hashlib
from datetime import datetime

class AuditType(Enum):
    """文本核查类型枚举"""
    BASIC_QUALITY = "basic_quality"              # 基础文本质量
    STRUCTURE_CONSISTENCY = "structure_consistency"  # 结构一致性
    EXTERNAL_COMPLIANCE = "external_compliance"   # 外部合规性
    CROSS_DOCUMENT_CONSISTENCY = "cross_document_consistency"  # 跨文档一致性

class BlockType(Enum):
    """文档块类型枚举"""
    PARAGRAPH = "paragraph"
    HEADER_1 = "header_1"
    HEADER_2 = "header_2" 
    HEADER_3 = "header_3"
    TABLE = "table"
    LIST_ITEM = "list_item"
    IMAGE_CAPTION = "image_caption"

@dataclass
class DocumentBlock:
    """文档块数据结构"""
    block_id: str
    page_number: int
    text_content: str
    block_type: BlockType
    coordinates: Optional[Dict[str, float]] = None  # {x, y, width, height}
    metadata: Optional[Dict[str, Any]] = None       # 字体、序号等
    parent_block_id: Optional[str] = None
    sequence_number: Optional[str] = None           # 章节序号

@dataclass
class ConsistencyFact:
    """一致性事实数据结构"""
    fact_key: str
    fact_value: str
    normalized_value: str
    exact_quote: str
    source_block_id: str
    source_page_number: int
    extraction_confidence: float = 0.0

@dataclass
class ContradictionGroup:
    """矛盾证据组数据结构"""
    fact_key: str
    error_message: str
    evidence_list: List[ConsistencyFact]
    group_hash: str

@dataclass
class TextAuditResult:
    """文本核查结果数据结构"""
    audit_type: AuditType
    error_type: str
    severity: str  # low, medium, high, critical
    error_message: str
    source_block_id: str
    source_page_number: int
    source_text: str
    suggestion: Optional[str] = None
    confidence_score: float = 0.0

class DocumentStructureParser:
    """文档结构解析器 - 第一层：文档解析层"""
    
    def __init__(self, task_id: str):
        self.task_id = task_id
        
    def parse_document_structure(self, document_path: str) -> List[DocumentBlock]:
        """
        解析文档结构，生成数字文档孪生体
        
        核心功能：
        1. 版面分析 - 识别文本块、标题、表格等元素
        2. 结构化提取 - 提取页码、序号、坐标等特征
        3. 层次化组织 - 构建文档的树状结构
        
        Args:
            document_path: 文档路径
            
        Returns:
            List[DocumentBlock]: 结构化的文档块列表
        """
        # 实现文档解析逻辑
        # 1. 使用现有的文档处理工具 (PyPDF2, python-docx等)
        # 2. 集成版面分析算法 (可使用开源工具如 pdfplumber)
        # 3. 提取结构化信息并生成DocumentBlock对象
        pass
        
    def extract_sequence_numbers(self, blocks: List[DocumentBlock]) -> Dict[str, str]:
        """提取所有标题的序号信息"""
        sequence_map = {}
        for block in blocks:
            if block.block_type in [BlockType.HEADER_1, BlockType.HEADER_2, BlockType.HEADER_3]:
                # 使用正则表达式提取序号
                # 例如: "1.2.3 标题内容" -> "1.2.3"
                sequence_map[block.block_id] = block.sequence_number
        return sequence_map

class AtomicTaskProcessor:
    """原子任务处理器 - 第二层：原子任务处理层"""
    
    def __init__(self, task_id: str, model_service):
        self.task_id = task_id
        self.model_service = model_service
        
    def process_basic_quality_check(self, blocks: List[DocumentBlock]) -> List[TextAuditResult]:
        """
        基础文本质量检查
        
        检查项目：
        - 错别字与乱码检测
        - 标点规范性检测  
        - 重复冗余检测
        - 语法逻辑检测
        - 漏字/漏词检测
        """
        results = []
        
        for block in blocks:
            # 构建优化的Prompt
            prompt = f"""
            你是一个专业的文档校对员，请检查以下文本块中的语法、拼写和标点错误。
            请以JSON格式返回错误列表，每个错误包含：
            - error_type: 错误类型 (typo/punctuation/grammar/redundancy/missing_word)
            - original_text: 原文片段
            - suggested_fix: 修改建议
            - confidence: 置信度 (0.0-1.0)
            
            文本内容：{block.text_content}
            """
            
            # 调用大模型
            response = self.model_service.get_chat_completion(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            )
            
            # 解析响应并生成结果
            errors = self._parse_quality_check_response(response, block)
            results.extend(errors)
            
        return results
        
    def process_structure_consistency_check(self, blocks: List[DocumentBlock]) -> List[TextAuditResult]:
        """
        结构一致性检查
        
        检查项目：
        - 编号一致性检测
        - 索引与目录一致性
        - 章节引用正确性
        """
        results = []
        
        # 提取所有序号
        sequence_numbers = []
        for block in blocks:
            if block.sequence_number:
                sequence_numbers.append(block.sequence_number)
                
        # 检查序号连续性
        prompt = f"""
        这是一个文档的标题序号列表，请检查其中是否存在跳号、错号或乱序的问题。
        序号列表：{sequence_numbers}
        
        请以JSON格式返回发现的问题，包含：
        - error_type: "sequence_gap" | "sequence_disorder" | "sequence_duplicate"
        - problematic_sequence: 有问题的序号
        - expected_sequence: 期望的序号
        - description: 问题描述
        """
        
        response = self.model_service.get_chat_completion(
            model="gpt-3.5-turbo", 
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1
        )
        
        # 解析响应并生成结果
        errors = self._parse_structure_check_response(response, blocks)
        results.extend(errors)
        
        return results
        
    def process_external_compliance_check(self, blocks: List[DocumentBlock]) -> List[TextAuditResult]:
        """
        外部合规性检查
        
        检查项目：
        - 工商信息一致性检测
        - 外部法规引用检测
        - 标准化表述检测
        """
        results = []
        
        # 提取公司信息并调用外部API验证
        for block in blocks:
            # 使用大模型提取公司信息
            company_info = self._extract_company_info(block)
            
            if company_info:
                # 调用外部API验证
                verification_result = self._verify_company_info(company_info)
                if not verification_result['is_consistent']:
                    results.append(TextAuditResult(
                        audit_type=AuditType.EXTERNAL_COMPLIANCE,
                        error_type="company_info_mismatch",
                        severity="high",
                        error_message=f"工商信息不一致: {verification_result['message']}",
                        source_block_id=block.block_id,
                        source_page_number=block.page_number,
                        source_text=block.text_content[:200],
                        confidence_score=verification_result['confidence']
                    ))
                    
        return results
        
    def _parse_quality_check_response(self, response: str, block: DocumentBlock) -> List[TextAuditResult]:
        """解析基础质量检查的模型响应"""
        # 实现JSON解析逻辑
        pass
        
    def _parse_structure_check_response(self, response: str, blocks: List[DocumentBlock]) -> List[TextAuditResult]:
        """解析结构检查的模型响应"""
        # 实现JSON解析逻辑
        pass
        
    def _extract_company_info(self, block: DocumentBlock) -> Optional[Dict[str, str]]:
        """从文档块中提取公司信息"""
        # 实现公司信息提取逻辑
        pass
        
    def _verify_company_info(self, company_info: Dict[str, str]) -> Dict[str, Any]:
        """调用外部API验证公司信息"""
        # 实现外部API调用逻辑
        pass

class GlobalConsistencyValidator:
    """全局一致性校验器 - 第三层：全局一致性校验层"""
    
    def __init__(self, task_id: str, model_service):
        self.task_id = task_id
        self.model_service = model_service
        self.universal_facts = self._load_universal_fact_library()
        
    def process_consistency_validation(self, blocks: List[DocumentBlock]) -> Tuple[List[ConsistencyFact], List[ContradictionGroup]]:
        """
        全局一致性校验主流程
        
        步骤：
        1. 动态事实发现
        2. 事实提取与归一化
        3. 矛盾检测与证据组生成
        """
        # 第一步：动态发现文档特有的事实
        dynamic_facts = self._discover_dynamic_facts(blocks)
        
        # 合并通用事实库和动态发现的事实
        all_fact_keys = list(self.universal_facts.keys()) + dynamic_facts
        
        # 第二步：事实提取
        extracted_facts = self._extract_facts(blocks, all_fact_keys)
        
        # 第三步：矛盾检测
        contradiction_groups = self._detect_contradictions(extracted_facts)
        
        return extracted_facts, contradiction_groups
        
    def _discover_dynamic_facts(self, blocks: List[DocumentBlock]) -> List[str]:
        """动态发现引擎 - 识别文档特有的关键指标"""
        candidate_facts = []
        
        # 第一遍扫描：候选事实识别
        for block in blocks:
            prompt = f"""
            分析以下文本，识别其中可能构成关键绩效指标(KPI)、核心业务数据、
            或重要量化承诺的短语。只提取那些看起来是公司特有且可能在后文重复提及的名词和数值。
            例如'A产品线毛利率'、'主要供应商B采购占比'。
            
            以JSON数组格式返回候选事实名称列表。
            
            文本内容：{block.text_content}
            """
            
            response = self.model_service.get_chat_completion(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            )
            
            # 解析候选事实
            block_candidates = json.loads(response)
            candidate_facts.extend(block_candidates)
            
        # 第二遍扫描：频率分析
        fact_frequency = {}
        for fact in candidate_facts:
            fact_frequency[fact] = fact_frequency.get(fact, 0) + 1
            
        # 筛选高频事实（出现3次以上）
        dynamic_facts = [fact for fact, freq in fact_frequency.items() if freq >= 3]
        
        return dynamic_facts
        
    def _extract_facts(self, blocks: List[DocumentBlock], fact_keys: List[str]) -> List[ConsistencyFact]:
        """事实提取与归一化"""
        extracted_facts = []
        
        for block in blocks:
            prompt = f"""
            你是一个精通财报和法律文书的分析师。请从以下文本中，
            根据规则列表提取关键信息。
            
            规则列表：{fact_keys}
            
            以JSON格式返回提取结果，每个结果包含：
            - fact_key: 事实类型
            - fact_value: 提取的原始值
            - normalized_value: 标准化值（金额转为不带逗号的数字，日期转为YYYY-MM-DD格式）
            - exact_quote: 包含该信息的完整句子
            - confidence: 提取置信度 (0.0-1.0)
            
            文本内容：{block.text_content}
            """
            
            response = self.model_service.get_chat_completion(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            )
            
            # 解析提取结果
            facts_data = json.loads(response)
            for fact_data in facts_data:
                fact = ConsistencyFact(
                    fact_key=fact_data['fact_key'],
                    fact_value=fact_data['fact_value'],
                    normalized_value=fact_data['normalized_value'],
                    exact_quote=fact_data['exact_quote'],
                    source_block_id=block.block_id,
                    source_page_number=block.page_number,
                    extraction_confidence=fact_data['confidence']
                )
                extracted_facts.append(fact)
                
        return extracted_facts
        
    def _detect_contradictions(self, facts: List[ConsistencyFact]) -> List[ContradictionGroup]:
        """矛盾检测 - 使用哈希映射高效比对"""
        # 构建哈希映射: fact_key -> normalized_value -> [facts]
        fact_map = {}
        for fact in facts:
            if fact.fact_key not in fact_map:
                fact_map[fact.fact_key] = {}
            if fact.normalized_value not in fact_map[fact.fact_key]:
                fact_map[fact.fact_key][fact.normalized_value] = []
            fact_map[fact.fact_key][fact.normalized_value].append(fact)
            
        # 识别矛盾
        contradiction_groups = []
        for fact_key, value_map in fact_map.items():
            if len(value_map) > 1:  # 存在不同的标准化值
                # 生成矛盾证据组
                all_evidence = []
                for value_list in value_map.values():
                    all_evidence.extend(value_list)
                    
                group_hash = hashlib.md5(f"{fact_key}_{len(all_evidence)}".encode()).hexdigest()
                
                contradiction_group = ContradictionGroup(
                    fact_key=fact_key,
                    error_message=f"文档中关于"{fact_key}"的表述存在不一致",
                    evidence_list=all_evidence,
                    group_hash=group_hash
                )
                contradiction_groups.append(contradiction_group)
                
        return contradiction_groups
        
    def _load_universal_fact_library(self) -> Dict[str, Dict[str, Any]]:
        """加载预置通用事实库"""
        # 从数据库加载通用事实库配置
        # 返回格式: {fact_key: {display_name, aliases, normalization_type, ...}}
        pass

class IntelligentTextAuditService:
    """智能文本核查服务 - 主服务类"""
    
    def __init__(self, task_id: str):
        self.task_id = task_id
        self.parser = DocumentStructureParser(task_id)
        self.atomic_processor = AtomicTaskProcessor(task_id, self._get_model_service())
        self.consistency_validator = GlobalConsistencyValidator(task_id, self._get_model_service())
        
    def process_text_audit(self, document_path: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        智能文本核查主流程
        
        Args:
            document_path: 文档路径
            config: 核查配置
            
        Returns:
            Dict: 完整的核查结果
        """
        results = {
            'task_id': self.task_id,
            'document_path': document_path,
            'audit_results': [],
            'contradiction_groups': [],
            'summary': {},
            'processing_time': 0
        }
        
        start_time = datetime.now()
        
        try:
            # 第一层：文档解析
            document_blocks = self.parser.parse_document_structure(document_path)
            
            # 第二层：原子任务处理
            if config.get('enable_basic_quality', True):
                basic_results = self.atomic_processor.process_basic_quality_check(document_blocks)
                results['audit_results'].extend(basic_results)
                
            if config.get('enable_structure_check', True):
                structure_results = self.atomic_processor.process_structure_consistency_check(document_blocks)
                results['audit_results'].extend(structure_results)
                
            if config.get('enable_external_compliance', False):
                compliance_results = self.atomic_processor.process_external_compliance_check(document_blocks)
                results['audit_results'].extend(compliance_results)
                
            # 第三层：全局一致性校验
            if config.get('enable_cross_document_check', True):
                facts, contradictions = self.consistency_validator.process_consistency_validation(document_blocks)
                results['contradiction_groups'] = contradictions
                
            # 生成汇总信息
            results['summary'] = self._generate_summary(results)
            
            # 保存结果到数据库
            self._save_results_to_database(results)
            
        except Exception as e:
            results['error'] = str(e)
            
        finally:
            end_time = datetime.now()
            results['processing_time'] = (end_time - start_time).total_seconds()
            
        return results
        
    def _get_model_service(self):
        """获取模型服务实例"""
        from app.services.model_service import get_model_service
        return get_model_service()
        
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成核查结果汇总"""
        audit_results = results.get('audit_results', [])
        contradiction_groups = results.get('contradiction_groups', [])
        
        summary = {
            'total_issues': len(audit_results) + len(contradiction_groups),
            'basic_quality_issues': len([r for r in audit_results if r.audit_type == AuditType.BASIC_QUALITY]),
            'structure_issues': len([r for r in audit_results if r.audit_type == AuditType.STRUCTURE_CONSISTENCY]),
            'compliance_issues': len([r for r in audit_results if r.audit_type == AuditType.EXTERNAL_COMPLIANCE]),
            'consistency_issues': len(contradiction_groups),
            'severity_distribution': {
                'critical': len([r for r in audit_results if r.severity == 'critical']),
                'high': len([r for r in audit_results if r.severity == 'high']),
                'medium': len([r for r in audit_results if r.severity == 'medium']),
                'low': len([r for r in audit_results if r.severity == 'low'])
            }
        }
        
        return summary
        
    def _save_results_to_database(self, results: Dict[str, Any]):
        """保存结果到数据库"""
        # 实现数据库保存逻辑
        # 1. 保存文档结构信息到 document_structure 表
        # 2. 保存核查结果到 text_audit_results 表  
        # 3. 保存一致性事实到 consistency_facts 表
        # 4. 保存矛盾证据组到 contradiction_groups 和 contradiction_evidence 表
        pass
