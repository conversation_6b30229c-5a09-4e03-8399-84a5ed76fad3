"""
文本核查异步任务设计

基于现有的Celery任务架构，新增文本核查相关的异步任务。
支持大文档的分块处理和长时间运行的核查任务。
"""

from celery import Celery
from celery.signals import task_prerun, task_postrun, task_failure
from typing import Dict, Any, Optional
import logging
import time
import json
from datetime import datetime

from app.core.config import get_settings
from app.services.task_service import TaskService
from app.core.database import get_db

settings = get_settings()
logger = logging.getLogger(__name__)

# Celery应用实例（基于现有配置）
celery_app = Celery(
    "text_audit_tasks",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend
)

# Celery配置
celery_app.conf.update(
    task_serializer=settings.celery_task_serializer,
    result_serializer=settings.celery_result_serializer,
    accept_content=settings.celery_accept_content,
    timezone=settings.celery_timezone,
    enable_utc=settings.celery_enable_utc,
    
    # 文本核查任务特定配置
    task_routes={
        'text_audit_tasks.process_text_audit_async': {'queue': 'text_audit'},
        'text_audit_tasks.process_document_structure_parsing': {'queue': 'document_parsing'},
        'text_audit_tasks.process_consistency_validation': {'queue': 'consistency_check'},
    },
    
    # 任务超时配置
    task_time_limit=3600,  # 1小时硬超时
    task_soft_time_limit=3000,  # 50分钟软超时
    
    # 重试配置
    task_default_retry_delay=60,  # 重试延迟60秒
    task_max_retries=3,
    
    # 结果过期时间
    result_expires=86400,  # 24小时
)

@celery_app.task(bind=True, name='text_audit_tasks.process_text_audit_async')
def process_text_audit_async(
    self,
    task_id: str,
    document_id: str,
    audit_config: Dict[str, Any],
    callback_url: Optional[str] = None
):
    """
    异步文本核查主任务
    
    协调整个文本核查流程的执行，包括文档解析、原子任务处理
    和全局一致性校验。支持进度跟踪和错误恢复。
    
    Args:
        task_id: 任务ID
        document_id: 文档ID
        audit_config: 核查配置
        callback_url: 回调URL
        
    Returns:
        Dict: 核查结果
    """
    try:
        logger.info(f"开始处理文本核查任务: {task_id}")
        
        # 更新任务状态为处理中
        with next(get_db()) as db:
            task_service = TaskService(db)
            task_service.update_task_status(task_id, "processing")
        
        # 获取文档路径
        document_path = _get_document_path_by_id(document_id)
        if not document_path:
            raise ValueError(f"文档ID {document_id} 对应的文件不存在")
        
        # 初始化进度跟踪
        progress_tracker = ProgressTracker(task_id, self)
        
        # 第一阶段：文档结构解析 (20%进度)
        progress_tracker.update_progress(5, "开始文档结构解析")
        
        parsing_result = process_document_structure_parsing.delay(
            task_id=task_id,
            document_path=document_path
        ).get(timeout=600)  # 10分钟超时
        
        if not parsing_result['success']:
            raise Exception(f"文档解析失败: {parsing_result['error']}")
            
        document_blocks = parsing_result['document_blocks']
        progress_tracker.update_progress(20, "文档结构解析完成")
        
        # 第二阶段：原子任务处理 (20% - 70%进度)
        atomic_results = []
        
        if audit_config.get('enable_basic_quality', True):
            progress_tracker.update_progress(25, "开始基础文本质量检查")
            quality_result = process_basic_quality_check.delay(
                task_id=task_id,
                document_blocks=document_blocks,
                config=audit_config
            ).get(timeout=1800)  # 30分钟超时
            atomic_results.extend(quality_result.get('issues', []))
            progress_tracker.update_progress(40, "基础文本质量检查完成")
        
        if audit_config.get('enable_structure_check', True):
            progress_tracker.update_progress(45, "开始结构一致性检查")
            structure_result = process_structure_consistency_check.delay(
                task_id=task_id,
                document_blocks=document_blocks,
                config=audit_config
            ).get(timeout=900)  # 15分钟超时
            atomic_results.extend(structure_result.get('issues', []))
            progress_tracker.update_progress(55, "结构一致性检查完成")
        
        if audit_config.get('enable_external_compliance', False):
            progress_tracker.update_progress(60, "开始外部合规性检查")
            compliance_result = process_external_compliance_check.delay(
                task_id=task_id,
                document_blocks=document_blocks,
                config=audit_config
            ).get(timeout=1200)  # 20分钟超时
            atomic_results.extend(compliance_result.get('issues', []))
            progress_tracker.update_progress(70, "外部合规性检查完成")
        
        # 第三阶段：全局一致性校验 (70% - 90%进度)
        contradiction_groups = []
        if audit_config.get('enable_cross_document_check', True):
            progress_tracker.update_progress(75, "开始全局一致性校验")
            consistency_result = process_consistency_validation.delay(
                task_id=task_id,
                document_blocks=document_blocks,
                config=audit_config
            ).get(timeout=1800)  # 30分钟超时
            contradiction_groups = consistency_result.get('contradiction_groups', [])
            progress_tracker.update_progress(90, "全局一致性校验完成")
        
        # 第四阶段：结果汇总和保存 (90% - 100%进度)
        progress_tracker.update_progress(95, "生成核查报告")
        
        final_result = {
            'task_id': task_id,
            'document_id': document_id,
            'audit_issues': atomic_results,
            'contradiction_groups': contradiction_groups,
            'summary': _generate_audit_summary(atomic_results, contradiction_groups),
            'processing_time': time.time() - self.request.called_directly,
            'completed_at': datetime.utcnow().isoformat()
        }
        
        # 保存结果到数据库
        _save_audit_results_to_database(final_result)
        
        # 更新任务状态为完成
        with next(get_db()) as db:
            task_service = TaskService(db)
            task_service.update_task_status(
                task_id,
                "completed",
                result=final_result
            )
        
        progress_tracker.update_progress(100, "文本核查任务完成")
        
        # 发送回调通知
        if callback_url:
            _send_callback_notification(callback_url, task_id, final_result)
        
        logger.info(f"文本核查任务完成: {task_id}")
        return final_result
        
    except Exception as e:
        logger.error(f"文本核查任务失败: {task_id}, 错误: {str(e)}")
        
        # 更新任务状态为失败
        with next(get_db()) as db:
            task_service = TaskService(db)
            task_service.update_task_status(
                task_id,
                "failed",
                exception_info=str(e)
            )
        
        # 发送失败回调
        if callback_url:
            _send_failure_callback(callback_url, task_id, str(e))
        
        raise

@celery_app.task(bind=True, name='text_audit_tasks.process_document_structure_parsing')
def process_document_structure_parsing(self, task_id: str, document_path: str):
    """
    文档结构解析子任务
    
    负责将文档解析为结构化的文档块，提取页码、序号、坐标等特征。
    
    Args:
        task_id: 任务ID
        document_path: 文档路径
        
    Returns:
        Dict: 解析结果
    """
    try:
        logger.info(f"开始文档结构解析: {task_id}")
        
        from design_docs.text_audit_service_design import DocumentStructureParser
        
        parser = DocumentStructureParser(task_id)
        document_blocks = parser.parse_document_structure(document_path)
        
        # 将结果序列化为可传输的格式
        serialized_blocks = [
            {
                'block_id': block.block_id,
                'page_number': block.page_number,
                'text_content': block.text_content,
                'block_type': block.block_type.value,
                'coordinates': block.coordinates,
                'metadata': block.metadata,
                'parent_block_id': block.parent_block_id,
                'sequence_number': block.sequence_number
            }
            for block in document_blocks
        ]
        
        logger.info(f"文档结构解析完成: {task_id}, 解析出 {len(serialized_blocks)} 个文档块")
        
        return {
            'success': True,
            'document_blocks': serialized_blocks,
            'total_blocks': len(serialized_blocks)
        }
        
    except Exception as e:
        logger.error(f"文档结构解析失败: {task_id}, 错误: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }

@celery_app.task(bind=True, name='text_audit_tasks.process_basic_quality_check')
def process_basic_quality_check(self, task_id: str, document_blocks: list, config: Dict[str, Any]):
    """
    基础文本质量检查子任务
    
    检查错别字、标点、语法、重复内容等基础文本质量问题。
    
    Args:
        task_id: 任务ID
        document_blocks: 文档块列表
        config: 检查配置
        
    Returns:
        Dict: 检查结果
    """
    try:
        logger.info(f"开始基础文本质量检查: {task_id}")
        
        from design_docs.text_audit_service_design import AtomicTaskProcessor, DocumentBlock, BlockType
        
        # 反序列化文档块
        blocks = [
            DocumentBlock(
                block_id=block['block_id'],
                page_number=block['page_number'],
                text_content=block['text_content'],
                block_type=BlockType(block['block_type']),
                coordinates=block.get('coordinates'),
                metadata=block.get('metadata'),
                parent_block_id=block.get('parent_block_id'),
                sequence_number=block.get('sequence_number')
            )
            for block in document_blocks
        ]
        
        # 执行质量检查
        processor = AtomicTaskProcessor(task_id, _get_model_service())
        quality_results = processor.process_basic_quality_check(blocks)
        
        # 序列化结果
        serialized_results = [
            {
                'audit_type': result.audit_type.value,
                'error_type': result.error_type,
                'severity': result.severity,
                'error_message': result.error_message,
                'source_block_id': result.source_block_id,
                'source_page_number': result.source_page_number,
                'source_text': result.source_text,
                'suggestion': result.suggestion,
                'confidence_score': result.confidence_score
            }
            for result in quality_results
        ]
        
        logger.info(f"基础文本质量检查完成: {task_id}, 发现 {len(serialized_results)} 个问题")
        
        return {
            'success': True,
            'issues': serialized_results,
            'total_issues': len(serialized_results)
        }
        
    except Exception as e:
        logger.error(f"基础文本质量检查失败: {task_id}, 错误: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'issues': []
        }

@celery_app.task(bind=True, name='text_audit_tasks.process_structure_consistency_check')
def process_structure_consistency_check(self, task_id: str, document_blocks: list, config: Dict[str, Any]):
    """
    结构一致性检查子任务
    
    检查编号连续性、索引对应、章节引用等结构一致性问题。
    """
    try:
        logger.info(f"开始结构一致性检查: {task_id}")
        
        # 实现结构一致性检查逻辑
        # 类似于 process_basic_quality_check 的实现模式
        
        return {
            'success': True,
            'issues': [],  # 实际实现中返回检查结果
            'total_issues': 0
        }
        
    except Exception as e:
        logger.error(f"结构一致性检查失败: {task_id}, 错误: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'issues': []
        }

@celery_app.task(bind=True, name='text_audit_tasks.process_external_compliance_check')
def process_external_compliance_check(self, task_id: str, document_blocks: list, config: Dict[str, Any]):
    """
    外部合规性检查子任务
    
    检查工商信息、法规引用等外部合规性问题。
    """
    try:
        logger.info(f"开始外部合规性检查: {task_id}")
        
        # 实现外部合规性检查逻辑
        
        return {
            'success': True,
            'issues': [],
            'total_issues': 0
        }
        
    except Exception as e:
        logger.error(f"外部合规性检查失败: {task_id}, 错误: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'issues': []
        }

@celery_app.task(bind=True, name='text_audit_tasks.process_consistency_validation')
def process_consistency_validation(self, task_id: str, document_blocks: list, config: Dict[str, Any]):
    """
    全局一致性校验子任务
    
    执行动态事实发现和一致性校验，生成矛盾证据组。
    """
    try:
        logger.info(f"开始全局一致性校验: {task_id}")
        
        # 实现一致性校验逻辑
        
        return {
            'success': True,
            'contradiction_groups': [],
            'total_contradictions': 0
        }
        
    except Exception as e:
        logger.error(f"全局一致性校验失败: {task_id}, 错误: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'contradiction_groups': []
        }

class ProgressTracker:
    """任务进度跟踪器"""
    
    def __init__(self, task_id: str, celery_task):
        self.task_id = task_id
        self.celery_task = celery_task
        
    def update_progress(self, percentage: int, message: str):
        """更新任务进度"""
        try:
            # 更新Celery任务状态
            self.celery_task.update_state(
                state='PROGRESS',
                meta={
                    'current': percentage,
                    'total': 100,
                    'message': message,
                    'timestamp': datetime.utcnow().isoformat()
                }
            )
            
            # 记录日志
            logger.info(f"任务进度更新: {self.task_id} - {percentage}% - {message}")
            
        except Exception as e:
            logger.error(f"更新任务进度失败: {self.task_id}, 错误: {str(e)}")

# 辅助函数
def _get_document_path_by_id(document_id: str) -> Optional[str]:
    """根据文档ID获取文档路径"""
    # 实现文档路径获取逻辑
    pass

def _get_model_service():
    """获取模型服务实例"""
    from app.services.model_service import get_model_service
    return get_model_service()

def _generate_audit_summary(atomic_results: list, contradiction_groups: list) -> Dict[str, Any]:
    """生成核查结果汇总"""
    # 实现汇总逻辑
    pass

def _save_audit_results_to_database(result: Dict[str, Any]):
    """保存核查结果到数据库"""
    # 实现数据库保存逻辑
    pass

def _send_callback_notification(callback_url: str, task_id: str, result: Dict[str, Any]):
    """发送成功回调通知"""
    # 实现回调通知逻辑
    pass

def _send_failure_callback(callback_url: str, task_id: str, error_message: str):
    """发送失败回调通知"""
    # 实现失败回调逻辑
    pass

# Celery信号处理
@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    """任务开始前的处理"""
    logger.info(f"任务开始执行: {task_id} - {task.name}")

@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, retval=None, state=None, **kwds):
    """任务完成后的处理"""
    logger.info(f"任务执行完成: {task_id} - {task.name} - 状态: {state}")

@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwds):
    """任务失败时的处理"""
    logger.error(f"任务执行失败: {task_id} - 异常: {str(exception)}")
    logger.error(f"错误堆栈: {traceback}")

if __name__ == '__main__':
    # 启动Celery Worker
    celery_app.start()
