# 智能文本核查系统部署和集成方案

## 🚀 部署架构设计

### 整体部署架构
```
┌─────────────────────────────────────────────────────────────┐
│                    负载均衡器 (Nginx)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                  API网关层                                   │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   现有审计API    │  │  新增文本核查API │                   │
│  │   (audit.py)    │  │  (text_audit.py)│                   │
│  └─────────────────┘  └─────────────────┘                   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                   服务层                                     │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   现有审计服务   │  │  智能文本核查服务 │                   │
│  │  AuditService   │  │ TextAuditService │                   │
│  └─────────────────┘  └─────────────────┘                   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                  异步任务层                                  │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   现有Celery     │  │  文本核查任务队列 │                   │
│  │   任务队列       │  │  (text_audit)   │                   │
│  └─────────────────┘  └─────────────────┘                   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                   数据存储层                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐   │
│  │     MySQL       │  │      Redis      │  │  文件存储    │   │
│  │   (扩展表结构)   │  │   (缓存/队列)   │  │  (文档文件)  │   │
│  └─────────────────┘  └─────────────────┘  └─────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 📦 Docker容器化部署

### 新增Docker服务配置

#### 1. 扩展现有docker-compose.yml
```yaml
# 在现有docker-compose.yml基础上新增以下服务

services:
  # 文本核查专用Worker
  text-audit-worker:
    build: .
    command: celery -A app.tasks.celery_tasks worker --loglevel=info --queues=text_audit,document_parsing,consistency_check
    volumes:
      - ./uploads:/app/uploads
      - ./temp:/app/temp
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
    environment:
      - DATABASE_URL=mysql+pymysql://audit_user:audit_password@mysql:3306/audit_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    networks:
      - audit-network
    restart: unless-stopped
    
  # 文档处理服务 (可选的独立服务)
  document-processor:
    build: 
      context: .
      dockerfile: docker/dockerfiles/Dockerfile.document-processor
    volumes:
      - ./uploads:/app/uploads
      - ./temp:/app/temp
    environment:
      - OCR_TYPE=paddle
      - USE_OCR=true
    networks:
      - audit-network
    restart: unless-stopped
    
  # 版面分析服务 (可选的独立服务)
  layout-analyzer:
    image: layoutlm/layoutlm-service:latest
    ports:
      - "8002:8000"
    networks:
      - audit-network
    restart: unless-stopped

# 扩展现有网络和卷配置
networks:
  audit-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
  uploads_data:
  temp_data:
```

#### 2. 新增专用Dockerfile
```dockerfile
# docker/dockerfiles/Dockerfile.document-processor
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    poppler-utils \
    tesseract-ocr \
    tesseract-ocr-chi-sim \
    libmagic1 \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 安装额外的文档处理依赖
RUN pip install --no-cache-dir \
    pdfplumber \
    layoutparser \
    paddlepaddle \
    paddleocr

WORKDIR /app
COPY . .

CMD ["python", "-m", "app.services.document_processor_service"]
```

## 🔧 数据库迁移方案

### 1. Alembic迁移脚本
```python
# migrations/versions/add_text_audit_tables.py
"""Add text audit tables

Revision ID: text_audit_001
Revises: previous_revision
Create Date: 2025-08-04 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers
revision = 'text_audit_001'
down_revision = 'previous_revision'
branch_labels = None
depends_on = None

def upgrade():
    # 创建文档结构化信息表
    op.create_table('document_structure',
        sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
        sa.Column('task_id', sa.String(255), nullable=False),
        sa.Column('document_id', sa.String(255), nullable=False),
        sa.Column('block_id', sa.String(255), nullable=False),
        sa.Column('page_number', sa.Integer(), nullable=False),
        sa.Column('block_type', sa.Enum('paragraph', 'header_1', 'header_2', 'header_3', 'table', 'list_item', 'image_caption'), nullable=False),
        sa.Column('text_content', sa.Text(), nullable=False),
        sa.Column('coordinates', sa.JSON(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('parent_block_id', sa.String(255), nullable=True),
        sa.Column('sequence_number', sa.String(50), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['task_id'], ['request_tracking.task_id'], ondelete='CASCADE'),
        sa.Index('idx_task_id', 'task_id'),
        sa.Index('idx_document_id', 'document_id'),
        sa.Index('idx_block_id', 'block_id'),
        sa.Index('idx_page_number', 'page_number'),
        sa.Index('idx_block_type', 'block_type')
    )
    
    # 创建其他表...
    # (省略其他表的创建代码，参考database_schema_extension.sql)

def downgrade():
    op.drop_table('contradiction_evidence')
    op.drop_table('contradiction_groups')
    op.drop_table('consistency_facts')
    op.drop_table('text_audit_config')
    op.drop_table('text_audit_results')
    op.drop_table('document_structure')
    op.drop_table('universal_fact_library')
```

### 2. 数据库初始化脚本
```bash
#!/bin/bash
# scripts/init_text_audit_db.sh

echo "开始初始化文本核查数据库..."

# 运行数据库迁移
alembic upgrade head

# 插入预置通用事实库数据
mysql -h ${DB_HOST} -u ${DB_USER} -p${DB_PASSWORD} ${DB_NAME} < design_docs/database_schema_extension.sql

echo "文本核查数据库初始化完成"
```

## 🔄 与现有系统集成

### 1. API路由集成
```python
# app/main.py 中添加新的路由
from app.api.v1 import audit, health, text_audit  # 新增text_audit

# 现有路由保持不变
app.include_router(
    audit.router,
    prefix="/api/v1",
    tags=["audit"]
)

# 新增文本核查路由
app.include_router(
    text_audit.router,
    prefix="/api/v1", 
    tags=["text-audit"]
)
```

### 2. 服务层集成
```python
# app/services/__init__.py 中注册新服务
from .audit_service import AuditService
from .text_audit_service import IntelligentTextAuditService  # 新增

# 服务工厂函数
def get_audit_service():
    return AuditService()

def get_text_audit_service(task_id: str):
    return IntelligentTextAuditService(task_id)
```

### 3. 任务队列集成
```python
# celery_app.py 中集成新的任务模块
from app.tasks.celery_tasks import celery_app as existing_celery
from design_docs.text_audit_celery_tasks import celery_app as text_audit_celery

# 合并任务注册
existing_celery.autodiscover_tasks([
    'app.tasks',
    'design_docs'  # 新增文本核查任务模块
])
```

## 🚀 部署步骤

### 1. 开发环境部署
```bash
# 1. 更新代码库
git pull origin main

# 2. 安装新依赖
pip install -r requirements.txt
pip install pdfplumber layoutparser paddleocr

# 3. 数据库迁移
alembic upgrade head
bash scripts/init_text_audit_db.sh

# 4. 启动服务
docker-compose up -d

# 5. 启动文本核查Worker
celery -A app.tasks.celery_tasks worker --loglevel=info --queues=text_audit
```

### 2. 生产环境部署
```bash
# 1. 备份现有数据
mysqldump -u root -p audit_db > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 蓝绿部署策略
# 部署到绿色环境
docker-compose -f docker-compose.prod.yml up -d --scale app=2

# 3. 数据库迁移
docker-compose exec app alembic upgrade head

# 4. 健康检查
curl http://localhost:8000/api/v1/health

# 5. 切换流量
# 更新负载均衡器配置，将流量切换到新版本

# 6. 监控和验证
# 监控日志和指标，确保新功能正常工作
```

## 📊 监控和运维

### 1. 新增监控指标
```python
# app/utils/metrics.py 中添加文本核查相关指标
from prometheus_client import Counter, Histogram, Gauge

# 文本核查任务指标
text_audit_tasks_total = Counter(
    'text_audit_tasks_total',
    'Total number of text audit tasks',
    ['status', 'audit_type']
)

text_audit_duration = Histogram(
    'text_audit_duration_seconds',
    'Time spent on text audit tasks',
    ['audit_type']
)

text_audit_issues_found = Counter(
    'text_audit_issues_found_total',
    'Total number of issues found',
    ['issue_type', 'severity']
)

document_blocks_processed = Counter(
    'document_blocks_processed_total',
    'Total number of document blocks processed'
)
```

### 2. 日志配置扩展
```yaml
# elk/logstash/pipeline/text-audit.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "text-audit" {
    grok {
      match => { 
        "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{DATA:component} %{GREEDYDATA:message}" 
      }
    }
    
    if [message] =~ /task_id/ {
      grok {
        match => { 
          "message" => "task_id=(?<task_id>[a-f0-9-]+)" 
        }
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "text-audit-logs-%{+YYYY.MM.dd}"
  }
}
```

### 3. 健康检查扩展
```python
# app/api/v1/health.py 中添加文本核查健康检查
@router.get("/health/text-audit")
async def text_audit_health_check():
    """文本核查系统健康检查"""
    health_status = {
        "text_audit_service": "healthy",
        "document_processor": "healthy", 
        "model_service": "healthy",
        "celery_workers": "healthy"
    }
    
    try:
        # 检查文本核查服务
        # 检查文档处理器
        # 检查模型服务连接
        # 检查Celery Worker状态
        
        return {
            "status": "healthy",
            "components": health_status,
            "timestamp": datetime.utcnow()
        }
    except Exception as e:
        return {
            "status": "unhealthy", 
            "error": str(e),
            "timestamp": datetime.utcnow()
        }
```

## 🔒 安全考虑

### 1. 文件上传安全
- 文件类型白名单验证
- 文件大小限制
- 病毒扫描集成
- 文件存储隔离

### 2. API安全
- 请求频率限制
- 文件访问权限控制
- 敏感信息脱敏
- 审计日志记录

### 3. 数据安全
- 数据库连接加密
- 敏感数据字段加密
- 定期数据备份
- 访问权限控制

## 📈 性能优化

### 1. 缓存策略
- Redis缓存文档解析结果
- 缓存通用事实库数据
- 缓存模型推理结果

### 2. 异步处理优化
- 任务队列分离
- 批量处理优化
- 资源池管理

### 3. 数据库优化
- 索引优化
- 分区表设计
- 查询性能监控

这个部署和集成方案确保了新的智能文本核查功能能够无缝集成到现有系统中，同时保持系统的稳定性和可扩展性。
