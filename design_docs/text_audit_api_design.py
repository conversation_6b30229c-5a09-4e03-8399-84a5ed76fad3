"""
智能文本核查API接口设计

基于现有的audit API架构，新增文本核查相关的接口端点。
保持与现有API的一致性和兼容性。
"""

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

from app.core.database import get_db
from app.services.task_service import TaskService
from app.models.schemas import TaskStatus

# 新增数据模型
class TextAuditType(str, Enum):
    """文本核查类型枚举"""
    BASIC_QUALITY = "basic_quality"
    STRUCTURE_CONSISTENCY = "structure_consistency" 
    EXTERNAL_COMPLIANCE = "external_compliance"
    CROSS_DOCUMENT_CONSISTENCY = "cross_document_consistency"
    COMPREHENSIVE = "comprehensive"  # 全面核查

class TextAuditConfig(BaseModel):
    """文本核查配置模型"""
    enable_basic_quality: bool = Field(True, description="启用基础文本质量检查")
    enable_structure_check: bool = Field(True, description="启用结构一致性检查")
    enable_external_compliance: bool = Field(False, description="启用外部合规性检查")
    enable_cross_document_check: bool = Field(True, description="启用跨文档一致性检查")
    enable_dynamic_fact_discovery: bool = Field(True, description="启用动态事实发现")
    
    # 高级配置选项
    custom_fact_keys: Optional[List[str]] = Field(None, description="用户自定义事实类型列表")
    external_api_config: Optional[Dict[str, Any]] = Field(None, description="外部API配置")
    quality_check_strictness: str = Field("medium", description="质量检查严格程度: low/medium/high")
    min_confidence_threshold: float = Field(0.7, description="最小置信度阈值")

class TextAuditRequest(BaseModel):
    """文本核查请求模型"""
    task_name: str = Field(..., description="任务名称")
    audit_type: TextAuditType = Field(TextAuditType.COMPREHENSIVE, description="核查类型")
    config: TextAuditConfig = Field(default_factory=TextAuditConfig, description="核查配置")
    callback_url: Optional[str] = Field(None, description="异步处理完成后的回调URL")

class ContradictionEvidence(BaseModel):
    """矛盾证据模型"""
    fact_value: str = Field(..., description="事实原始值")
    normalized_value: str = Field(..., description="标准化值")
    exact_quote: str = Field(..., description="原文引用")
    page_number: int = Field(..., description="页码")
    block_id: str = Field(..., description="文档块ID")
    confidence: float = Field(..., description="提取置信度")

class ContradictionGroup(BaseModel):
    """矛盾证据组模型"""
    fact_key: str = Field(..., description="事实类型")
    error_message: str = Field(..., description="错误描述")
    evidence_count: int = Field(..., description="证据数量")
    evidence_list: List[ContradictionEvidence] = Field(..., description="证据列表")

class TextAuditIssue(BaseModel):
    """文本核查问题模型"""
    audit_type: str = Field(..., description="核查类型")
    error_type: str = Field(..., description="错误类型")
    severity: str = Field(..., description="严重程度")
    error_message: str = Field(..., description="错误描述")
    source_page: int = Field(..., description="源页码")
    source_text: str = Field(..., description="源文本片段")
    suggestion: Optional[str] = Field(None, description="修改建议")
    confidence_score: float = Field(..., description="置信度分数")

class TextAuditSummary(BaseModel):
    """文本核查汇总模型"""
    total_issues: int = Field(..., description="问题总数")
    basic_quality_issues: int = Field(..., description="基础质量问题数")
    structure_issues: int = Field(..., description="结构问题数")
    compliance_issues: int = Field(..., description="合规问题数")
    consistency_issues: int = Field(..., description="一致性问题数")
    severity_distribution: Dict[str, int] = Field(..., description="严重程度分布")

class TextAuditResponse(BaseModel):
    """文本核查响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    message: str = Field(..., description="响应消息")
    
    # 核查结果数据（仅在完成时返回）
    audit_issues: Optional[List[TextAuditIssue]] = Field(None, description="核查问题列表")
    contradiction_groups: Optional[List[ContradictionGroup]] = Field(None, description="矛盾证据组列表")
    summary: Optional[TextAuditSummary] = Field(None, description="核查结果汇总")
    processing_time: Optional[float] = Field(None, description="处理耗时（秒）")

class DocumentUploadResponse(BaseModel):
    """文档上传响应模型"""
    document_id: str = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小（字节）")
    upload_time: datetime = Field(..., description="上传时间")
    message: str = Field(..., description="上传状态消息")

# API路由定义
router = APIRouter()

@router.post("/text-audit/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """
    上传待核查文档
    
    支持的文件格式：
    - PDF文档 (.pdf)
    - Word文档 (.docx, .doc)
    - Excel文档 (.xlsx, .xls)
    - 图片文件 (.jpg, .jpeg, .png)
    
    Args:
        file: 上传的文件
        db: 数据库会话
        
    Returns:
        DocumentUploadResponse: 上传结果
    """
    try:
        # 验证文件类型和大小
        if not _is_supported_file_type(file.filename):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的文件类型"
            )
            
        # 保存文件并生成文档ID
        document_id = await _save_uploaded_file(file)
        
        return DocumentUploadResponse(
            document_id=document_id,
            filename=file.filename,
            file_size=file.size,
            upload_time=datetime.utcnow(),
            message="文档上传成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文档上传失败: {str(e)}"
        )

@router.post("/text-audit/async", response_model=TextAuditResponse)
async def text_audit_async(
    request: TextAuditRequest,
    document_id: str,
    db: Session = Depends(get_db)
):
    """
    提交异步文本核查任务
    
    将文本核查任务提交到Celery队列进行异步处理，适用于大型文档
    或复杂核查规则的场景。
    
    Args:
        request: 文本核查请求
        document_id: 文档ID（通过upload接口获得）
        db: 数据库会话
        
    Returns:
        TextAuditResponse: 任务提交响应
    """
    try:
        task_service = TaskService(db)
        
        # 创建任务记录
        task_id = task_service.create_task(
            task_name=request.task_name,
            config=request.config.dict(),  # 将配置序列化为字符串
            main_doc=document_id
        )
        
        # 提交到Celery队列
        from app.tasks.celery_tasks import process_text_audit_async
        process_text_audit_async.delay(
            task_id=task_id,
            document_id=document_id,
            audit_config=request.config.dict(),
            callback_url=request.callback_url
        )
        
        return TextAuditResponse(
            task_id=task_id,
            status=TaskStatus.PENDING,
            message="文本核查任务已提交处理"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"提交文本核查任务失败: {str(e)}"
        )

@router.post("/text-audit/sync", response_model=TextAuditResponse)
async def text_audit_sync(
    request: TextAuditRequest,
    document_id: str,
    db: Session = Depends(get_db)
):
    """
    同步文本核查处理
    
    立即处理文本核查任务并返回结果，适用于小型文档或简单
    核查规则的场景。
    
    Args:
        request: 文本核查请求
        document_id: 文档ID
        db: 数据库会话
        
    Returns:
        TextAuditResponse: 核查结果响应
    """
    try:
        task_service = TaskService(db)
        
        # 创建任务记录
        task_id = task_service.create_task(
            task_name=request.task_name,
            config=request.config.dict(),
            main_doc=document_id
        )
        
        # 更新状态为处理中
        task_service.update_task_status(task_id, "processing")
        
        try:
            # 同步执行文本核查
            from design_docs.text_audit_service_design import IntelligentTextAuditService
            
            audit_service = IntelligentTextAuditService(task_id)
            document_path = _get_document_path(document_id)
            
            result = audit_service.process_text_audit(
                document_path=document_path,
                config=request.config.dict()
            )
            
            # 更新任务状态为完成
            task_service.update_task_status(
                task_id,
                "completed", 
                result=result
            )
            
            # 转换结果格式
            response_data = _convert_audit_result_to_response(result)
            
            return TextAuditResponse(
                task_id=task_id,
                status=TaskStatus.COMPLETED,
                message="文本核查完成",
                **response_data
            )
            
        except Exception as e:
            # 更新任务状态为失败
            task_service.update_task_status(
                task_id,
                "failed",
                exception_info=str(e)
            )
            raise
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文本核查处理失败: {str(e)}"
        )

@router.get("/text-audit/status/{task_id}", response_model=TextAuditResponse)
async def get_text_audit_status(
    task_id: str,
    db: Session = Depends(get_db)
):
    """
    查询文本核查任务状态
    
    Args:
        task_id: 任务ID
        db: 数据库会话
        
    Returns:
        TextAuditResponse: 任务状态和结果
    """
    try:
        task_service = TaskService(db)
        task_status = task_service.get_task_status(task_id)
        
        if not task_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务 {task_id} 不存在"
            )
            
        response_data = {
            "task_id": task_id,
            "status": task_status.status,
            "message": _get_status_message(task_status.status)
        }
        
        # 如果任务已完成，包含结果数据
        if task_status.status == TaskStatus.COMPLETED and task_status.request_result:
            result_data = _convert_audit_result_to_response(task_status.request_result)
            response_data.update(result_data)
            
        return TextAuditResponse(**response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询任务状态失败: {str(e)}"
        )

@router.get("/text-audit/contradiction-details/{task_id}")
async def get_contradiction_details(
    task_id: str,
    fact_key: str,
    db: Session = Depends(get_db)
):
    """
    获取特定矛盾证据组的详细信息
    
    用于前端展示矛盾证据的详细对比信息，支持原文溯源。
    
    Args:
        task_id: 任务ID
        fact_key: 事实类型键
        db: 数据库会话
        
    Returns:
        Dict: 矛盾证据详情
    """
    try:
        # 从数据库查询矛盾证据组详情
        contradiction_details = _get_contradiction_details_from_db(task_id, fact_key, db)
        
        if not contradiction_details:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="未找到指定的矛盾证据组"
            )
            
        return contradiction_details
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取矛盾详情失败: {str(e)}"
        )

@router.get("/text-audit/universal-facts")
async def get_universal_fact_library():
    """
    获取预置通用事实库
    
    返回系统预置的通用事实类型列表，供用户了解系统能够
    自动检查的事实类型。
    
    Returns:
        Dict: 通用事实库信息
    """
    try:
        # 从数据库加载通用事实库
        universal_facts = _load_universal_facts_from_db()
        
        return {
            "total_facts": len(universal_facts),
            "categories": _group_facts_by_category(universal_facts),
            "facts": universal_facts
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取通用事实库失败: {str(e)}"
        )

# 辅助函数
def _is_supported_file_type(filename: str) -> bool:
    """检查文件类型是否支持"""
    supported_extensions = {'.pdf', '.docx', '.doc', '.xlsx', '.xls', '.jpg', '.jpeg', '.png'}
    return any(filename.lower().endswith(ext) for ext in supported_extensions)

async def _save_uploaded_file(file: UploadFile) -> str:
    """保存上传的文件并返回文档ID"""
    # 实现文件保存逻辑
    pass

def _get_document_path(document_id: str) -> str:
    """根据文档ID获取文档路径"""
    # 实现文档路径获取逻辑
    pass

def _convert_audit_result_to_response(result: Dict[str, Any]) -> Dict[str, Any]:
    """将内部审计结果转换为API响应格式"""
    # 实现结果格式转换逻辑
    pass

def _get_status_message(status: str) -> str:
    """根据状态获取状态消息"""
    status_messages = {
        "pending": "任务等待处理中",
        "processing": "任务正在处理中", 
        "completed": "任务处理完成",
        "failed": "任务处理失败"
    }
    return status_messages.get(status, "未知状态")

def _get_contradiction_details_from_db(task_id: str, fact_key: str, db: Session) -> Dict[str, Any]:
    """从数据库获取矛盾证据组详情"""
    # 实现数据库查询逻辑
    pass

def _load_universal_facts_from_db() -> List[Dict[str, Any]]:
    """从数据库加载通用事实库"""
    # 实现数据库查询逻辑
    pass

def _group_facts_by_category(facts: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """按类别分组事实"""
    # 实现分组逻辑
    pass
