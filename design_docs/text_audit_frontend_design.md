# 智能文本核查前端界面设计方案

## 🎨 整体设计理念

基于"零配置、智能化、可视化"的设计原则，为用户提供简洁直观的文本核查体验。界面设计遵循现代化的Material Design规范，确保在企业级应用中的专业性和易用性。

## 📱 界面架构设计

### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│  智遇文档平台 - 智能文本核查                                    │
├─────────────────────────────────────────────────────────────┤
│  [文档上传区域]  [核查配置面板]  [结果展示区域]                   │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐   │
│  │   📄 拖拽    │  │  ⚙️ 智能配置  │  │  📊 实时结果展示     │   │
│  │   上传区域   │  │   助手       │  │                     │   │
│  │             │  │             │  │  • 基础质量问题      │   │
│  └─────────────┘  └─────────────┘  │  • 结构一致性问题    │   │
│                                   │  • 外部合规问题      │   │
│                                   │  • 跨文档一致性问题  │   │
│                                   └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 核心功能界面设计

### 1. 文档上传界面

#### 上传区域设计
- **拖拽上传**: 支持直接拖拽文件到指定区域
- **点击上传**: 点击区域弹出文件选择对话框
- **格式提示**: 清晰显示支持的文件格式（PDF、Word、Excel、图片）
- **进度显示**: 实时显示上传进度和文件大小

```html
<div class="upload-zone">
  <div class="upload-icon">📄</div>
  <h3>拖拽文件到此处或点击上传</h3>
  <p>支持 PDF、Word、Excel、图片格式，最大100MB</p>
  <button class="upload-btn">选择文件</button>

  <!-- 上传进度条 -->
  <div class="progress-bar" v-if="uploading">
    <div class="progress-fill" :style="{width: uploadProgress + '%'}"></div>
    <span class="progress-text">{{uploadProgress}}%</span>
  </div>
</div>
```

### 2. 智能配置面板

#### 零配置模式（默认）
```html
<div class="config-panel">
  <h3>🤖 智能核查配置</h3>

  <!-- 一键开启模式 -->
  <div class="quick-mode">
    <button class="primary-btn" @click="startQuickAudit">
      ⚡ 一键智能核查
    </button>
    <p class="hint">系统将自动检查所有常见问题</p>
  </div>

  <!-- 高级配置（可折叠） -->
  <div class="advanced-config" v-if="showAdvanced">
    <h4>高级选项</h4>

    <div class="config-group">
      <label class="switch">
        <input type="checkbox" v-model="config.enableBasicQuality" checked>
        <span class="slider"></span>
        基础文本质量检查
      </label>
      <small>检查错别字、标点、语法等问题</small>
    </div>

    <div class="config-group">
      <label class="switch">
        <input type="checkbox" v-model="config.enableStructureCheck" checked>
        <span class="slider"></span>
        结构一致性检查
      </label>
      <small>检查编号连续性、章节引用等</small>
    </div>

    <div class="config-group">
      <label class="switch">
        <input type="checkbox" v-model="config.enableExternalCompliance">
        <span class="slider"></span>
        外部合规性检查
      </label>
      <small>验证工商信息、法规引用等</small>
    </div>

    <div class="config-group">
      <label class="switch">
        <input type="checkbox" v-model="config.enableCrossDocumentCheck" checked>
        <span class="slider"></span>
        跨文档一致性检查
      </label>
      <small>检查文档内部信息一致性</small>
    </div>
  </div>

  <button class="link-btn" @click="showAdvanced = !showAdvanced">
    {{ showAdvanced ? '收起高级选项' : '展开高级选项' }}
  </button>
</div>
```

### 3. 实时结果展示界面

#### 核查进度显示
```html
<div class="audit-progress" v-if="auditInProgress">
  <div class="progress-header">
    <h3>🔍 正在进行智能核查...</h3>
    <span class="progress-percentage">{{progress}}%</span>
  </div>

  <div class="progress-steps">
    <div class="step" :class="{active: currentStep >= 1, completed: currentStep > 1}">
      <div class="step-icon">📄</div>
      <span>文档解析</span>
    </div>
    <div class="step" :class="{active: currentStep >= 2, completed: currentStep > 2}">
      <div class="step-icon">🔤</div>
      <span>文本质量检查</span>
    </div>
    <div class="step" :class="{active: currentStep >= 3, completed: currentStep > 3}">
      <div class="step-icon">📋</div>
      <span>结构一致性检查</span>
    </div>
    <div class="step" :class="{active: currentStep >= 4, completed: currentStep > 4}">
      <div class="step-icon">🔍</div>
      <span>一致性校验</span>
    </div>
  </div>

  <div class="current-task">
    <p>{{currentTaskMessage}}</p>
  </div>
</div>
```

#### 核查结果展示
```html
<div class="audit-results" v-if="auditCompleted">
  <!-- 结果汇总卡片 -->
  <div class="summary-cards">
    <div class="summary-card">
      <div class="card-icon">📊</div>
      <div class="card-content">
        <h3>{{summary.totalIssues}}</h3>
        <p>发现问题总数</p>
      </div>
    </div>

    <div class="summary-card severity-high">
      <div class="card-icon">🚨</div>
      <div class="card-content">
        <h3>{{summary.severityDistribution.high}}</h3>
        <p>高优先级问题</p>
      </div>
    </div>

    <div class="summary-card severity-medium">
      <div class="card-icon">⚠️</div>
      <div class="card-content">
        <h3>{{summary.severityDistribution.medium}}</h3>
        <p>中优先级问题</p>
      </div>
    </div>

    <div class="summary-card severity-low">
      <div class="card-icon">ℹ️</div>
      <div class="card-content">
        <h3>{{summary.severityDistribution.low}}</h3>
        <p>低优先级问题</p>
      </div>
    </div>
  </div>

  <!-- 问题分类标签页 -->
  <div class="results-tabs">
    <button class="tab-btn" :class="{active: activeTab === 'basic'}" @click="activeTab = 'basic'">
      基础质量问题 ({{summary.basicQualityIssues}})
    </button>
    <button class="tab-btn" :class="{active: activeTab === 'structure'}" @click="activeTab = 'structure'">
      结构问题 ({{summary.structureIssues}})
    </button>
    <button class="tab-btn" :class="{active: activeTab === 'compliance'}" @click="activeTab = 'compliance'">
      合规问题 ({{summary.complianceIssues}})
    </button>
    <button class="tab-btn" :class="{active: activeTab === 'consistency'}" @click="activeTab = 'consistency'">
      一致性问题 ({{summary.consistencyIssues}})
    </button>
  </div>
</div>
```

### 4. 矛盾证据组展示界面

这是本设计方案的核心创新点，解决原文溯源的关键界面。

```html
<div class="contradiction-group" v-for="group in contradictionGroups" :key="group.factKey">
  <div class="group-header" @click="toggleGroup(group.factKey)">
    <div class="group-title">
      <span class="error-icon">⚠️</span>
      <h4>{{group.errorMessage}}</h4>
      <span class="evidence-count">{{group.evidenceCount}}处不一致</span>
    </div>
    <div class="expand-icon" :class="{expanded: expandedGroups.includes(group.factKey)}">
      ▼
    </div>
  </div>

  <!-- 展开的证据对比区域 -->
  <div class="evidence-comparison" v-if="expandedGroups.includes(group.factKey)">
    <div class="comparison-grid">
      <div class="evidence-card" v-for="evidence in group.evidenceList" :key="evidence.blockId">
        <div class="evidence-header">
          <span class="page-number">第{{evidence.pageNumber}}页</span>
          <span class="confidence">置信度: {{(evidence.confidence * 100).toFixed(1)}}%</span>
        </div>

        <div class="evidence-content">
          <div class="fact-value" :class="getValueClass(evidence.normalizedValue)">
            {{evidence.factValue}}
          </div>
          <div class="exact-quote">
            "{{evidence.exactQuote}}"
          </div>
        </div>

        <div class="evidence-actions">
          <button class="link-btn" @click="jumpToSource(evidence.blockId, evidence.pageNumber)">
            📍 跳转原文
          </button>
        </div>
      </div>
    </div>

    <!-- 标准化值对比 -->
    <div class="normalized-comparison">
      <h5>标准化值对比：</h5>
      <div class="value-tags">
        <span class="value-tag" v-for="value in getUniqueNormalizedValues(group)" :key="value">
          {{value}}
        </span>
      </div>
    </div>
  </div>
</div>
```

### 5. 原文溯源界面

```html
<div class="source-viewer" v-if="showSourceViewer">
  <div class="viewer-header">
    <h3>📄 原文查看</h3>
    <button class="close-btn" @click="closeSourceViewer">✕</button>
  </div>

  <div class="viewer-content">
    <!-- 文档预览区域 -->
    <div class="document-preview">
      <div class="page-navigation">
        <button @click="previousPage" :disabled="currentPage <= 1">◀ 上一页</button>
        <span>第 {{currentPage}} 页 / 共 {{totalPages}} 页</span>
        <button @click="nextPage" :disabled="currentPage >= totalPages">下一页 ▶</button>
      </div>

      <!-- 文档内容显示区域，高亮显示目标文本 -->
      <div class="document-content">
        <div class="highlighted-text" v-html="highlightedContent"></div>
      </div>
    </div>

    <!-- 相关问题侧边栏 -->
    <div class="related-issues-sidebar">
      <h4>本页相关问题</h4>
      <div class="issue-list">
        <div class="issue-item" v-for="issue in currentPageIssues" :key="issue.id">
          <div class="issue-type">{{issue.errorType}}</div>
          <div class="issue-message">{{issue.errorMessage}}</div>
          <button class="highlight-btn" @click="highlightIssue(issue)">定位</button>
        </div>
      </div>
    </div>
  </div>
</div>
```

## 🎯 用户交互流程设计

### 核心交互流程
1. **文档上传** → 拖拽或点击上传文档
2. **配置选择** → 选择"一键智能核查"或自定义配置
3. **开始核查** → 点击开始，显示实时进度
4. **查看结果** → 分类查看不同类型的问题
5. **深入分析** → 点击展开矛盾证据组，查看详细对比
6. **原文溯源** → 点击"跳转原文"查看具体位置

### 关键交互特性
- **零配置启动**: 默认启用所有核查功能，用户无需配置
- **实时进度反馈**: 显示当前处理阶段和进度百分比
- **分层结果展示**: 从汇总到详情的层次化信息展示
- **一键原文定位**: 点击即可跳转到问题所在的原文位置
- **并排证据对比**: 矛盾信息并排显示，便于用户对比分析

## 📱 响应式设计

### 桌面端 (≥1200px)
- 三栏布局：上传区 | 配置面板 | 结果展示
- 矛盾证据组采用网格布局，最多3列显示

### 平板端 (768px - 1199px)
- 两栏布局：上传配置合并 | 结果展示
- 矛盾证据组采用2列网格布局

### 移动端 (<768px)
- 单栏垂直布局，各区域上下排列
- 矛盾证据组采用单列布局
- 原文查看器全屏显示

## 🎨 视觉设计规范

### 色彩方案
- **主色调**: #1976D2 (蓝色) - 专业、可信赖
- **成功色**: #4CAF50 (绿色) - 通过、正确
- **警告色**: #FF9800 (橙色) - 中等问题
- **错误色**: #F44336 (红色) - 严重问题
- **信息色**: #2196F3 (浅蓝) - 提示信息

### 字体规范
- **标题字体**: 16px-24px, 字重600
- **正文字体**: 14px, 字重400
- **小字提示**: 12px, 字重400
- **代码字体**: Monaco, Consolas, monospace

### 间距规范
- **组件间距**: 16px, 24px, 32px
- **内容边距**: 8px, 12px, 16px
- **卡片圆角**: 8px
- **按钮圆角**: 4px

这个前端设计方案充分考虑了用户的"懒人"需求，提供零配置的智能核查体验，同时通过创新的矛盾证据组展示和原文溯源功能，完美解决了复杂文档核查结果的可视化展示问题。