# 审计服务项目 .gitignore 文件

# ===== 日志文件 =====
logs/
*.log
*.log.*
log/
audit.log
error.log
access.log
debug.log
application.log
celery.log

# ===== Python 相关 =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ===== 数据库文件 =====
*.db
*.sqlite
*.sqlite3
*.db-journal

# MySQL
*.sql
mysql_data/

# Redis
redis_data/
dump.rdb

# ===== 文档和上传文件 =====
uploads/
temp/
tmp/
documents/
*.pdf
*.docx
*.doc
*.xlsx
*.xls
*.ppt
*.pptx

# 但保留示例文件
!examples/*.pdf
!examples/*.docx
!tests/fixtures/*.pdf

# ===== 缓存和临时文件 =====
.cache/
*.cache
*.tmp
*.temp
.temporary/

# ===== IDE 和编辑器 =====
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== 系统文件 =====
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== Docker 相关 =====
.dockerignore
docker-compose.override.yml
.docker/

# ===== 配置文件 =====
# 敏感配置文件
config/local.py
config/production.py
.env.local
.env.production
.env.development
secrets.json
private_key.pem
*.key
*.pem
*.crt
*.cert

# 但保留示例配置
!config/example.py
!.env.example

# ===== 审计服务特定文件 =====
# 处理结果
results/
output/
reports/
audit_results/

# 模型文件
models/
*.pkl
*.joblib
*.h5
*.pb

# 向量数据库
faiss_index/
embeddings/
*.index

# ELK Stack 数据
elasticsearch_data/
logstash_data/
kibana_data/

# Celery 相关
celerybeat-schedule.*
celery_worker.log

# 监控数据
monitoring/
metrics/
prometheus_data/

# 备份文件
backup/
*.backup
*.bak

# ===== 开发工具 =====
# 性能分析
*.prof
profile_output/

# 调试文件
debug/
*.debug

# 测试输出
test_output/
test_results/

# 覆盖率报告
.coverage.*
coverage_html/

# ===== 其他 =====
# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 但保留发布包
!releases/*.zip
!dist/*.tar.gz

# 临时笔记
notes.txt
TODO.txt
scratch.py
test.py

# 本地开发脚本
local_*.py
dev_*.py

# 锁文件（根据需要调整）
package-lock.json
yarn.lock

# Node.js (如果有前端部分)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
