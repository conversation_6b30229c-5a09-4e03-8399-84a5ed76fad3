# Elasticsearch configuration for audit service

# Cluster settings
cluster.name: audit-cluster
node.name: elasticsearch-node-1

# Network settings
network.host: 0.0.0.0
http.port: 9200
transport.port: 9300

# Discovery settings
discovery.type: single-node

# Path settings
path.data: /usr/share/elasticsearch/data
path.logs: /usr/share/elasticsearch/logs

# Memory settings
bootstrap.memory_lock: true

# Security settings (disabled for development)
xpack.security.enabled: false
xpack.security.enrollment.enabled: false
xpack.security.http.ssl.enabled: false
xpack.security.transport.ssl.enabled: false

# Monitoring settings
xpack.monitoring.collection.enabled: true

# Index settings
action.auto_create_index: true

# Logging settings
logger.level: INFO

# Performance settings
indices.memory.index_buffer_size: 10%
indices.memory.min_index_buffer_size: 48mb

# Index lifecycle management
xpack.ilm.enabled: true

# Template for audit service logs
index.number_of_shards: 1
index.number_of_replicas: 0
index.refresh_interval: 5s

# Disable machine learning (to save resources)
xpack.ml.enabled: false

# Enable CORS for development
http.cors.enabled: true
http.cors.allow-origin: "*"
http.cors.allow-methods: OPTIONS, HEAD, GET, POST, PUT, DELETE
http.cors.allow-headers: X-Requested-With, X-Auth-Token, Content-Type, Content-Length, Authorization
