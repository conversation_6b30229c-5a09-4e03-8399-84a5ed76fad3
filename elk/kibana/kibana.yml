# Kibana configuration for audit service

# Server settings
server.name: "audit-kibana"
server.host: "0.0.0.0"
server.port: 5601

# Elasticsearch settings
elasticsearch.hosts: ["http://elasticsearch:9200"]
elasticsearch.requestTimeout: 30000
elasticsearch.shardTimeout: 30000

# Kibana settings
kibana.index: ".kibana"
kibana.defaultAppId: "discover"

# Logging settings
logging.appenders:
  file:
    type: file
    fileName: /usr/share/kibana/logs/kibana.log
    layout:
      type: json
  console:
    type: console
    layout:
      type: pattern
      pattern: "[%date][%level][%logger] %message"

logging.loggers:
  - name: root
    appenders: [file, console]
    level: info

# Monitoring settings
monitoring.ui.container.elasticsearch.enabled: true
monitoring.ui.container.logstash.enabled: true

# Security settings (disabled for development)
xpack.security.enabled: false
xpack.encryptedSavedObjects.encryptionKey: "audit-service-encryption-key-32-chars"

# Reporting settings
xpack.reporting.enabled: true
xpack.reporting.encryptionKey: "audit-service-reporting-key-32-chars"

# Canvas settings
xpack.canvas.enabled: true

# Maps settings
xpack.maps.enabled: true

# Machine learning settings (disabled to save resources)
xpack.ml.enabled: false

# APM settings
xpack.apm.enabled: true

# Spaces settings
xpack.spaces.enabled: true

# Advanced settings
server.maxPayloadBytes: 1048576
server.compression.enabled: true

# Telemetry settings
telemetry.enabled: false
telemetry.optIn: false

# Newsfeed settings
newsfeed.enabled: false

# Data views settings
data_views.scriptedFieldsEnabled: true

# Saved objects settings
savedObjects.maxImportPayloadBytes: 26214400

# UI settings
uiSettings.overrides:
  "dateFormat": "YYYY-MM-DD HH:mm:ss"
  "dateFormat:tz": "UTC"
  "defaultIndex": "audit-service-logs-*"
  "timepicker:timeDefaults": "{ \"from\": \"now-24h\", \"to\": \"now\" }"
  "discover:sampleSize": 500
  "context:defaultSize": 5
  "discover:aggs:terms:size": 20
