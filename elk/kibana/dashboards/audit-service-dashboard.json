{"version": "8.11.0", "objects": [{"id": "audit-service-logs-*", "type": "index-pattern", "attributes": {"title": "audit-service-logs-*", "timeFieldName": "@timestamp", "fields": "[{\"name\":\"@timestamp\",\"type\":\"date\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"level\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"logger\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"message\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":false},{\"name\":\"service\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"hostname\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"module\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"function\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"line\",\"type\":\"number\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"file\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"request_id\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"task_id\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"task_name\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"http_method\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"status_code\",\"type\":\"number\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"duration_ms\",\"type\":\"number\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"url\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"log_category\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"log_type\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"environment\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"version\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true}]"}}, {"id": "audit-service-overview", "type": "dashboard", "attributes": {"title": "Audit Service Overview", "description": "Overview dashboard for audit service logs and metrics", "panelsJSON": "[{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},\"panelIndex\":\"1\",\"embeddableConfig\":{},\"panelRefName\":\"panel_1\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"2\"},\"panelIndex\":\"2\",\"embeddableConfig\":{},\"panelRefName\":\"panel_2\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":15,\"w\":48,\"h\":15,\"i\":\"3\"},\"panelIndex\":\"3\",\"embeddableConfig\":{},\"panelRefName\":\"panel_3\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":30,\"w\":24,\"h\":15,\"i\":\"4\"},\"panelIndex\":\"4\",\"embeddableConfig\":{},\"panelRefName\":\"panel_4\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":24,\"y\":30,\"w\":24,\"h\":15,\"i\":\"5\"},\"panelIndex\":\"5\",\"embeddableConfig\":{},\"panelRefName\":\"panel_5\"}]", "optionsJSON": "{\"useMargins\":true,\"syncColors\":false,\"hidePanelTitles\":false}", "version": 1, "timeRestore": false, "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[]}"}}, "references": [{"name": "panel_1", "type": "visualization", "id": "log-levels-pie"}, {"name": "panel_2", "type": "visualization", "id": "http-status-codes"}, {"name": "panel_3", "type": "visualization", "id": "logs-timeline"}, {"name": "panel_4", "type": "visualization", "id": "top-errors"}, {"name": "panel_5", "type": "visualization", "id": "response-times"}]}, {"id": "log-levels-pie", "type": "visualization", "attributes": {"title": "Log Levels Distribution", "visState": "{\"title\":\"Log Levels Distribution\",\"type\":\"pie\",\"aggs\":[{\"id\":\"1\",\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"type\":\"terms\",\"schema\":\"segment\",\"params\":{\"field\":\"level\",\"size\":10,\"order\":\"desc\",\"orderBy\":\"1\"}}]}", "uiStateJSON": "{}", "description": "", "version": 1, "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"audit-service-logs-*\",\"query\":{\"match_all\":{}},\"filter\":[]}"}}}, {"id": "http-status-codes", "type": "visualization", "attributes": {"title": "HTTP Status Codes", "visState": "{\"title\":\"HTTP Status Codes\",\"type\":\"histogram\",\"aggs\":[{\"id\":\"1\",\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"type\":\"terms\",\"schema\":\"segment\",\"params\":{\"field\":\"status_code\",\"size\":10,\"order\":\"desc\",\"orderBy\":\"1\"}}]}", "uiStateJSON": "{}", "description": "", "version": 1, "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"audit-service-logs-*\",\"query\":{\"bool\":{\"must\":[{\"exists\":{\"field\":\"status_code\"}}]}},\"filter\":[]}"}}}, {"id": "logs-timeline", "type": "visualization", "attributes": {"title": "Logs Timeline", "visState": "{\"title\":\"Logs Timeline\",\"type\":\"line\",\"aggs\":[{\"id\":\"1\",\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"type\":\"date_histogram\",\"schema\":\"segment\",\"params\":{\"field\":\"@timestamp\",\"interval\":\"auto\",\"min_doc_count\":1}},{\"id\":\"3\",\"type\":\"terms\",\"schema\":\"group\",\"params\":{\"field\":\"level\",\"size\":5,\"order\":\"desc\",\"orderBy\":\"1\"}}]}", "uiStateJSON": "{}", "description": "", "version": 1, "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"audit-service-logs-*\",\"query\":{\"match_all\":{}},\"filter\":[]}"}}}, {"id": "top-errors", "type": "visualization", "attributes": {"title": "Top Errors", "visState": "{\"title\":\"Top Errors\",\"type\":\"table\",\"aggs\":[{\"id\":\"1\",\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"type\":\"terms\",\"schema\":\"bucket\",\"params\":{\"field\":\"message.keyword\",\"size\":10,\"order\":\"desc\",\"orderBy\":\"1\"}},{\"id\":\"3\",\"type\":\"terms\",\"schema\":\"bucket\",\"params\":{\"field\":\"logger\",\"size\":5,\"order\":\"desc\",\"orderBy\":\"1\"}}]}", "uiStateJSON": "{}", "description": "", "version": 1, "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"audit-service-logs-*\",\"query\":{\"bool\":{\"must\":[{\"term\":{\"level\":\"ERROR\"}}]}},\"filter\":[]}"}}}, {"id": "response-times", "type": "visualization", "attributes": {"title": "HTTP Response Times", "visState": "{\"title\":\"HTTP Response Times\",\"type\":\"line\",\"aggs\":[{\"id\":\"1\",\"type\":\"avg\",\"schema\":\"metric\",\"params\":{\"field\":\"duration_ms\"}},{\"id\":\"2\",\"type\":\"date_histogram\",\"schema\":\"segment\",\"params\":{\"field\":\"@timestamp\",\"interval\":\"auto\",\"min_doc_count\":1}},{\"id\":\"3\",\"type\":\"percentiles\",\"schema\":\"metric\",\"params\":{\"field\":\"duration_ms\",\"percents\":[50,95,99]}}]}", "uiStateJSON": "{}", "description": "", "version": 1, "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"audit-service-logs-*\",\"query\":{\"bool\":{\"must\":[{\"exists\":{\"field\":\"duration_ms\"}}]}},\"filter\":[]}"}}}]}