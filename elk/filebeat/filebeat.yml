# Filebeat configuration for audit service

# Filebeat inputs
filebeat.inputs:
  # Application log files
  - type: log
    enabled: true
    paths:
      - /usr/share/filebeat/logs/*.log
    fields:
      service: audit-service
      log_type: application
    fields_under_root: true
    json.keys_under_root: true
    json.add_error_key: true
    json.message_key: message
    multiline.pattern: '^\d{4}-\d{2}-\d{2}'
    multiline.negate: true
    multiline.match: after
    scan_frequency: 10s
    harvester_buffer_size: 16384
    max_bytes: 10485760

  # Docker container logs
  - type: container
    enabled: true
    paths:
      - '/var/lib/docker/containers/*/*.log'
    processors:
      - add_docker_metadata:
          host: "unix:///var/run/docker.sock"
      - decode_json_fields:
          fields: ["message"]
          target: ""
          overwrite_keys: true
    fields:
      service: audit-service
      log_type: container
    fields_under_root: true

# Filebeat modules
filebeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: false

# Processors
processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
  - add_docker_metadata: ~
  - add_kubernetes_metadata: ~
  - timestamp:
      field: "@timestamp"
      layouts:
        - '2006-01-02T15:04:05.000Z'
        - '2006-01-02T15:04:05Z'
      test:
        - '2023-01-01T12:00:00.000Z'

# Output configuration
output.logstash:
  hosts: ["logstash:5044"]
  compression_level: 3
  bulk_max_size: 2048
  template.name: "audit-service"
  template.pattern: "audit-service-*"

# Alternative: Direct output to Elasticsearch (comment out logstash output above)
# output.elasticsearch:
#   hosts: ["elasticsearch:9200"]
#   index: "audit-service-logs-%{+yyyy.MM.dd}"
#   template.name: "audit-service"
#   template.pattern: "audit-service-*"
#   template.settings:
#     index.number_of_shards: 1
#     index.number_of_replicas: 0

# Kibana configuration
setup.kibana:
  host: "kibana:5601"

# Index template settings
setup.template.settings:
  index.number_of_shards: 1
  index.number_of_replicas: 0
  index.refresh_interval: 5s

# Dashboards
setup.dashboards.enabled: true

# Logging configuration
logging.level: info
logging.to_files: true
logging.files:
  path: /usr/share/filebeat/logs
  name: filebeat
  keepfiles: 7
  permissions: 0644

# Monitoring
monitoring.enabled: true

# HTTP endpoint for health checks
http.enabled: true
http.host: 0.0.0.0
http.port: 5066

# Registry settings
filebeat.registry.path: /usr/share/filebeat/data/registry
filebeat.registry.file_permissions: 0600
filebeat.registry.flush: 1s

# Queue settings
queue.mem:
  events: 4096
  flush.min_events: 512
  flush.timeout: 1s
