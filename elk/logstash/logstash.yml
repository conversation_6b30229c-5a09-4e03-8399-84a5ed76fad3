# Logstash configuration for audit service

# Node settings
node.name: logstash-audit

# Path settings
path.data: /usr/share/logstash/data
path.logs: /usr/share/logstash/logs
path.settings: /usr/share/logstash/config

# Pipeline settings
pipeline.workers: 2
pipeline.batch.size: 125
pipeline.batch.delay: 50

# HTTP API settings
http.host: "0.0.0.0"
http.port: 9600

# Log settings
log.level: info
path.logs: /usr/share/logstash/logs

# Monitoring settings
xpack.monitoring.enabled: false

# Config reload settings
config.reload.automatic: true
config.reload.interval: 3s

# Dead letter queue settings
dead_letter_queue.enable: true
dead_letter_queue.max_bytes: 1024mb

# Queue settings
queue.type: memory
queue.max_events: 0
queue.max_bytes: 1024mb

# Slow log settings
slowlog.threshold.warn: 2s
slowlog.threshold.info: 1s
slowlog.threshold.debug: 500ms
slowlog.threshold.trace: 100ms
