# Logstash pipeline configuration for audit service

input {
  # Beats input (Filebeat)
  beats {
    port => 5044
    host => "0.0.0.0"
  }
  
  # TCP input for direct log shipping
  tcp {
    port => 5000
    codec => json_lines
  }
  
  # UDP input for high-throughput logging
  udp {
    port => 5000
    codec => json_lines
  }
  
  # File input for log files
  file {
    path => "/usr/share/logstash/logs/*.log"
    start_position => "beginning"
    codec => json
    tags => ["file_input"]
  }
}

filter {
  # Parse JSON logs
  if [message] {
    json {
      source => "message"
      target => "parsed"
    }
    
    # Move parsed fields to root level
    if [parsed] {
      ruby {
        code => "
          parsed = event.get('parsed')
          if parsed.is_a?(Hash)
            parsed.each { |k, v| event.set(k, v) }
          end
          event.remove('parsed')
        "
      }
    }
  }
  
  # Add service identification
  if ![service] {
    mutate {
      add_field => { "service" => "audit-service" }
    }
  }
  
  # Parse timestamp
  if [@timestamp] {
    date {
      match => [ "@timestamp", "ISO8601" ]
      target => "@timestamp"
    }
  }
  
  # Add environment information
  mutate {
    add_field => { 
      "environment" => "${ENVIRONMENT:development}"
      "version" => "${SERVICE_VERSION:1.0.0}"
    }
  }
  
  # Categorize log levels
  if [level] {
    if [level] == "ERROR" {
      mutate {
        add_tag => ["error"]
        add_field => { "log_category" => "error" }
      }
    } else if [level] == "WARN" or [level] == "WARNING" {
      mutate {
        add_tag => ["warning"]
        add_field => { "log_category" => "warning" }
      }
    } else if [level] == "INFO" {
      mutate {
        add_tag => ["info"]
        add_field => { "log_category" => "info" }
      }
    } else if [level] == "DEBUG" {
      mutate {
        add_tag => ["debug"]
        add_field => { "log_category" => "debug" }
      }
    }
  }
  
  # Parse HTTP request logs
  if [type] == "http_request" {
    mutate {
      add_tag => ["http"]
      add_field => { "log_type" => "http_request" }
    }
    
    # Categorize HTTP status codes
    if [status_code] {
      if [status_code] >= 500 {
        mutate {
          add_tag => ["http_error"]
          add_field => { "http_category" => "server_error" }
        }
      } else if [status_code] >= 400 {
        mutate {
          add_tag => ["http_client_error"]
          add_field => { "http_category" => "client_error" }
        }
      } else if [status_code] >= 300 {
        mutate {
          add_tag => ["http_redirect"]
          add_field => { "http_category" => "redirect" }
        }
      } else if [status_code] >= 200 {
        mutate {
          add_tag => ["http_success"]
          add_field => { "http_category" => "success" }
        }
      }
    }
  }
  
  # Parse task execution logs
  if [type] == "task_execution" {
    mutate {
      add_tag => ["task"]
      add_field => { "log_type" => "task_execution" }
    }
    
    # Categorize task status
    if [task_status] {
      if [task_status] == "failed" {
        mutate {
          add_tag => ["task_failed"]
        }
      } else if [task_status] == "completed" {
        mutate {
          add_tag => ["task_completed"]
        }
      } else if [task_status] == "started" {
        mutate {
          add_tag => ["task_started"]
        }
      }
    }
  }
  
  # Parse exception information
  if [exception] {
    mutate {
      add_tag => ["exception"]
      add_field => { "has_exception" => true }
    }
  }
  
  # Clean up fields
  mutate {
    remove_field => ["agent", "ecs", "input", "log"]
  }
  
  # Add geolocation for IP addresses (if available)
  if [client_ip] {
    geoip {
      source => "client_ip"
      target => "geoip"
    }
  }
}

output {
  # Output to Elasticsearch
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "audit-service-logs-%{+YYYY.MM.dd}"
    template_name => "audit-service-template"
    template_pattern => "audit-service-logs-*"
    template => {
      "index_patterns" => ["audit-service-logs-*"]
      "settings" => {
        "number_of_shards" => 1
        "number_of_replicas" => 0
        "index.refresh_interval" => "5s"
      }
      "mappings" => {
        "properties" => {
          "@timestamp" => { "type" => "date" }
          "level" => { "type" => "keyword" }
          "logger" => { "type" => "keyword" }
          "message" => { "type" => "text" }
          "service" => { "type" => "keyword" }
          "hostname" => { "type" => "keyword" }
          "module" => { "type" => "keyword" }
          "function" => { "type" => "keyword" }
          "line" => { "type" => "integer" }
          "file" => { "type" => "keyword" }
          "thread" => { "type" => "integer" }
          "process" => { "type" => "integer" }
          "request_id" => { "type" => "keyword" }
          "task_id" => { "type" => "keyword" }
          "task_name" => { "type" => "keyword" }
          "http_method" => { "type" => "keyword" }
          "status_code" => { "type" => "integer" }
          "duration_ms" => { "type" => "float" }
          "url" => { "type" => "keyword" }
          "log_category" => { "type" => "keyword" }
          "log_type" => { "type" => "keyword" }
          "environment" => { "type" => "keyword" }
          "version" => { "type" => "keyword" }
        }
      }
    }
  }
  
  # Debug output (comment out in production)
  # stdout {
  #   codec => rubydebug
  # }
}
