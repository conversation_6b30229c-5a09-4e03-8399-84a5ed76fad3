"""
负载测试和性能测试
"""
import pytest
import time
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import patch

from tests.factories import TaskDataFactory, MockFactory


@pytest.mark.slow
@pytest.mark.performance
class TestAPIPerformance:
    """API性能测试"""
    
    def test_health_check_response_time(self, test_client):
        """测试健康检查响应时间"""
        response_times = []
        
        # 执行多次健康检查
        for _ in range(50):
            start_time = time.time()
            response = test_client.get("/api/v1/health")
            end_time = time.time()
            
            assert response.status_code == 200
            response_times.append(end_time - start_time)
        
        # 分析响应时间
        avg_time = statistics.mean(response_times)
        max_time = max(response_times)
        min_time = min(response_times)
        p95_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
        
        print(f"\n健康检查性能统计:")
        print(f"平均响应时间: {avg_time:.3f}s")
        print(f"最大响应时间: {max_time:.3f}s")
        print(f"最小响应时间: {min_time:.3f}s")
        print(f"95%响应时间: {p95_time:.3f}s")
        
        # 性能断言
        assert avg_time < 0.1  # 平均响应时间应小于100ms
        assert p95_time < 0.2  # 95%的请求应在200ms内完成
    
    def test_audit_sync_performance(self, test_client, mock_external_services):
        """测试同步审核性能"""
        audit_request = TaskDataFactory.create_audit_request_data(
            task_name="性能测试任务"
        )
        
        response_times = []
        
        with patch('app.services.audit_service.AuditService.process_audit_sync') as mock_process:
            mock_process.return_value = {
                "audit_result": "通过",
                "score": 90,
                "summary": "性能测试完成"
            }
            
            # 执行多次同步审核
            for i in range(20):
                start_time = time.time()
                response = test_client.post("/api/v1/audit/sync", json=audit_request)
                end_time = time.time()
                
                assert response.status_code == 200
                response_times.append(end_time - start_time)
        
        # 分析性能
        avg_time = statistics.mean(response_times)
        max_time = max(response_times)
        p95_time = statistics.quantiles(response_times, n=20)[18]
        
        print(f"\n同步审核性能统计:")
        print(f"平均响应时间: {avg_time:.3f}s")
        print(f"最大响应时间: {max_time:.3f}s")
        print(f"95%响应时间: {p95_time:.3f}s")
        
        # 性能断言
        assert avg_time < 1.0  # 平均响应时间应小于1秒
        assert p95_time < 2.0  # 95%的请求应在2秒内完成
    
    def test_concurrent_requests_performance(self, test_client, mock_external_services):
        """测试并发请求性能"""
        audit_request = TaskDataFactory.create_audit_request_data()
        
        def make_request():
            with patch('app.services.audit_service.AuditService.process_audit_sync') as mock_process:
                mock_process.return_value = {"audit_result": "通过", "score": 90}
                
                start_time = time.time()
                response = test_client.post("/api/v1/audit/sync", json=audit_request)
                end_time = time.time()
                
                return {
                    "status_code": response.status_code,
                    "response_time": end_time - start_time
                }
        
        # 并发执行请求
        concurrent_users = 10
        requests_per_user = 5
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = []
            for _ in range(concurrent_users * requests_per_user):
                futures.append(executor.submit(make_request))
            
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 分析结果
        successful_requests = [r for r in results if r["status_code"] == 200]
        response_times = [r["response_time"] for r in successful_requests]
        
        success_rate = len(successful_requests) / len(results)
        throughput = len(successful_requests) / total_time
        avg_response_time = statistics.mean(response_times) if response_times else 0
        
        print(f"\n并发性能统计:")
        print(f"并发用户数: {concurrent_users}")
        print(f"总请求数: {len(results)}")
        print(f"成功请求数: {len(successful_requests)}")
        print(f"成功率: {success_rate:.2%}")
        print(f"吞吐量: {throughput:.2f} 请求/秒")
        print(f"平均响应时间: {avg_response_time:.3f}s")
        print(f"总执行时间: {total_time:.3f}s")
        
        # 性能断言
        assert success_rate >= 0.95  # 成功率应大于95%
        assert throughput >= 5  # 吞吐量应大于5请求/秒
        assert avg_response_time < 2.0  # 平均响应时间应小于2秒


@pytest.mark.slow
@pytest.mark.performance
class TestDatabasePerformance:
    """数据库性能测试"""
    
    def test_task_creation_performance(self, test_db_session):
        """测试任务创建性能"""
        from app.services.task_service import TaskService
        
        task_service = TaskService(test_db_session)
        creation_times = []
        
        # 批量创建任务
        for i in range(100):
            start_time = time.time()
            
            task_id = task_service.create_task(
                task_name=f"性能测试任务 {i}",
                config="模型:\n  相关性模型: test-model",
                main_doc=f'{{"title": "文档 {i}", "content": "内容"}}'
            )
            
            end_time = time.time()
            creation_times.append(end_time - start_time)
            
            assert task_id is not None
        
        # 分析性能
        avg_time = statistics.mean(creation_times)
        max_time = max(creation_times)
        p95_time = statistics.quantiles(creation_times, n=20)[18]
        
        print(f"\n任务创建性能统计:")
        print(f"平均创建时间: {avg_time:.4f}s")
        print(f"最大创建时间: {max_time:.4f}s")
        print(f"95%创建时间: {p95_time:.4f}s")
        
        # 性能断言
        assert avg_time < 0.01  # 平均创建时间应小于10ms
        assert p95_time < 0.05  # 95%的创建应在50ms内完成
    
    def test_task_query_performance(self, test_db_session):
        """测试任务查询性能"""
        from app.services.task_service import TaskService
        from app.models.database import RequestTracking, TaskStatus
        
        # 先创建大量测试数据
        task_records = []
        for i in range(1000):
            record = TaskDataFactory.create_task_record(
                task_name=f"查询测试任务 {i}",
                status=TaskStatus.PENDING if i % 2 == 0 else TaskStatus.COMPLETED
            )
            task_records.append(record)
            test_db_session.add(record)
        
        test_db_session.commit()
        
        task_service = TaskService(test_db_session)
        query_times = []
        
        # 执行多次查询
        for record in task_records[:100]:  # 查询前100个任务
            start_time = time.time()
            
            status_response = task_service.get_task_status(record.task_id)
            
            end_time = time.time()
            query_times.append(end_time - start_time)
            
            assert status_response is not None
        
        # 分析性能
        avg_time = statistics.mean(query_times)
        max_time = max(query_times)
        p95_time = statistics.quantiles(query_times, n=20)[18]
        
        print(f"\n任务查询性能统计:")
        print(f"平均查询时间: {avg_time:.4f}s")
        print(f"最大查询时间: {max_time:.4f}s")
        print(f"95%查询时间: {p95_time:.4f}s")
        
        # 性能断言
        assert avg_time < 0.005  # 平均查询时间应小于5ms
        assert p95_time < 0.02  # 95%的查询应在20ms内完成
    
    def test_concurrent_database_operations(self, test_db_session):
        """测试并发数据库操作性能"""
        from app.services.task_service import TaskService
        
        def create_and_query_task(index):
            # 注意：每个线程需要自己的数据库会话
            # 这里为了简化测试，使用共享会话，实际应用中应该避免
            task_service = TaskService(test_db_session)
            
            start_time = time.time()
            
            # 创建任务
            task_id = task_service.create_task(
                task_name=f"并发测试任务 {index}",
                config="模型:\n  相关性模型: test-model"
            )
            
            # 查询任务
            status_response = task_service.get_task_status(task_id)
            
            end_time = time.time()
            
            return {
                "task_id": task_id,
                "operation_time": end_time - start_time,
                "success": status_response is not None
            }
        
        # 并发执行数据库操作
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_and_query_task, i) for i in range(50)]
            results = [future.result() for future in as_completed(futures)]
        
        # 分析结果
        successful_operations = [r for r in results if r["success"]]
        operation_times = [r["operation_time"] for r in successful_operations]
        
        success_rate = len(successful_operations) / len(results)
        avg_time = statistics.mean(operation_times) if operation_times else 0
        
        print(f"\n并发数据库操作性能统计:")
        print(f"总操作数: {len(results)}")
        print(f"成功操作数: {len(successful_operations)}")
        print(f"成功率: {success_rate:.2%}")
        print(f"平均操作时间: {avg_time:.4f}s")
        
        # 性能断言
        assert success_rate >= 0.95  # 成功率应大于95%
        assert avg_time < 0.1  # 平均操作时间应小于100ms


@pytest.mark.slow
@pytest.mark.performance
class TestMemoryUsage:
    """内存使用测试"""
    
    def test_memory_usage_during_load(self, test_client, mock_external_services):
        """测试负载期间的内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        audit_request = TaskDataFactory.create_audit_request_data()
        
        with patch('app.services.audit_service.AuditService.process_audit_sync') as mock_process:
            mock_process.return_value = {"audit_result": "通过", "score": 90}
            
            # 执行大量请求
            for i in range(200):
                response = test_client.post("/api/v1/audit/sync", json=audit_request)
                assert response.status_code == 200
                
                # 每50个请求检查一次内存
                if i % 50 == 0:
                    current_memory = process.memory_info().rss / 1024 / 1024
                    memory_increase = current_memory - initial_memory
                    
                    print(f"请求 {i}: 内存使用 {current_memory:.2f}MB (+{memory_increase:.2f}MB)")
        
        final_memory = process.memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory
        
        print(f"\n内存使用统计:")
        print(f"初始内存: {initial_memory:.2f}MB")
        print(f"最终内存: {final_memory:.2f}MB")
        print(f"内存增长: {total_increase:.2f}MB")
        
        # 内存使用断言（允许适度的内存增长）
        assert total_increase < 100  # 内存增长应小于100MB


@pytest.mark.slow
@pytest.mark.performance
class TestStressTest:
    """压力测试"""
    
    def test_system_under_stress(self, test_client, mock_external_services):
        """系统压力测试"""
        audit_request = TaskDataFactory.create_audit_request_data()
        
        # 压力测试参数
        duration_seconds = 30  # 测试持续时间
        concurrent_users = 20  # 并发用户数
        
        results = []
        start_time = time.time()
        
        def stress_worker():
            worker_results = []
            worker_start = time.time()
            
            while time.time() - worker_start < duration_seconds:
                try:
                    with patch('app.services.audit_service.AuditService.process_audit_sync') as mock_process:
                        mock_process.return_value = {"audit_result": "通过", "score": 90}
                        
                        request_start = time.time()
                        response = test_client.post("/api/v1/audit/sync", json=audit_request)
                        request_end = time.time()
                        
                        worker_results.append({
                            "status_code": response.status_code,
                            "response_time": request_end - request_start,
                            "timestamp": request_end
                        })
                        
                except Exception as e:
                    worker_results.append({
                        "status_code": 500,
                        "response_time": 0,
                        "error": str(e),
                        "timestamp": time.time()
                    })
            
            return worker_results
        
        # 启动压力测试
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(stress_worker) for _ in range(concurrent_users)]
            
            for future in as_completed(futures):
                results.extend(future.result())
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 分析压力测试结果
        total_requests = len(results)
        successful_requests = [r for r in results if r["status_code"] == 200]
        failed_requests = [r for r in results if r["status_code"] != 200]
        
        success_rate = len(successful_requests) / total_requests if total_requests > 0 else 0
        throughput = total_requests / total_duration
        
        if successful_requests:
            response_times = [r["response_time"] for r in successful_requests]
            avg_response_time = statistics.mean(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18]
        else:
            avg_response_time = 0
            p95_response_time = 0
        
        print(f"\n压力测试结果:")
        print(f"测试持续时间: {total_duration:.2f}s")
        print(f"并发用户数: {concurrent_users}")
        print(f"总请求数: {total_requests}")
        print(f"成功请求数: {len(successful_requests)}")
        print(f"失败请求数: {len(failed_requests)}")
        print(f"成功率: {success_rate:.2%}")
        print(f"吞吐量: {throughput:.2f} 请求/秒")
        print(f"平均响应时间: {avg_response_time:.3f}s")
        print(f"95%响应时间: {p95_response_time:.3f}s")
        
        # 压力测试断言
        assert success_rate >= 0.90  # 在压力下成功率应大于90%
        assert throughput >= 10  # 吞吐量应大于10请求/秒
        assert avg_response_time < 5.0  # 平均响应时间应小于5秒
