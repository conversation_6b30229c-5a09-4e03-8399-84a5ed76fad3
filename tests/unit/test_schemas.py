"""
Pydantic模式单元测试
"""
import pytest
from datetime import datetime
from pydantic import ValidationError

from app.models.schemas import (
    AuditRequest, AuditResponse, TaskStatusResponse, 
    HealthResponse, ErrorResponse, TaskStatus
)


@pytest.mark.unit
class TestAuditRequest:
    """AuditRequest模式测试"""
    
    def test_valid_audit_request(self):
        """测试有效的审核请求"""
        request_data = {
            "task_name": "测试审核任务",
            "config": "模型:\n  相关性模型: test-model",
            "main_doc": '{"title": "测试文档"}',
            "callback_url": "http://example.com/callback"
        }
        
        request = AuditRequest(**request_data)
        
        assert request.task_name == "测试审核任务"
        assert request.config == "模型:\n  相关性模型: test-model"
        assert request.main_doc == '{"title": "测试文档"}'
        assert request.callback_url == "http://example.com/callback"
    
    def test_minimal_audit_request(self):
        """测试最小审核请求（只有必需字段）"""
        request_data = {
            "task_name": "最小测试任务",
            "config": "模型:\n  相关性模型: test-model"
        }
        
        request = AuditRequest(**request_data)
        
        assert request.task_name == "最小测试任务"
        assert request.config == "模型:\n  相关性模型: test-model"
        assert request.main_doc is None
        assert request.callback_url is None
    
    def test_empty_task_name_validation(self):
        """测试空任务名称验证"""
        with pytest.raises(ValidationError) as exc_info:
            AuditRequest(
                task_name="",
                config="模型:\n  相关性模型: test-model"
            )
        
        errors = exc_info.value.errors()
        assert len(errors) > 0
    
    def test_empty_config_validation(self):
        """测试空配置验证"""
        with pytest.raises(ValidationError) as exc_info:
            AuditRequest(
                task_name="测试任务",
                config=""
            )
        
        errors = exc_info.value.errors()
        assert any("Config cannot be empty" in str(error) for error in errors)
    
    def test_whitespace_config_validation(self):
        """测试空白配置验证"""
        with pytest.raises(ValidationError) as exc_info:
            AuditRequest(
                task_name="测试任务",
                config="   \n\t  "
            )
        
        errors = exc_info.value.errors()
        assert any("Config cannot be empty" in str(error) for error in errors)


@pytest.mark.unit
class TestAuditResponse:
    """AuditResponse模式测试"""
    
    def test_pending_audit_response(self):
        """测试待处理审核响应"""
        response_data = {
            "task_id": "task-123",
            "status": TaskStatus.PENDING,
            "message": "任务已提交"
        }
        
        response = AuditResponse(**response_data)
        
        assert response.task_id == "task-123"
        assert response.status == TaskStatus.PENDING
        assert response.message == "任务已提交"
        assert response.data is None
    
    def test_completed_audit_response(self):
        """测试完成的审核响应"""
        result_data = {
            "audit_result": "通过",
            "score": 95,
            "details": "审核完成"
        }
        
        response_data = {
            "task_id": "task-456",
            "status": TaskStatus.COMPLETED,
            "message": "审核完成",
            "data": result_data
        }
        
        response = AuditResponse(**response_data)
        
        assert response.task_id == "task-456"
        assert response.status == TaskStatus.COMPLETED
        assert response.message == "审核完成"
        assert response.data == result_data


@pytest.mark.unit
class TestTaskStatusResponse:
    """TaskStatusResponse模式测试"""
    
    def test_complete_task_status_response(self):
        """测试完整的任务状态响应"""
        now = datetime.now()
        result_data = {"result": "success"}
        
        response_data = {
            "task_id": "task-789",
            "task_name": "测试任务",
            "status": TaskStatus.COMPLETED,
            "request_start_time": now,
            "request_end_time": now,
            "request_result": result_data,
            "exception_info": None,
            "created_at": now,
            "updated_at": now
        }
        
        response = TaskStatusResponse(**response_data)
        
        assert response.task_id == "task-789"
        assert response.task_name == "测试任务"
        assert response.status == TaskStatus.COMPLETED
        assert response.request_start_time == now
        assert response.request_end_time == now
        assert response.request_result == result_data
        assert response.exception_info is None
        assert response.created_at == now
        assert response.updated_at == now
    
    def test_failed_task_status_response(self):
        """测试失败的任务状态响应"""
        now = datetime.now()
        
        response_data = {
            "task_id": "task-failed",
            "task_name": "失败任务",
            "status": TaskStatus.FAILED,
            "request_start_time": now,
            "request_end_time": now,
            "request_result": None,
            "exception_info": "连接超时",
            "created_at": now,
            "updated_at": now
        }
        
        response = TaskStatusResponse(**response_data)
        
        assert response.task_id == "task-failed"
        assert response.status == TaskStatus.FAILED
        assert response.exception_info == "连接超时"
        assert response.request_result is None


@pytest.mark.unit
class TestHealthResponse:
    """HealthResponse模式测试"""
    
    def test_healthy_response(self):
        """测试健康响应"""
        now = datetime.now()
        dependencies = {
            "database": "healthy",
            "redis": "healthy",
            "celery": "healthy"
        }
        
        response_data = {
            "status": "healthy",
            "timestamp": now,
            "version": "1.0.0",
            "dependencies": dependencies
        }
        
        response = HealthResponse(**response_data)
        
        assert response.status == "healthy"
        assert response.timestamp == now
        assert response.version == "1.0.0"
        assert response.dependencies == dependencies
    
    def test_degraded_response(self):
        """测试降级响应"""
        now = datetime.now()
        dependencies = {
            "database": "healthy",
            "redis": "unhealthy: connection failed",
            "celery": "no workers available"
        }
        
        response_data = {
            "status": "degraded",
            "timestamp": now,
            "version": "1.0.0",
            "dependencies": dependencies
        }
        
        response = HealthResponse(**response_data)
        
        assert response.status == "degraded"
        assert response.dependencies["redis"] == "unhealthy: connection failed"


@pytest.mark.unit
class TestErrorResponse:
    """ErrorResponse模式测试"""
    
    def test_simple_error_response(self):
        """测试简单错误响应"""
        response_data = {
            "error": "ValidationError",
            "message": "输入数据无效"
        }
        
        response = ErrorResponse(**response_data)
        
        assert response.error == "ValidationError"
        assert response.message == "输入数据无效"
        assert response.details is None
    
    def test_detailed_error_response(self):
        """测试详细错误响应"""
        details = {
            "field": "config",
            "issue": "格式错误",
            "expected": "YAML格式"
        }
        
        response_data = {
            "error": "ConfigurationError",
            "message": "配置文件格式错误",
            "details": details
        }
        
        response = ErrorResponse(**response_data)
        
        assert response.error == "ConfigurationError"
        assert response.message == "配置文件格式错误"
        assert response.details == details


@pytest.mark.unit
class TestTaskStatus:
    """TaskStatus枚举测试"""
    
    def test_task_status_values(self):
        """测试任务状态值"""
        assert TaskStatus.PENDING.value == "pending"
        assert TaskStatus.PROCESSING.value == "processing"
        assert TaskStatus.COMPLETED.value == "completed"
        assert TaskStatus.FAILED.value == "failed"
    
    def test_task_status_serialization(self):
        """测试任务状态序列化"""
        response = AuditResponse(
            task_id="test",
            status=TaskStatus.PENDING,
            message="test"
        )
        
        data = response.model_dump()
        assert data["status"] == "pending"
