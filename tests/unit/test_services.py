"""
服务层单元测试
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from app.services.task_service import TaskService
from app.services.http_service import HTTPService, HTTPServiceError
from app.models.database import RequestTracking, TaskStatus


@pytest.mark.unit
class TestTaskService:
    """TaskService单元测试"""
    
    def test_create_task(self, test_db_session, sample_task_data):
        """测试创建任务"""
        task_service = TaskService(test_db_session)
        
        task_id = task_service.create_task(
            task_name=sample_task_data["task_name"],
            config=sample_task_data["config"],
            main_doc=sample_task_data["main_doc"]
        )
        
        assert task_id is not None
        assert len(task_id) == 36  # UUID长度
        
        # 验证数据库记录
        task_record = test_db_session.query(RequestTracking).filter_by(
            task_id=task_id
        ).first()
        
        assert task_record is not None
        assert task_record.task_name == sample_task_data["task_name"]
        assert task_record.status == TaskStatus.PENDING.value
    
    def test_get_task_status_existing(self, test_db_session, sample_task_record):
        """测试获取存在的任务状态"""
        task_service = TaskService(test_db_session)
        
        status_response = task_service.get_task_status(sample_task_record.task_id)
        
        assert status_response is not None
        assert status_response.task_id == sample_task_record.task_id
        assert status_response.task_name == sample_task_record.task_name
        assert status_response.status == TaskStatus.PENDING
    
    def test_get_task_status_nonexistent(self, test_db_session):
        """测试获取不存在的任务状态"""
        task_service = TaskService(test_db_session)
        
        status_response = task_service.get_task_status("nonexistent-task-id")
        
        assert status_response is None
    
    def test_update_task_status_to_processing(self, test_db_session, sample_task_record):
        """测试更新任务状态为处理中"""
        task_service = TaskService(test_db_session)
        
        task_service.update_task_status(
            sample_task_record.task_id,
            TaskStatus.PROCESSING.value
        )
        
        # 验证更新
        updated_record = test_db_session.query(RequestTracking).filter_by(
            task_id=sample_task_record.task_id
        ).first()
        
        assert updated_record.status == TaskStatus.PROCESSING.value
    
    def test_update_task_status_to_completed_with_result(self, test_db_session, sample_task_record):
        """测试更新任务状态为完成并设置结果"""
        task_service = TaskService(test_db_session)
        
        result_data = {
            "audit_result": "通过",
            "score": 95,
            "summary": "审核完成"
        }
        
        task_service.update_task_status(
            sample_task_record.task_id,
            TaskStatus.COMPLETED.value,
            result=result_data
        )
        
        # 验证更新
        updated_record = test_db_session.query(RequestTracking).filter_by(
            task_id=sample_task_record.task_id
        ).first()
        
        assert updated_record.status == TaskStatus.COMPLETED.value
        assert updated_record.request_result == result_data
        assert updated_record.request_end_time is not None
    
    def test_update_task_status_to_failed_with_exception(self, test_db_session, sample_task_record):
        """测试更新任务状态为失败并记录异常"""
        task_service = TaskService(test_db_session)
        
        exception_info = "连接超时：无法连接到外部服务"
        
        task_service.update_task_status(
            sample_task_record.task_id,
            TaskStatus.FAILED.value,
            exception_info=exception_info
        )
        
        # 验证更新
        updated_record = test_db_session.query(RequestTracking).filter_by(
            task_id=sample_task_record.task_id
        ).first()
        
        assert updated_record.status == TaskStatus.FAILED.value
        assert updated_record.exception_info == exception_info
        assert updated_record.request_end_time is not None
    
    def test_delete_task_data(self, test_db_session, sample_task_record):
        """测试删除任务数据"""
        task_service = TaskService(test_db_session)
        
        # 确认任务存在
        assert test_db_session.query(RequestTracking).filter_by(
            task_id=sample_task_record.task_id
        ).first() is not None
        
        # 删除任务数据
        task_service.delete_task_data(sample_task_record.task_id)
        
        # 验证删除
        deleted_record = test_db_session.query(RequestTracking).filter_by(
            task_id=sample_task_record.task_id
        ).first()
        
        assert deleted_record is None


@pytest.mark.unit
class TestHTTPService:
    """HTTPService单元测试"""
    
    def test_http_service_initialization(self, test_settings):
        """测试HTTP服务初始化"""
        http_service = HTTPService()
        
        assert http_service.timeout == test_settings.http_timeout
        assert http_service.max_retries == test_settings.http_max_retries
        assert http_service.retry_delay == test_settings.http_retry_delay
    
    @patch('requests.Session.post')
    def test_successful_post_request(self, mock_post):
        """测试成功的POST请求"""
        # Mock响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"result": "success"}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        http_service = HTTPService()
        
        response = http_service.post(
            url="http://test-api/endpoint",
            json_data={"test": "data"}
        )
        
        assert response.status_code == 200
        assert response.json() == {"result": "success"}
        mock_post.assert_called_once()
    
    @patch('requests.Session.get')
    def test_successful_get_request(self, mock_get):
        """测试成功的GET请求"""
        # Mock响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"data": "test"}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        http_service = HTTPService()
        
        response = http_service.get(
            url="http://test-api/data",
            params={"id": "123"}
        )
        
        assert response.status_code == 200
        assert response.json() == {"data": "test"}
        mock_get.assert_called_once()
    
    @patch('requests.Session.post')
    def test_request_with_retry_on_failure(self, mock_post):
        """测试请求失败时的重试机制"""
        # Mock第一次失败，第二次成功
        mock_response_fail = Mock()
        mock_response_fail.status_code = 500
        mock_response_fail.raise_for_status.side_effect = Exception("Server Error")
        
        mock_response_success = Mock()
        mock_response_success.status_code = 200
        mock_response_success.json.return_value = {"result": "success"}
        mock_response_success.raise_for_status.return_value = None
        
        mock_post.side_effect = [mock_response_fail, mock_response_success]
        
        http_service = HTTPService(max_retries=2, retry_delay=0.1)
        
        response = http_service.post(
            url="http://test-api/endpoint",
            json_data={"test": "data"}
        )
        
        assert response.status_code == 200
        assert mock_post.call_count == 2
    
    @patch('requests.Session.post')
    def test_request_max_retries_exceeded(self, mock_post):
        """测试超过最大重试次数"""
        # Mock所有请求都失败
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.raise_for_status.side_effect = Exception("Server Error")
        mock_post.return_value = mock_response
        
        http_service = HTTPService(max_retries=2, retry_delay=0.1)
        
        with pytest.raises(HTTPServiceError) as exc_info:
            http_service.post(
                url="http://test-api/endpoint",
                json_data={"test": "data"}
            )
        
        assert "Max retries exceeded" in str(exc_info.value)
        assert mock_post.call_count == 3  # 初始请求 + 2次重试
    
    @patch('requests.Session.post')
    def test_request_timeout(self, mock_post):
        """测试请求超时"""
        from requests.exceptions import Timeout
        
        mock_post.side_effect = Timeout("Request timeout")
        
        http_service = HTTPService(timeout=1, max_retries=1, retry_delay=0.1)
        
        with pytest.raises(HTTPServiceError) as exc_info:
            http_service.post(
                url="http://test-api/endpoint",
                json_data={"test": "data"}
            )
        
        assert "Request timeout" in str(exc_info.value)
    
    def test_build_headers_with_auth(self):
        """测试构建带认证的请求头"""
        http_service = HTTPService()
        
        headers = http_service._build_headers(
            custom_headers={"Authorization": "Bearer token123"}
        )
        
        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer token123"
        assert "User-Agent" in headers
    
    def test_build_headers_default(self):
        """测试构建默认请求头"""
        http_service = HTTPService()
        
        headers = http_service._build_headers()
        
        assert "User-Agent" in headers
        assert "Content-Type" in headers
        assert headers["Content-Type"] == "application/json"
