"""
工具函数单元测试
"""
import pytest
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, mock_open

from app.utils.file_utils import ensure_directory, get_file_size, is_valid_file_type
from app.utils.text_processing import auto_text_clean, adjust_indent, extract_json


@pytest.mark.unit
class TestFileUtils:
    """文件工具函数测试"""
    
    def test_ensure_directory_creates_new_directory(self, temp_dir):
        """测试创建新目录"""
        new_dir = os.path.join(temp_dir, "new_directory")
        
        # 确认目录不存在
        assert not os.path.exists(new_dir)
        
        # 创建目录
        result = ensure_directory(new_dir)
        
        # 验证结果
        assert result is True
        assert os.path.exists(new_dir)
        assert os.path.isdir(new_dir)
    
    def test_ensure_directory_existing_directory(self, temp_dir):
        """测试确保已存在的目录"""
        # 目录已存在
        result = ensure_directory(temp_dir)
        
        # 验证结果
        assert result is True
        assert os.path.exists(temp_dir)
    
    def test_ensure_directory_nested_path(self, temp_dir):
        """测试创建嵌套目录路径"""
        nested_dir = os.path.join(temp_dir, "level1", "level2", "level3")
        
        # 创建嵌套目录
        result = ensure_directory(nested_dir)
        
        # 验证结果
        assert result is True
        assert os.path.exists(nested_dir)
        assert os.path.isdir(nested_dir)
    
    def test_get_file_size_existing_file(self, temp_dir):
        """测试获取存在文件的大小"""
        # 创建测试文件
        test_file = os.path.join(temp_dir, "test_file.txt")
        test_content = "Hello, World!" * 100  # 约1300字节
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # 获取文件大小
        file_size = get_file_size(test_file)
        
        # 验证结果
        assert file_size > 0
        assert file_size == len(test_content.encode('utf-8'))
    
    def test_get_file_size_nonexistent_file(self):
        """测试获取不存在文件的大小"""
        file_size = get_file_size("nonexistent_file.txt")
        assert file_size == 0
    
    def test_is_valid_file_type_pdf(self):
        """测试PDF文件类型验证"""
        assert is_valid_file_type("document.pdf", ["pdf", "doc", "docx"]) is True
        assert is_valid_file_type("document.PDF", ["pdf", "doc", "docx"]) is True
    
    def test_is_valid_file_type_invalid(self):
        """测试无效文件类型"""
        assert is_valid_file_type("image.jpg", ["pdf", "doc", "docx"]) is False
        assert is_valid_file_type("script.py", ["pdf", "doc", "docx"]) is False
    
    def test_is_valid_file_type_no_extension(self):
        """测试无扩展名文件"""
        assert is_valid_file_type("filename", ["pdf", "doc", "docx"]) is False
        assert is_valid_file_type("", ["pdf", "doc", "docx"]) is False


@pytest.mark.unit
class TestTextProcessing:
    """文本处理工具函数测试"""
    
    def test_auto_text_clean_basic(self):
        """测试基本文本清理"""
        dirty_text = "  Hello,   World!  \n\n  "
        cleaned_text = auto_text_clean(dirty_text)
        
        assert cleaned_text == "Hello, World!"
    
    def test_auto_text_clean_multiple_spaces(self):
        """测试多余空格清理"""
        text_with_spaces = "This    has     multiple    spaces"
        cleaned_text = auto_text_clean(text_with_spaces)
        
        assert cleaned_text == "This has multiple spaces"
    
    def test_auto_text_clean_newlines_and_tabs(self):
        """测试换行符和制表符清理"""
        text_with_whitespace = "Line1\n\n\nLine2\t\t\tLine3"
        cleaned_text = auto_text_clean(text_with_whitespace)
        
        assert cleaned_text == "Line1 Line2 Line3"
    
    def test_auto_text_clean_empty_string(self):
        """测试空字符串清理"""
        assert auto_text_clean("") == ""
        assert auto_text_clean("   ") == ""
        assert auto_text_clean("\n\t\r") == ""
    
    def test_adjust_indent_increase(self):
        """测试增加缩进"""
        text = "Line 1\nLine 2\nLine 3"
        indented_text = adjust_indent(text, indent_level=2)
        
        expected = "  Line 1\n  Line 2\n  Line 3"
        assert indented_text == expected
    
    def test_adjust_indent_custom_char(self):
        """测试自定义缩进字符"""
        text = "Line 1\nLine 2"
        indented_text = adjust_indent(text, indent_level=1, indent_char="\t")
        
        expected = "\tLine 1\n\tLine 2"
        assert indented_text == expected
    
    def test_adjust_indent_zero_level(self):
        """测试零缩进级别"""
        text = "Line 1\nLine 2"
        indented_text = adjust_indent(text, indent_level=0)
        
        assert indented_text == text
    
    def test_extract_json_valid_json(self):
        """测试提取有效JSON"""
        text_with_json = 'Some text {"key": "value", "number": 42} more text'
        extracted_json = extract_json(text_with_json)
        
        assert extracted_json == {"key": "value", "number": 42}
    
    def test_extract_json_multiple_objects(self):
        """测试提取多个JSON对象（返回第一个）"""
        text_with_multiple = '{"first": 1} and {"second": 2}'
        extracted_json = extract_json(text_with_multiple)
        
        assert extracted_json == {"first": 1}
    
    def test_extract_json_no_json(self):
        """测试无JSON内容的文本"""
        text_without_json = "This is just plain text without any JSON"
        extracted_json = extract_json(text_without_json)
        
        assert extracted_json is None
    
    def test_extract_json_invalid_json(self):
        """测试无效JSON格式"""
        text_with_invalid_json = 'Some text {invalid: json} more text'
        extracted_json = extract_json(text_with_invalid_json)
        
        assert extracted_json is None
    
    def test_extract_json_nested_objects(self):
        """测试嵌套JSON对象"""
        text_with_nested = 'Result: {"outer": {"inner": {"value": 123}}, "array": [1, 2, 3]}'
        extracted_json = extract_json(text_with_nested)
        
        expected = {
            "outer": {"inner": {"value": 123}},
            "array": [1, 2, 3]
        }
        assert extracted_json == expected
    
    def test_extract_json_with_chinese_characters(self):
        """测试包含中文字符的JSON"""
        text_with_chinese = '结果：{"名称": "测试", "描述": "这是一个测试", "数量": 10}'
        extracted_json = extract_json(text_with_chinese)
        
        expected = {"名称": "测试", "描述": "这是一个测试", "数量": 10}
        assert extracted_json == expected


@pytest.mark.unit
class TestUtilsIntegration:
    """工具函数集成测试"""
    
    def test_file_and_text_processing_workflow(self, temp_dir):
        """测试文件和文本处理工作流"""
        # 1. 创建目录
        work_dir = os.path.join(temp_dir, "workflow_test")
        ensure_directory(work_dir)
        
        # 2. 创建包含需要清理文本的文件
        test_file = os.path.join(work_dir, "test_document.txt")
        dirty_content = "  Title:   Document   Processing  \n\n\n  Content with    extra spaces  "
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(dirty_content)
        
        # 3. 读取并清理文本
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        cleaned_content = auto_text_clean(content)
        
        # 4. 验证结果
        assert os.path.exists(work_dir)
        assert os.path.exists(test_file)
        assert get_file_size(test_file) > 0
        assert cleaned_content == "Title: Document Processing Content with extra spaces"
    
    @patch('builtins.open', new_callable=mock_open, read_data='{"result": "success"}')
    def test_json_extraction_from_file(self, mock_file):
        """测试从文件中提取JSON"""
        # Mock文件读取
        with open("mock_file.json", 'r') as f:
            content = f.read()
        
        extracted_json = extract_json(content)
        
        assert extracted_json == {"result": "success"}
        mock_file.assert_called_once_with("mock_file.json", 'r')
