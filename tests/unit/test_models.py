"""
数据库模型单元测试
"""
import pytest
from datetime import datetime
from sqlalchemy.exc import IntegrityError

from app.models.database import RequestTracking, TaskStatus


@pytest.mark.unit
@pytest.mark.database
class TestRequestTracking:
    """RequestTracking模型测试"""
    
    def test_create_request_tracking(self, test_db_session):
        """测试创建请求跟踪记录"""
        task_record = RequestTracking(
            task_id="test-task-001",
            task_name="测试任务",
            status=TaskStatus.PENDING.value
        )
        
        test_db_session.add(task_record)
        test_db_session.commit()
        test_db_session.refresh(task_record)
        
        assert task_record.id is not None
        assert task_record.task_id == "test-task-001"
        assert task_record.task_name == "测试任务"
        assert task_record.status == TaskStatus.PENDING.value
        assert task_record.request_start_time is not None
        assert task_record.request_end_time is None
        assert task_record.request_result is None
        assert task_record.exception_info is None
    
    def test_unique_task_id_constraint(self, test_db_session):
        """测试task_id唯一性约束"""
        # 创建第一个记录
        task_record1 = RequestTracking(
            task_id="duplicate-task-id",
            task_name="任务1",
            status=TaskStatus.PENDING.value
        )
        test_db_session.add(task_record1)
        test_db_session.commit()
        
        # 尝试创建相同task_id的记录
        task_record2 = RequestTracking(
            task_id="duplicate-task-id",
            task_name="任务2",
            status=TaskStatus.PENDING.value
        )
        test_db_session.add(task_record2)
        
        with pytest.raises(IntegrityError):
            test_db_session.commit()
    
    def test_update_task_status(self, test_db_session):
        """测试更新任务状态"""
        task_record = RequestTracking(
            task_id="test-task-002",
            task_name="测试任务",
            status=TaskStatus.PENDING.value
        )
        
        test_db_session.add(task_record)
        test_db_session.commit()
        
        # 更新状态
        task_record.status = TaskStatus.PROCESSING.value
        task_record.request_result = {"progress": 50}
        test_db_session.commit()
        
        # 验证更新
        updated_record = test_db_session.query(RequestTracking).filter_by(
            task_id="test-task-002"
        ).first()
        
        assert updated_record.status == TaskStatus.PROCESSING.value
        assert updated_record.request_result == {"progress": 50}
    
    def test_complete_task_with_result(self, test_db_session):
        """测试完成任务并设置结果"""
        task_record = RequestTracking(
            task_id="test-task-003",
            task_name="测试任务",
            status=TaskStatus.PENDING.value
        )
        
        test_db_session.add(task_record)
        test_db_session.commit()
        
        # 完成任务
        end_time = datetime.utcnow()
        result_data = {
            "audit_result": "通过",
            "score": 95,
            "details": "审核完成"
        }
        
        task_record.status = TaskStatus.COMPLETED.value
        task_record.request_end_time = end_time
        task_record.request_result = result_data
        test_db_session.commit()
        
        # 验证结果
        completed_record = test_db_session.query(RequestTracking).filter_by(
            task_id="test-task-003"
        ).first()
        
        assert completed_record.status == TaskStatus.COMPLETED.value
        assert completed_record.request_end_time == end_time
        assert completed_record.request_result == result_data
    
    def test_failed_task_with_exception(self, test_db_session):
        """测试失败任务并记录异常信息"""
        task_record = RequestTracking(
            task_id="test-task-004",
            task_name="测试任务",
            status=TaskStatus.PROCESSING.value
        )
        
        test_db_session.add(task_record)
        test_db_session.commit()
        
        # 任务失败
        exception_info = "连接超时：无法连接到外部服务"
        end_time = datetime.utcnow()
        
        task_record.status = TaskStatus.FAILED.value
        task_record.request_end_time = end_time
        task_record.exception_info = exception_info
        test_db_session.commit()
        
        # 验证失败记录
        failed_record = test_db_session.query(RequestTracking).filter_by(
            task_id="test-task-004"
        ).first()
        
        assert failed_record.status == TaskStatus.FAILED.value
        assert failed_record.request_end_time == end_time
        assert failed_record.exception_info == exception_info
    
    def test_query_by_status(self, test_db_session):
        """测试按状态查询任务"""
        # 创建不同状态的任务
        tasks = [
            RequestTracking(task_id="pending-1", task_name="待处理1", status=TaskStatus.PENDING.value),
            RequestTracking(task_id="pending-2", task_name="待处理2", status=TaskStatus.PENDING.value),
            RequestTracking(task_id="processing-1", task_name="处理中1", status=TaskStatus.PROCESSING.value),
            RequestTracking(task_id="completed-1", task_name="已完成1", status=TaskStatus.COMPLETED.value),
        ]
        
        for task in tasks:
            test_db_session.add(task)
        test_db_session.commit()
        
        # 查询待处理任务
        pending_tasks = test_db_session.query(RequestTracking).filter_by(
            status=TaskStatus.PENDING.value
        ).all()
        
        assert len(pending_tasks) == 2
        assert all(task.status == TaskStatus.PENDING.value for task in pending_tasks)
        
        # 查询处理中任务
        processing_tasks = test_db_session.query(RequestTracking).filter_by(
            status=TaskStatus.PROCESSING.value
        ).all()
        
        assert len(processing_tasks) == 1
        assert processing_tasks[0].task_id == "processing-1"


@pytest.mark.unit
class TestTaskStatus:
    """TaskStatus枚举测试"""
    
    def test_task_status_values(self):
        """测试任务状态枚举值"""
        assert TaskStatus.PENDING.value == "pending"
        assert TaskStatus.PROCESSING.value == "processing"
        assert TaskStatus.COMPLETED.value == "completed"
        assert TaskStatus.FAILED.value == "failed"
    
    def test_task_status_enum_members(self):
        """测试任务状态枚举成员"""
        statuses = list(TaskStatus)
        assert len(statuses) == 4
        assert TaskStatus.PENDING in statuses
        assert TaskStatus.PROCESSING in statuses
        assert TaskStatus.COMPLETED in statuses
        assert TaskStatus.FAILED in statuses
