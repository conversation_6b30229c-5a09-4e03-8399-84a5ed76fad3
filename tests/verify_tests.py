#!/usr/bin/env python3
"""
测试体系验证脚本
验证测试环境和测试文件的完整性
"""
import os
import sys
import importlib.util
from pathlib import Path


def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✓ {description}: {file_path}")
        return True
    else:
        print(f"✗ {description}: {file_path} (不存在)")
        return False


def check_python_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            compile(f.read(), file_path, 'exec')
        return True
    except SyntaxError as e:
        print(f"✗ 语法错误 {file_path}: {e}")
        return False
    except Exception as e:
        print(f"✗ 检查失败 {file_path}: {e}")
        return False


def check_imports(file_path):
    """检查Python文件的导入"""
    try:
        spec = importlib.util.spec_from_file_location("test_module", file_path)
        if spec is None:
            return False
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return True
    except ImportError as e:
        print(f"✗ 导入错误 {file_path}: {e}")
        return False
    except Exception as e:
        print(f"✗ 模块加载失败 {file_path}: {e}")
        return False


def main():
    """主验证函数"""
    print("🧪 测试体系验证")
    print("=" * 50)

    project_root = Path(__file__).parent.parent  # 现在脚本在tests/目录下
    tests_dir = Path(__file__).parent  # tests目录就是当前目录
    
    # 检查测试目录结构
    print("\n📁 检查测试目录结构:")
    
    required_files = [
        (tests_dir / "__init__.py", "测试模块初始化文件"),
        (tests_dir / "conftest.py", "全局测试配置"),
        (tests_dir / "pytest.ini", "pytest配置文件"),
        (tests_dir / "run_tests.py", "测试运行脚本"),
        (tests_dir / "factories.py", "测试数据工厂"),
        (tests_dir / "README.md", "测试文档"),
        (tests_dir / "docker-compose.test.yml", "Docker测试配置"),
    ]
    
    structure_ok = True
    for file_path, description in required_files:
        if not check_file_exists(file_path, description):
            structure_ok = False
    
    # 检查测试子目录
    test_subdirs = [
        (tests_dir / "unit", "单元测试目录"),
        (tests_dir / "integration", "集成测试目录"),
        (tests_dir / "e2e", "端到端测试目录"),
        (tests_dir / "performance", "性能测试目录"),
        (tests_dir / "mock_data", "Mock数据目录"),
    ]
    
    for dir_path, description in test_subdirs:
        if not check_file_exists(dir_path, description):
            structure_ok = False
    
    # 检查测试文件
    print("\n🧪 检查测试文件:")
    
    test_files = [
        tests_dir / "unit" / "test_models.py",
        tests_dir / "unit" / "test_schemas.py",
        tests_dir / "unit" / "test_services.py",
        tests_dir / "unit" / "test_utils.py",
        tests_dir / "integration" / "test_api_endpoints.py",
        tests_dir / "integration" / "test_database_operations.py",
        tests_dir / "integration" / "test_celery_tasks.py",
        tests_dir / "e2e" / "test_audit_workflow.py",
        tests_dir / "performance" / "test_load_testing.py",
    ]
    
    files_ok = True
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"✓ 测试文件: {test_file.relative_to(project_root)}")
            
            # 检查语法
            if not check_python_syntax(test_file):
                files_ok = False
        else:
            print(f"✗ 测试文件不存在: {test_file.relative_to(project_root)}")
            files_ok = False
    
    # 检查配置文件
    print("\n⚙️ 检查配置文件:")
    
    config_files = [
        (project_root / "requirements-test.txt", "测试依赖文件"),
        (project_root / "Makefile.test", "测试Makefile"),
    ]
    
    config_ok = True
    for file_path, description in config_files:
        if not check_file_exists(file_path, description):
            config_ok = False
    
    # 检查Mock数据
    print("\n🎭 检查Mock数据:")
    
    mock_files = [
        tests_dir / "mock_data" / "mappings" / "embedding_api.json",
        tests_dir / "mock_data" / "mappings" / "llm_api.json",
        tests_dir / "mock_data" / "mappings" / "kbs_api.json",
    ]
    
    mock_ok = True
    for mock_file in mock_files:
        if not check_file_exists(mock_file, f"Mock配置: {mock_file.name}"):
            mock_ok = False
    
    # 检查Python包依赖
    print("\n📦 检查Python包:")
    
    required_packages = [
        "pytest",
        "pytest-cov",
        "pytest-asyncio",
        "fastapi",
        "sqlalchemy",
    ]
    
    packages_ok = True
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✓ 包: {package}")
        except ImportError:
            print(f"✗ 包: {package} (未安装)")
            packages_ok = False
    
    # 尝试导入测试模块
    print("\n🔍 检查测试模块导入:")
    
    try:
        sys.path.insert(0, str(tests_dir))
        import conftest
        print("✓ conftest.py 导入成功")
        
        import factories
        print("✓ factories.py 导入成功")
        
        modules_ok = True
    except Exception as e:
        print(f"✗ 测试模块导入失败: {e}")
        modules_ok = False
    
    # 总结
    print("\n📊 验证总结:")
    print("=" * 50)
    
    all_checks = [
        ("目录结构", structure_ok),
        ("测试文件", files_ok),
        ("配置文件", config_ok),
        ("Mock数据", mock_ok),
        ("Python包", packages_ok),
        ("模块导入", modules_ok),
    ]
    
    all_ok = True
    for check_name, check_result in all_checks:
        status = "✓ 通过" if check_result else "✗ 失败"
        print(f"{check_name}: {status}")
        if not check_result:
            all_ok = False
    
    print("\n" + "=" * 50)
    if all_ok:
        print("🎉 测试体系验证通过！")
        print("\n可以运行以下命令开始测试:")
        print("  python tests/run_tests.py")
        print("  make -f Makefile.test test")
        print("  pytest tests/")
        return 0
    else:
        print("❌ 测试体系验证失败！")
        print("\n请检查上述失败项目并修复后重新验证。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
