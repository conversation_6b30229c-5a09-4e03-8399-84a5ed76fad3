"""
审核工作流端到端测试
"""
import pytest
import time
from unittest.mock import patch

from app.models.database import TaskStatus


@pytest.mark.e2e
@pytest.mark.slow
class TestAuditWorkflowE2E:
    """审核工作流端到端测试"""
    
    def test_complete_async_audit_workflow(self, test_client, mock_external_services, mock_celery):
        """测试完整的异步审核工作流"""
        # 1. 提交异步审核任务
        audit_request = {
            "task_name": "端到端测试任务",
            "config": """
模型:
  相关性模型: qwen2-7b-8k-simple_tasks_v1
  总结模型: Qwen2.5-72B-Instruct-GPTQ-Int4-kd
  温度: 1e-8

global:
  知识库地址: http://test-kbs/query
  知识库图谱: ["test_graph"]
            """.strip(),
            "main_doc": '{"title": "测试文档", "content": "这是一个测试文档的内容"}',
            "callback_url": "http://test-callback/webhook"
        }
        
        with patch('app.tasks.celery_tasks.process_audit_async') as mock_task:
            mock_task.delay.return_value = None
            
            # 提交任务
            response = test_client.post("/api/v1/audit/async", json=audit_request)
            
            assert response.status_code == 202
            data = response.json()
            task_id = data["task_id"]
            
            assert data["status"] == "pending"
            assert data["message"] == "任务已提交处理"
            
            # 验证Celery任务被调用
            mock_task.delay.assert_called_once()
        
        # 2. 查询任务状态
        status_response = test_client.get(f"/api/v1/audit/status/{task_id}")
        
        assert status_response.status_code == 200
        status_data = status_response.json()
        
        assert status_data["task_id"] == task_id
        assert status_data["task_name"] == "端到端测试任务"
        assert status_data["status"] == "pending"
        
        # 3. 模拟任务处理完成（更新数据库状态）
        # 在实际测试中，这里会等待Celery任务完成
        # 这里我们直接更新数据库状态来模拟任务完成
        from app.services.task_service import TaskService
        from app.core.database import get_db
        
        db_session = next(get_db())
        task_service = TaskService(db_session)
        
        # 模拟任务完成
        completion_result = {
            "audit_result": "通过",
            "score": 95,
            "summary": "文档审核通过，符合要求",
            "details": {
                "relevance_score": 0.92,
                "quality_score": 0.98,
                "completeness_score": 0.95
            }
        }
        
        task_service.update_task_status(
            task_id,
            TaskStatus.COMPLETED.value,
            result=completion_result
        )
        
        # 4. 再次查询任务状态，验证完成
        final_status_response = test_client.get(f"/api/v1/audit/status/{task_id}")
        
        assert final_status_response.status_code == 200
        final_status_data = final_status_response.json()
        
        assert final_status_data["task_id"] == task_id
        assert final_status_data["status"] == "completed"
        assert final_status_data["request_result"] == completion_result
        assert final_status_data["request_end_time"] is not None
    
    def test_complete_sync_audit_workflow(self, test_client, mock_external_services):
        """测试完整的同步审核工作流"""
        audit_request = {
            "task_name": "同步端到端测试",
            "config": """
模型:
  相关性模型: qwen2-7b-8k-simple_tasks_v1
  总结模型: Qwen2.5-72B-Instruct-GPTQ-Int4-kd
  温度: 1e-8

global:
  知识库地址: http://test-kbs/query
  知识库图谱: ["test_graph"]
            """.strip(),
            "main_doc": '{"title": "同步测试文档", "content": "这是同步测试文档的内容"}'
        }
        
        # Mock审核服务返回结果
        with patch('app.services.audit_service.AuditService.process_audit_sync') as mock_process:
            mock_result = {
                "audit_result": "通过",
                "score": 88,
                "summary": "同步审核完成",
                "processing_time": 2.5
            }
            mock_process.return_value = mock_result
            
            # 提交同步审核
            response = test_client.post("/api/v1/audit/sync", json=audit_request)
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["status"] == "completed"
            assert data["message"] == "审核成功"
            assert data["data"] == mock_result
            
            task_id = data["task_id"]
            
            # 验证任务记录已创建并完成
            status_response = test_client.get(f"/api/v1/audit/status/{task_id}")
            assert status_response.status_code == 200
            
            status_data = status_response.json()
            assert status_data["status"] == "completed"
            assert status_data["request_result"] == mock_result
    
    def test_audit_workflow_with_error_handling(self, test_client, mock_external_services):
        """测试带错误处理的审核工作流"""
        audit_request = {
            "task_name": "错误处理测试",
            "config": """
模型:
  相关性模型: qwen2-7b-8k-simple_tasks_v1
  总结模型: Qwen2.5-72B-Instruct-GPTQ-Int4-kd
  温度: 1e-8

global:
  知识库地址: http://test-kbs/query
  知识库图谱: ["test_graph"]
            """.strip(),
            "main_doc": '{"title": "错误测试文档", "content": "这是错误测试文档"}'
        }
        
        # Mock审核服务抛出异常
        with patch('app.services.audit_service.AuditService.process_audit_sync') as mock_process:
            mock_process.side_effect = Exception("外部服务连接失败")
            
            # 提交同步审核
            response = test_client.post("/api/v1/audit/sync", json=audit_request)
            
            assert response.status_code == 500
            data = response.json()
            
            assert "detail" in data
            assert "审核出错" in data["detail"]
    
    def test_task_cancellation_workflow(self, test_client, mock_celery):
        """测试任务取消工作流"""
        audit_request = {
            "task_name": "取消测试任务",
            "config": """
模型:
  相关性模型: qwen2-7b-8k-simple_tasks_v1
  总结模型: Qwen2.5-72B-Instruct-GPTQ-Int4-kd
  温度: 1e-8

global:
  知识库地址: http://test-kbs/query
  知识库图谱: ["test_graph"]
            """.strip(),
            "main_doc": '{"title": "取消测试文档", "content": "这是取消测试文档"}'
        }
        
        with patch('app.tasks.celery_tasks.process_audit_async') as mock_task:
            mock_task.delay.return_value = None
            
            # 1. 提交任务
            response = test_client.post("/api/v1/audit/async", json=audit_request)
            assert response.status_code == 202
            
            task_id = response.json()["task_id"]
            
            # 2. 验证任务状态为pending
            status_response = test_client.get(f"/api/v1/audit/status/{task_id}")
            assert status_response.status_code == 200
            assert status_response.json()["status"] == "pending"
            
            # 3. 取消任务
            cancel_response = test_client.delete(f"/api/v1/audit/task/{task_id}")
            assert cancel_response.status_code == 200
            
            cancel_data = cancel_response.json()
            assert "cancelled successfully" in cancel_data["message"]
            
            # 4. 验证任务状态已更新为failed
            final_status_response = test_client.get(f"/api/v1/audit/status/{task_id}")
            assert final_status_response.status_code == 200
            
            final_status_data = final_status_response.json()
            assert final_status_data["status"] == "failed"
            assert "cancelled by user" in final_status_data["exception_info"]


@pytest.mark.e2e
@pytest.mark.external
class TestExternalServiceIntegration:
    """外部服务集成测试"""
    
    @pytest.mark.skip(reason="需要真实的外部服务")
    def test_real_embedding_service_integration(self, test_client):
        """测试真实的embedding服务集成"""
        # 这个测试需要真实的embedding服务运行
        # 在CI/CD环境中可能需要跳过或使用测试服务
        pass
    
    @pytest.mark.skip(reason="需要真实的LLM服务")
    def test_real_llm_service_integration(self, test_client):
        """测试真实的LLM服务集成"""
        # 这个测试需要真实的LLM服务运行
        # 在CI/CD环境中可能需要跳过或使用测试服务
        pass
    
    @pytest.mark.skip(reason="需要真实的知识库服务")
    def test_real_knowledge_base_integration(self, test_client):
        """测试真实的知识库服务集成"""
        # 这个测试需要真实的知识库服务运行
        # 在CI/CD环境中可能需要跳过或使用测试服务
        pass


@pytest.mark.e2e
@pytest.mark.slow
class TestSystemPerformance:
    """系统性能端到端测试"""
    
    def test_concurrent_audit_requests(self, test_client, mock_external_services, mock_celery):
        """测试并发审核请求"""
        import concurrent.futures
        import threading
        
        audit_request = {
            "task_name": "并发测试任务",
            "config": """
模型:
  相关性模型: qwen2-7b-8k-simple_tasks_v1
  总结模型: Qwen2.5-72B-Instruct-GPTQ-Int4-kd
  温度: 1e-8

global:
  知识库地址: http://test-kbs/query
  知识库图谱: ["test_graph"]
            """.strip(),
            "main_doc": '{"title": "并发测试文档", "content": "这是并发测试文档"}'
        }
        
        with patch('app.tasks.celery_tasks.process_audit_async') as mock_task:
            mock_task.delay.return_value = None
            
            def submit_audit_request(index):
                request_data = audit_request.copy()
                request_data["task_name"] = f"并发测试任务 {index}"
                
                response = test_client.post("/api/v1/audit/async", json=request_data)
                return response
            
            # 并发提交10个审核请求
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(submit_audit_request, i) for i in range(10)]
                responses = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            # 验证所有请求都成功
            task_ids = set()
            for response in responses:
                assert response.status_code == 202
                data = response.json()
                assert data["status"] == "pending"
                task_ids.add(data["task_id"])
            
            # 验证所有任务ID都是唯一的
            assert len(task_ids) == 10
            
            # 验证Celery任务被调用了10次
            assert mock_task.delay.call_count == 10
    
    def test_system_load_handling(self, test_client, mock_external_services):
        """测试系统负载处理"""
        # 快速连续发送多个请求
        responses = []
        
        for i in range(20):
            audit_request = {
                "task_name": f"负载测试任务 {i}",
                "config": "模型:\n  相关性模型: test-model",
                "main_doc": f'{{"title": "负载测试文档 {i}", "content": "内容"}}'
            }
            
            with patch('app.services.audit_service.AuditService.process_audit_sync') as mock_process:
                mock_process.return_value = {"audit_result": "通过", "score": 90}
                
                response = test_client.post("/api/v1/audit/sync", json=audit_request)
                responses.append(response)
        
        # 验证所有请求都得到了响应
        assert len(responses) == 20
        
        # 验证大部分请求成功（允许少量失败）
        successful_responses = [r for r in responses if r.status_code == 200]
        assert len(successful_responses) >= 18  # 至少90%成功率
    
    def test_health_check_under_load(self, test_client):
        """测试负载下的健康检查"""
        import concurrent.futures
        
        def check_health():
            return test_client.get("/api/v1/health")
        
        # 并发发送健康检查请求
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(check_health) for _ in range(50)]
            responses = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # 验证所有健康检查都成功
        for response in responses:
            assert response.status_code == 200
            data = response.json()
            assert "status" in data
            assert data["status"] in ["healthy", "degraded"]
