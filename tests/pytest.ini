[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --color=yes
    --durations=10
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    slow: 慢速测试（运行时间较长）
    external: 需要外部服务的测试
    api: API接口测试
    database: 数据库相关测试
    celery: Celery任务测试
    redis: Redis相关测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# 最小版本要求
minversion = 6.0

# 并行测试配置
# 使用 pytest-xdist 进行并行测试
# 运行时使用: pytest -n auto
