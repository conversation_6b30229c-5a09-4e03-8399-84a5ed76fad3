# Docker测试环境配置
version: '3.8'

services:
  # 测试数据库
  test_mysql:
    image: mysql:8.0
    container_name: audit_test_mysql
    environment:
      MYSQL_ROOT_PASSWORD: test_root_password
      MYSQL_DATABASE: audit_test_db
      MYSQL_USER: audit_test_user
      MYSQL_PASSWORD: test_password
    ports:
      - "3307:3306"
    volumes:
      - test_mysql_data:/var/lib/mysql
      - ../docker/configs/mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-ptest_root_password"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - test_network

  # 测试Redis
  test_redis:
    image: redis:7-alpine
    container_name: audit_test_redis
    ports:
      - "6380:6379"
    volumes:
      - test_redis_data:/data
      - ../docker/configs/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - test_network

  # 测试应用
  test_app:
    build:
      context: ..
      dockerfile: docker/dockerfiles/Dockerfile.dev
    container_name: audit_test_app
    environment:
      - DATABASE_URL=mysql+pymysql://audit_test_user:test_password@test_mysql:3306/audit_test_db
      - REDIS_URL=redis://test_redis:6379/0
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - CELERY_BROKER_URL=redis://test_redis:6379/0
      - CELERY_RESULT_BACKEND=redis://test_redis:6379/0
      - EMBEDDING_API_URL=http://mock-embedding:8080/v1/
      - LLM_API_URL=http://mock-llm:8080/v1/
      - KBS_ADDRESS=http://mock-kbs:8080/query
      - ENABLE_METRICS=false
    ports:
      - "8002:8000"
    volumes:
      - ..:/app
      - test_uploads:/app/uploads
      - test_logs:/app/logs
    depends_on:
      test_mysql:
        condition: service_healthy
      test_redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - test_network

  # 测试Celery Worker
  test_celery_worker:
    build:
      context: ..
      dockerfile: docker/dockerfiles/Dockerfile.celery
    container_name: audit_test_celery_worker
    environment:
      - DATABASE_URL=mysql+pymysql://audit_test_user:test_password@test_mysql:3306/audit_test_db
      - REDIS_URL=redis://test_redis:6379/0
      - CELERY_BROKER_URL=redis://test_redis:6379/0
      - CELERY_RESULT_BACKEND=redis://test_redis:6379/0
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    volumes:
      - ..:/app
      - test_uploads:/app/uploads
      - test_logs:/app/logs
    depends_on:
      test_mysql:
        condition: service_healthy
      test_redis:
        condition: service_healthy
    networks:
      - test_network

  # Mock外部服务
  mock_services:
    image: wiremock/wiremock:latest
    container_name: audit_test_mock_services
    ports:
      - "8080:8080"
    volumes:
      - ./mock_data:/home/<USER>
    command: ["--global-response-templating", "--verbose"]
    networks:
      - test_network

  # 测试运行器
  test_runner:
    build:
      context: ..
      dockerfile: docker/dockerfiles/Dockerfile.dev
    container_name: audit_test_runner
    environment:
      - DATABASE_URL=mysql+pymysql://audit_test_user:test_password@test_mysql:3306/audit_test_db
      - REDIS_URL=redis://test_redis:6379/0
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - PYTHONPATH=/app
    volumes:
      - ..:/app
      - test_coverage:/app/htmlcov
      - test_reports:/app/test-reports
    working_dir: /app
    command: >
      sh -c "
        echo 'Waiting for services to be ready...' &&
        sleep 30 &&
        echo 'Running database migrations...' &&
        python create_tables.py &&
        echo 'Running tests...' &&
        python -m pytest tests/ -v --cov=app --cov-report=html --cov-report=term-missing --junitxml=test-reports/junit.xml
      "
    depends_on:
      test_mysql:
        condition: service_healthy
      test_redis:
        condition: service_healthy
    networks:
      - test_network

volumes:
  test_mysql_data:
    driver: local
  test_redis_data:
    driver: local
  test_uploads:
    driver: local
  test_logs:
    driver: local
  test_coverage:
    driver: local
  test_reports:
    driver: local

networks:
  test_network:
    driver: bridge
