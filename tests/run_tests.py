#!/usr/bin/env python3
"""
测试运行脚本
提供便捷的测试执行和报告生成功能
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path


def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    print(f"执行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return e.returncode, e.stdout, e.stderr


def run_tests(test_type="all", verbose=False, coverage=True, parallel=False, markers=None):
    """运行测试"""
    project_root = Path(__file__).parent.parent
    
    # 基础pytest命令
    cmd = ["python", "-m", "pytest"]
    
    # 添加测试路径
    if test_type == "unit":
        cmd.append("tests/unit")
    elif test_type == "integration":
        cmd.append("tests/integration")
    elif test_type == "e2e":
        cmd.append("tests/e2e")
    elif test_type == "performance":
        cmd.append("tests/performance")
    else:
        cmd.append("tests")
    
    # 添加选项
    if verbose:
        cmd.extend(["-v", "-s"])
    
    if coverage:
        cmd.extend([
            "--cov=app",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-report=xml"
        ])
    
    if parallel:
        cmd.extend(["-n", "auto"])
    
    # 添加标记过滤
    if markers:
        for marker in markers:
            cmd.extend(["-m", marker])
    
    # 添加其他有用的选项
    cmd.extend([
        "--tb=short",
        "--strict-markers",
        "--disable-warnings"
    ])
    
    # 运行测试
    returncode, stdout, stderr = run_command(cmd, cwd=project_root)
    
    print(stdout)
    if stderr:
        print("错误输出:", stderr)
    
    return returncode == 0


def generate_report():
    """生成测试报告"""
    project_root = Path(__file__).parent.parent
    
    print("生成测试报告...")
    
    # 生成HTML覆盖率报告
    if os.path.exists(project_root / "htmlcov" / "index.html"):
        print(f"HTML覆盖率报告: {project_root}/htmlcov/index.html")
    
    # 生成JUnit XML报告
    cmd = [
        "python", "-m", "pytest",
        "tests",
        "--junitxml=test-results.xml",
        "--tb=short"
    ]
    
    run_command(cmd, cwd=project_root)
    
    if os.path.exists(project_root / "test-results.xml"):
        print(f"JUnit XML报告: {project_root}/test-results.xml")


def clean_test_artifacts():
    """清理测试产生的文件"""
    project_root = Path(__file__).parent.parent
    
    artifacts = [
        ".coverage",
        "coverage.xml",
        "test-results.xml",
        "htmlcov",
        ".pytest_cache",
        "test.db"
    ]
    
    for artifact in artifacts:
        artifact_path = project_root / artifact
        if artifact_path.exists():
            if artifact_path.is_file():
                artifact_path.unlink()
                print(f"删除文件: {artifact}")
            elif artifact_path.is_dir():
                import shutil
                shutil.rmtree(artifact_path)
                print(f"删除目录: {artifact}")


def check_test_environment():
    """检查测试环境"""
    print("检查测试环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("警告: Python版本过低，建议使用3.8+")
    
    # 检查必需的包
    required_packages = [
        "pytest",
        "pytest-cov",
        "pytest-asyncio",
        "fastapi",
        "sqlalchemy",
        "redis"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package}")
    
    if missing_packages:
        print(f"\n缺少以下包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True


def main():
    parser = argparse.ArgumentParser(description="测试运行脚本")
    
    parser.add_argument(
        "test_type",
        choices=["all", "unit", "integration", "e2e", "performance"],
        default="all",
        nargs="?",
        help="测试类型"
    )
    
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "--no-coverage",
        action="store_true",
        help="禁用覆盖率报告"
    )
    
    parser.add_argument(
        "-p", "--parallel",
        action="store_true",
        help="并行运行测试"
    )
    
    parser.add_argument(
        "-m", "--markers",
        nargs="+",
        help="pytest标记过滤"
    )
    
    parser.add_argument(
        "--clean",
        action="store_true",
        help="清理测试产生的文件"
    )
    
    parser.add_argument(
        "--check-env",
        action="store_true",
        help="检查测试环境"
    )
    
    parser.add_argument(
        "--report",
        action="store_true",
        help="生成测试报告"
    )
    
    args = parser.parse_args()
    
    # 检查环境
    if args.check_env:
        if not check_test_environment():
            sys.exit(1)
        return
    
    # 清理文件
    if args.clean:
        clean_test_artifacts()
        return
    
    # 生成报告
    if args.report:
        generate_report()
        return
    
    # 检查测试环境
    if not check_test_environment():
        print("测试环境检查失败，继续运行可能会出现问题")
        response = input("是否继续? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    # 运行测试
    success = run_tests(
        test_type=args.test_type,
        verbose=args.verbose,
        coverage=not args.no_coverage,
        parallel=args.parallel,
        markers=args.markers
    )
    
    if success:
        print("\n✓ 所有测试通过!")
        
        # 自动生成报告
        if not args.no_coverage:
            generate_report()
    else:
        print("\n✗ 测试失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
