"""
测试数据工厂
用于创建测试数据和Mock对象
"""
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from unittest.mock import Mock

from app.models.database import RequestTracking, TaskStatus


class TaskDataFactory:
    """任务数据工厂"""
    
    @staticmethod
    def create_audit_request_data(
        task_name: str = "测试审核任务",
        config: Optional[str] = None,
        main_doc: Optional[str] = None,
        callback_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """创建审核请求数据"""
        if config is None:
            config = """
模型:
  相关性模型: qwen2-7b-8k-simple_tasks_v1
  总结模型: Qwen2.5-72B-Instruct-GPTQ-Int4-kd
  温度: 1e-8

global:
  知识库地址: http://test-kbs/query
  知识库图谱: ["test_graph"]
            """.strip()
        
        if main_doc is None:
            main_doc = '{"title": "测试文档", "content": "这是一个测试文档的内容"}'
        
        return {
            "task_name": task_name,
            "config": config,
            "main_doc": main_doc,
            "callback_url": callback_url
        }
    
    @staticmethod
    def create_task_record(
        task_id: Optional[str] = None,
        task_name: str = "测试任务",
        status: TaskStatus = TaskStatus.PENDING,
        request_start_time: Optional[datetime] = None,
        request_end_time: Optional[datetime] = None,
        request_result: Optional[Dict[str, Any]] = None,
        exception_info: Optional[str] = None
    ) -> RequestTracking:
        """创建任务记录"""
        if task_id is None:
            task_id = str(uuid.uuid4())
        
        if request_start_time is None:
            request_start_time = datetime.utcnow()
        
        return RequestTracking(
            task_id=task_id,
            task_name=task_name,
            status=status.value,
            request_start_time=request_start_time,
            request_end_time=request_end_time,
            request_result=request_result,
            exception_info=exception_info
        )
    
    @staticmethod
    def create_completed_task_record(
        task_id: Optional[str] = None,
        task_name: str = "已完成任务",
        result: Optional[Dict[str, Any]] = None
    ) -> RequestTracking:
        """创建已完成的任务记录"""
        if result is None:
            result = {
                "audit_result": "通过",
                "score": 95,
                "summary": "审核完成",
                "details": {
                    "relevance_score": 0.92,
                    "quality_score": 0.98,
                    "completeness_score": 0.95
                }
            }
        
        start_time = datetime.utcnow() - timedelta(minutes=5)
        end_time = datetime.utcnow()
        
        return TaskDataFactory.create_task_record(
            task_id=task_id,
            task_name=task_name,
            status=TaskStatus.COMPLETED,
            request_start_time=start_time,
            request_end_time=end_time,
            request_result=result
        )
    
    @staticmethod
    def create_failed_task_record(
        task_id: Optional[str] = None,
        task_name: str = "失败任务",
        exception_info: str = "处理失败"
    ) -> RequestTracking:
        """创建失败的任务记录"""
        start_time = datetime.utcnow() - timedelta(minutes=2)
        end_time = datetime.utcnow()
        
        return TaskDataFactory.create_task_record(
            task_id=task_id,
            task_name=task_name,
            status=TaskStatus.FAILED,
            request_start_time=start_time,
            request_end_time=end_time,
            exception_info=exception_info
        )


class MockFactory:
    """Mock对象工厂"""
    
    @staticmethod
    def create_mock_http_response(
        status_code: int = 200,
        json_data: Optional[Dict[str, Any]] = None,
        text: str = "Mock response"
    ) -> Mock:
        """创建Mock HTTP响应"""
        mock_response = Mock()
        mock_response.status_code = status_code
        mock_response.text = text
        
        if json_data is None:
            json_data = {"result": "success"}
        
        mock_response.json.return_value = json_data
        mock_response.raise_for_status.return_value = None
        
        return mock_response
    
    @staticmethod
    def create_mock_embedding_response(
        embedding: Optional[list] = None
    ) -> Dict[str, Any]:
        """创建Mock embedding响应"""
        if embedding is None:
            embedding = [0.1, 0.2, 0.3, 0.4, 0.5]
        
        return {
            "data": [{"embedding": embedding}],
            "model": "bge-m3",
            "usage": {"total_tokens": 10}
        }
    
    @staticmethod
    def create_mock_llm_response(
        content: str = "这是一个Mock LLM响应"
    ) -> Dict[str, Any]:
        """创建Mock LLM响应"""
        return {
            "choices": [{
                "message": {
                    "content": content,
                    "role": "assistant"
                },
                "finish_reason": "stop"
            }],
            "model": "Qwen2.5-72B-Instruct-GPTQ-Int4-kd",
            "usage": {
                "prompt_tokens": 50,
                "completion_tokens": 20,
                "total_tokens": 70
            }
        }
    
    @staticmethod
    def create_mock_kbs_response(
        result: str = "这是知识库查询结果"
    ) -> Dict[str, Any]:
        """创建Mock知识库响应"""
        return {
            "result": result,
            "confidence": 0.85,
            "sources": ["source1", "source2"],
            "query_time": 0.5
        }
    
    @staticmethod
    def create_mock_redis_client() -> Mock:
        """创建Mock Redis客户端"""
        mock_redis = Mock()
        mock_redis.ping.return_value = True
        mock_redis.get.return_value = None
        mock_redis.set.return_value = True
        mock_redis.delete.return_value = 1
        mock_redis.exists.return_value = False
        mock_redis.expire.return_value = True
        mock_redis.incr.return_value = 1
        mock_redis.decr.return_value = 0
        mock_redis.hget.return_value = None
        mock_redis.hset.return_value = True
        mock_redis.hdel.return_value = 1
        
        return mock_redis
    
    @staticmethod
    def create_mock_celery_task() -> Mock:
        """创建Mock Celery任务"""
        mock_task = Mock()
        mock_task.id = str(uuid.uuid4())
        mock_task.state = "PENDING"
        mock_task.result = None
        mock_task.info = {}
        
        # Mock delay方法
        mock_delay_result = Mock()
        mock_delay_result.id = mock_task.id
        mock_task.delay.return_value = mock_delay_result
        
        # Mock apply_async方法
        mock_task.apply_async.return_value = mock_delay_result
        
        return mock_task


class TestDataBuilder:
    """测试数据构建器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """重置构建器"""
        self._task_name = "测试任务"
        self._config = None
        self._main_doc = None
        self._callback_url = None
        self._status = TaskStatus.PENDING
        self._result = None
        self._exception_info = None
        return self
    
    def with_task_name(self, task_name: str):
        """设置任务名称"""
        self._task_name = task_name
        return self
    
    def with_config(self, config: str):
        """设置配置"""
        self._config = config
        return self
    
    def with_main_doc(self, main_doc: str):
        """设置主文档"""
        self._main_doc = main_doc
        return self
    
    def with_callback_url(self, callback_url: str):
        """设置回调URL"""
        self._callback_url = callback_url
        return self
    
    def with_status(self, status: TaskStatus):
        """设置状态"""
        self._status = status
        return self
    
    def with_result(self, result: Dict[str, Any]):
        """设置结果"""
        self._result = result
        return self
    
    def with_exception_info(self, exception_info: str):
        """设置异常信息"""
        self._exception_info = exception_info
        return self
    
    def build_audit_request(self) -> Dict[str, Any]:
        """构建审核请求数据"""
        return TaskDataFactory.create_audit_request_data(
            task_name=self._task_name,
            config=self._config,
            main_doc=self._main_doc,
            callback_url=self._callback_url
        )
    
    def build_task_record(self) -> RequestTracking:
        """构建任务记录"""
        return TaskDataFactory.create_task_record(
            task_name=self._task_name,
            status=self._status,
            request_result=self._result,
            exception_info=self._exception_info
        )


# 预定义的测试数据集
class TestDataSets:
    """预定义的测试数据集"""
    
    # 有效的审核请求数据
    VALID_AUDIT_REQUESTS = [
        {
            "task_name": "标准审核任务",
            "config": """
模型:
  相关性模型: qwen2-7b-8k-simple_tasks_v1
  总结模型: Qwen2.5-72B-Instruct-GPTQ-Int4-kd
  温度: 1e-8

global:
  知识库地址: http://test-kbs/query
  知识库图谱: ["test_graph"]
            """.strip(),
            "main_doc": '{"title": "标准文档", "content": "这是标准文档内容"}',
            "callback_url": "http://example.com/callback"
        },
        {
            "task_name": "简单审核任务",
            "config": "模型:\n  相关性模型: simple-model",
            "main_doc": '{"title": "简单文档", "content": "简单内容"}'
        },
        {
            "task_name": "复杂审核任务",
            "config": """
模型:
  相关性模型: qwen2-7b-8k-simple_tasks_v1
  总结模型: Qwen2.5-72B-Instruct-GPTQ-Int4-kd
  温度: 1e-8

global:
  知识库地址: http://test-kbs/query
  知识库图谱: ["graph1", "graph2", "graph3"]
  
参数:
  最大长度: 2048
  批处理大小: 16
  超时时间: 300
            """.strip(),
            "main_doc": '{"title": "复杂文档", "content": "这是一个包含复杂内容的文档，需要详细审核"}',
            "callback_url": "http://complex-callback.com/webhook"
        }
    ]
    
    # 无效的审核请求数据
    INVALID_AUDIT_REQUESTS = [
        {
            "task_name": "",  # 空任务名
            "config": "模型:\n  相关性模型: test-model"
        },
        {
            "task_name": "无效配置任务",
            "config": ""  # 空配置
        },
        {
            "task_name": "格式错误任务",
            "config": "invalid yaml: [unclosed bracket"
        }
    ]
    
    # 审核结果数据
    AUDIT_RESULTS = [
        {
            "audit_result": "通过",
            "score": 95,
            "summary": "文档质量优秀，完全符合要求",
            "details": {
                "relevance_score": 0.95,
                "quality_score": 0.98,
                "completeness_score": 0.92
            }
        },
        {
            "audit_result": "部分通过",
            "score": 75,
            "summary": "文档基本符合要求，但有改进空间",
            "details": {
                "relevance_score": 0.80,
                "quality_score": 0.75,
                "completeness_score": 0.70
            }
        },
        {
            "audit_result": "不通过",
            "score": 45,
            "summary": "文档质量不符合要求，需要重新编写",
            "details": {
                "relevance_score": 0.50,
                "quality_score": 0.40,
                "completeness_score": 0.45
            }
        }
    ]
