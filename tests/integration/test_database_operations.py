"""
数据库操作集成测试
"""
import pytest
from datetime import datetime, timedelta
from sqlalchemy import text

from app.models.database import RequestTracking, TaskStatus
from app.services.task_service import TaskService


@pytest.mark.integration
@pytest.mark.database
class TestDatabaseConnection:
    """数据库连接测试"""
    
    def test_database_connection(self, test_db_session):
        """测试数据库连接"""
        # 执行简单查询
        result = test_db_session.execute(text("SELECT 1 as test_value"))
        row = result.fetchone()
        
        assert row is not None
        assert row.test_value == 1
    
    def test_database_transaction_commit(self, test_db_session):
        """测试数据库事务提交"""
        # 创建记录
        task_record = RequestTracking(
            task_id="transaction-test-001",
            task_name="事务测试",
            status=TaskStatus.PENDING.value
        )
        
        test_db_session.add(task_record)
        test_db_session.commit()
        
        # 验证记录已保存
        saved_record = test_db_session.query(RequestTracking).filter_by(
            task_id="transaction-test-001"
        ).first()
        
        assert saved_record is not None
        assert saved_record.task_name == "事务测试"
    
    def test_database_transaction_rollback(self, test_db_session):
        """测试数据库事务回滚"""
        # 创建记录但不提交
        task_record = RequestTracking(
            task_id="rollback-test-001",
            task_name="回滚测试",
            status=TaskStatus.PENDING.value
        )
        
        test_db_session.add(task_record)
        
        # 验证记录在会话中存在
        session_record = test_db_session.query(RequestTracking).filter_by(
            task_id="rollback-test-001"
        ).first()
        assert session_record is not None
        
        # 回滚事务
        test_db_session.rollback()
        
        # 验证记录已被回滚
        rolled_back_record = test_db_session.query(RequestTracking).filter_by(
            task_id="rollback-test-001"
        ).first()
        assert rolled_back_record is None


@pytest.mark.integration
@pytest.mark.database
class TestRequestTrackingCRUD:
    """RequestTracking CRUD操作测试"""
    
    def test_create_multiple_records(self, test_db_session):
        """测试创建多条记录"""
        records = []
        for i in range(5):
            record = RequestTracking(
                task_id=f"batch-task-{i:03d}",
                task_name=f"批量任务 {i+1}",
                status=TaskStatus.PENDING.value
            )
            records.append(record)
            test_db_session.add(record)
        
        test_db_session.commit()
        
        # 验证所有记录都已创建
        saved_records = test_db_session.query(RequestTracking).filter(
            RequestTracking.task_id.like("batch-task-%")
        ).all()
        
        assert len(saved_records) == 5
        
        # 验证记录内容
        for i, record in enumerate(sorted(saved_records, key=lambda x: x.task_id)):
            assert record.task_id == f"batch-task-{i:03d}"
            assert record.task_name == f"批量任务 {i+1}"
    
    def test_update_record_status(self, test_db_session):
        """测试更新记录状态"""
        # 创建初始记录
        task_record = RequestTracking(
            task_id="update-test-001",
            task_name="更新测试",
            status=TaskStatus.PENDING.value
        )
        
        test_db_session.add(task_record)
        test_db_session.commit()
        
        # 更新状态
        task_record.status = TaskStatus.PROCESSING.value
        task_record.request_result = {"progress": 50}
        test_db_session.commit()
        
        # 验证更新
        updated_record = test_db_session.query(RequestTracking).filter_by(
            task_id="update-test-001"
        ).first()
        
        assert updated_record.status == TaskStatus.PROCESSING.value
        assert updated_record.request_result == {"progress": 50}
    
    def test_delete_record(self, test_db_session):
        """测试删除记录"""
        # 创建记录
        task_record = RequestTracking(
            task_id="delete-test-001",
            task_name="删除测试",
            status=TaskStatus.PENDING.value
        )
        
        test_db_session.add(task_record)
        test_db_session.commit()
        
        # 确认记录存在
        existing_record = test_db_session.query(RequestTracking).filter_by(
            task_id="delete-test-001"
        ).first()
        assert existing_record is not None
        
        # 删除记录
        test_db_session.delete(existing_record)
        test_db_session.commit()
        
        # 验证记录已删除
        deleted_record = test_db_session.query(RequestTracking).filter_by(
            task_id="delete-test-001"
        ).first()
        assert deleted_record is None
    
    def test_query_with_filters(self, test_db_session):
        """测试带过滤条件的查询"""
        # 创建不同状态的记录
        records = [
            RequestTracking(task_id="filter-pending-1", task_name="待处理1", status=TaskStatus.PENDING.value),
            RequestTracking(task_id="filter-pending-2", task_name="待处理2", status=TaskStatus.PENDING.value),
            RequestTracking(task_id="filter-processing-1", task_name="处理中1", status=TaskStatus.PROCESSING.value),
            RequestTracking(task_id="filter-completed-1", task_name="已完成1", status=TaskStatus.COMPLETED.value),
        ]
        
        for record in records:
            test_db_session.add(record)
        test_db_session.commit()
        
        # 查询待处理任务
        pending_tasks = test_db_session.query(RequestTracking).filter(
            RequestTracking.status == TaskStatus.PENDING.value,
            RequestTracking.task_id.like("filter-%")
        ).all()
        
        assert len(pending_tasks) == 2
        assert all(task.status == TaskStatus.PENDING.value for task in pending_tasks)
        
        # 查询非待处理任务
        non_pending_tasks = test_db_session.query(RequestTracking).filter(
            RequestTracking.status != TaskStatus.PENDING.value,
            RequestTracking.task_id.like("filter-%")
        ).all()
        
        assert len(non_pending_tasks) == 2
    
    def test_query_with_ordering(self, test_db_session):
        """测试带排序的查询"""
        # 创建记录（故意打乱时间顺序）
        base_time = datetime.utcnow()
        records = [
            RequestTracking(
                task_id="order-test-3",
                task_name="第三个",
                status=TaskStatus.PENDING.value,
                request_start_time=base_time + timedelta(minutes=2)
            ),
            RequestTracking(
                task_id="order-test-1",
                task_name="第一个",
                status=TaskStatus.PENDING.value,
                request_start_time=base_time
            ),
            RequestTracking(
                task_id="order-test-2",
                task_name="第二个",
                status=TaskStatus.PENDING.value,
                request_start_time=base_time + timedelta(minutes=1)
            ),
        ]
        
        for record in records:
            test_db_session.add(record)
        test_db_session.commit()
        
        # 按时间升序查询
        ordered_records = test_db_session.query(RequestTracking).filter(
            RequestTracking.task_id.like("order-test-%")
        ).order_by(RequestTracking.request_start_time.asc()).all()
        
        assert len(ordered_records) == 3
        assert ordered_records[0].task_id == "order-test-1"
        assert ordered_records[1].task_id == "order-test-2"
        assert ordered_records[2].task_id == "order-test-3"
        
        # 按时间降序查询
        desc_ordered_records = test_db_session.query(RequestTracking).filter(
            RequestTracking.task_id.like("order-test-%")
        ).order_by(RequestTracking.request_start_time.desc()).all()
        
        assert desc_ordered_records[0].task_id == "order-test-3"
        assert desc_ordered_records[1].task_id == "order-test-2"
        assert desc_ordered_records[2].task_id == "order-test-1"


@pytest.mark.integration
@pytest.mark.database
class TestTaskServiceIntegration:
    """TaskService与数据库集成测试"""
    
    def test_task_service_full_workflow(self, test_db_session):
        """测试TaskService完整工作流"""
        task_service = TaskService(test_db_session)
        
        # 1. 创建任务
        task_id = task_service.create_task(
            task_name="集成测试任务",
            config="模型:\n  相关性模型: test-model",
            main_doc='{"title": "测试文档"}'
        )
        
        assert task_id is not None
        
        # 2. 获取任务状态
        status_response = task_service.get_task_status(task_id)
        assert status_response is not None
        assert status_response.status == TaskStatus.PENDING
        
        # 3. 更新任务为处理中
        task_service.update_task_status(task_id, TaskStatus.PROCESSING.value)
        
        status_response = task_service.get_task_status(task_id)
        assert status_response.status == TaskStatus.PROCESSING
        
        # 4. 完成任务
        result_data = {"audit_result": "通过", "score": 95}
        task_service.update_task_status(
            task_id,
            TaskStatus.COMPLETED.value,
            result=result_data
        )
        
        status_response = task_service.get_task_status(task_id)
        assert status_response.status == TaskStatus.COMPLETED
        assert status_response.request_result == result_data
        assert status_response.request_end_time is not None
    
    def test_task_service_error_handling(self, test_db_session):
        """测试TaskService错误处理"""
        task_service = TaskService(test_db_session)
        
        # 1. 创建任务
        task_id = task_service.create_task(
            task_name="错误处理测试",
            config="模型:\n  相关性模型: test-model"
        )
        
        # 2. 更新任务为处理中
        task_service.update_task_status(task_id, TaskStatus.PROCESSING.value)
        
        # 3. 任务失败
        exception_info = "外部服务连接失败"
        task_service.update_task_status(
            task_id,
            TaskStatus.FAILED.value,
            exception_info=exception_info
        )
        
        # 4. 验证失败状态
        status_response = task_service.get_task_status(task_id)
        assert status_response.status == TaskStatus.FAILED
        assert status_response.exception_info == exception_info
        assert status_response.request_end_time is not None
    
    def test_concurrent_task_operations(self, test_db_session):
        """测试并发任务操作"""
        import concurrent.futures
        import threading
        
        task_service = TaskService(test_db_session)
        created_task_ids = []
        lock = threading.Lock()
        
        def create_task(index):
            task_id = task_service.create_task(
                task_name=f"并发任务 {index}",
                config="模型:\n  相关性模型: test-model"
            )
            with lock:
                created_task_ids.append(task_id)
            return task_id
        
        # 并发创建10个任务
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_task, i) for i in range(10)]
            task_ids = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # 验证所有任务都创建成功
        assert len(task_ids) == 10
        assert len(set(task_ids)) == 10  # 所有ID都是唯一的
        
        # 验证数据库中的记录
        records = test_db_session.query(RequestTracking).filter(
            RequestTracking.task_id.in_(task_ids)
        ).all()
        
        assert len(records) == 10
        assert all(record.status == TaskStatus.PENDING.value for record in records)
