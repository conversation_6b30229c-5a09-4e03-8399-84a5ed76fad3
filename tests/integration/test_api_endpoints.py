"""
API端点集成测试
"""
import pytest
import json
from fastapi import status
from unittest.mock import patch

from app.models.database import RequestTracking, TaskStatus


@pytest.mark.integration
@pytest.mark.api
class TestHealthEndpoints:
    """健康检查端点测试"""
    
    def test_health_check_success(self, test_client):
        """测试健康检查成功"""
        response = test_client.get("/api/v1/health")
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert "dependencies" in data
        
        # 验证依赖状态
        dependencies = data["dependencies"]
        assert "database" in dependencies
        assert dependencies["database"] == "healthy"
    
    def test_health_check_with_redis_failure(self, test_client, mock_redis):
        """测试Redis失败时的健康检查"""
        # Mock Redis连接失败
        mock_redis.ping.side_effect = Exception("Redis connection failed")
        
        response = test_client.get("/api/v1/health")
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        # 即使Redis失败，只要数据库健康，整体状态应该是健康或降级
        assert data["status"] in ["healthy", "degraded"]


@pytest.mark.integration
@pytest.mark.api
class TestAuditEndpoints:
    """审核端点测试"""
    
    def test_audit_async_success(self, test_client, sample_task_data, mock_celery):
        """测试异步审核提交成功"""
        with patch('app.tasks.celery_tasks.process_audit_async') as mock_task:
            mock_task.delay.return_value = None
            
            response = test_client.post(
                "/api/v1/audit/async",
                json=sample_task_data
            )
            
            assert response.status_code == status.HTTP_202_ACCEPTED
            
            data = response.json()
            assert "task_id" in data
            assert data["status"] == "pending"
            assert data["message"] == "任务已提交处理"
            
            # 验证Celery任务被调用
            mock_task.delay.assert_called_once()
    
    def test_audit_async_invalid_config(self, test_client):
        """测试异步审核无效配置"""
        invalid_data = {
            "task_name": "test_task",
            "config": "",  # 空配置
            "main_doc": '{"title": "test"}'
        }
        
        response = test_client.post(
            "/api/v1/audit/async",
            json=invalid_data
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_audit_sync_success(self, test_client, sample_task_data, mock_external_services):
        """测试同步审核成功"""
        with patch('app.services.audit_service.AuditService.process_audit_sync') as mock_process:
            mock_result = {
                "audit_result": "通过",
                "score": 95,
                "summary": "审核完成"
            }
            mock_process.return_value = mock_result
            
            response = test_client.post(
                "/api/v1/audit/sync",
                json=sample_task_data
            )
            
            assert response.status_code == status.HTTP_200_OK
            
            data = response.json()
            assert "task_id" in data
            assert data["status"] == "completed"
            assert data["message"] == "审核成功"
            assert data["data"] == mock_result
    
    def test_audit_sync_processing_error(self, test_client, sample_task_data):
        """测试同步审核处理错误"""
        with patch('app.services.audit_service.AuditService.process_audit_sync') as mock_process:
            mock_process.side_effect = Exception("处理失败")
            
            response = test_client.post(
                "/api/v1/audit/sync",
                json=sample_task_data
            )
            
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            
            data = response.json()
            assert "detail" in data
            assert "审核出错" in data["detail"]
    
    def test_get_task_status_existing(self, test_client, test_db_session, sample_task_record):
        """测试获取存在任务的状态"""
        response = test_client.get(f"/api/v1/audit/status/{sample_task_record.task_id}")
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["task_id"] == sample_task_record.task_id
        assert data["task_name"] == sample_task_record.task_name
        assert data["status"] == sample_task_record.status
    
    def test_get_task_status_nonexistent(self, test_client):
        """测试获取不存在任务的状态"""
        response = test_client.get("/api/v1/audit/status/nonexistent-task-id")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"]
    
    def test_cancel_task_pending(self, test_client, test_db_session, sample_task_record):
        """测试取消待处理任务"""
        # 确保任务状态为pending
        sample_task_record.status = TaskStatus.PENDING.value
        test_db_session.commit()
        
        response = test_client.delete(f"/api/v1/audit/task/{sample_task_record.task_id}")
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert "message" in data
        assert "cancelled successfully" in data["message"]
        
        # 验证任务状态已更新为failed
        test_db_session.refresh(sample_task_record)
        assert sample_task_record.status == TaskStatus.FAILED.value
        assert "cancelled by user" in sample_task_record.exception_info
    
    def test_cancel_task_processing(self, test_client, test_db_session, sample_task_record):
        """测试取消处理中任务（应该失败）"""
        # 设置任务状态为processing
        sample_task_record.status = TaskStatus.PROCESSING.value
        test_db_session.commit()
        
        response = test_client.delete(f"/api/v1/audit/task/{sample_task_record.task_id}")
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        data = response.json()
        assert "detail" in data
        assert "Cannot cancel task" in data["detail"]
    
    def test_cancel_task_nonexistent(self, test_client):
        """测试取消不存在的任务"""
        response = test_client.delete("/api/v1/audit/task/nonexistent-task-id")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.integration
@pytest.mark.api
class TestRootEndpoints:
    """根端点测试"""
    
    def test_root_endpoint(self, test_client, test_settings):
        """测试根端点"""
        response = test_client.get("/")
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert "service" in data
        assert "version" in data
        assert "status" in data
        assert data["service"] == test_settings.app_name
        assert data["status"] == "running"
    
    def test_metrics_endpoint_disabled(self, test_client, test_settings):
        """测试禁用的指标端点"""
        # 测试设置中metrics是禁用的
        response = test_client.get("/metrics")
        
        # 如果metrics禁用，应该返回404
        assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.integration
@pytest.mark.api
class TestErrorHandling:
    """错误处理测试"""
    
    def test_validation_error_handling(self, test_client):
        """测试验证错误处理"""
        invalid_data = {
            "task_name": "",  # 空任务名
            "config": "invalid config"
        }
        
        response = test_client.post(
            "/api/v1/audit/async",
            json=invalid_data
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        data = response.json()
        assert "detail" in data
        assert isinstance(data["detail"], list)
    
    def test_method_not_allowed(self, test_client):
        """测试不允许的HTTP方法"""
        response = test_client.put("/api/v1/health")
        
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
    
    def test_not_found_endpoint(self, test_client):
        """测试不存在的端点"""
        response = test_client.get("/api/v1/nonexistent")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_malformed_json(self, test_client):
        """测试格式错误的JSON"""
        response = test_client.post(
            "/api/v1/audit/async",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.integration
@pytest.mark.api
@pytest.mark.slow
class TestConcurrentRequests:
    """并发请求测试"""
    
    def test_concurrent_health_checks(self, test_client):
        """测试并发健康检查"""
        import concurrent.futures
        import threading
        
        def make_health_request():
            return test_client.get("/api/v1/health")
        
        # 并发发送10个健康检查请求
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_health_request) for _ in range(10)]
            responses = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # 验证所有请求都成功
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "status" in data
    
    def test_concurrent_task_creation(self, test_client, sample_task_data, mock_celery):
        """测试并发任务创建"""
        import concurrent.futures
        
        with patch('app.tasks.celery_tasks.process_audit_async') as mock_task:
            mock_task.delay.return_value = None
            
            def create_task():
                return test_client.post("/api/v1/audit/async", json=sample_task_data)
            
            # 并发创建5个任务
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                futures = [executor.submit(create_task) for _ in range(5)]
                responses = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            # 验证所有任务创建成功
            task_ids = set()
            for response in responses:
                assert response.status_code == status.HTTP_202_ACCEPTED
                data = response.json()
                assert "task_id" in data
                task_ids.add(data["task_id"])
            
            # 验证所有任务ID都是唯一的
            assert len(task_ids) == 5
