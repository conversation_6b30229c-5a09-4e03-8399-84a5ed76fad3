"""
Celery任务集成测试
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from celery.exceptions import Retry

from app.tasks.celery_tasks import process_audit_async, celery_app
from app.models.database import RequestTracking, TaskStatus


@pytest.mark.integration
@pytest.mark.celery
class TestCeleryTaskExecution:
    """Celery任务执行测试"""
    
    @patch('app.tasks.celery_tasks.AuditService')
    @patch('app.tasks.celery_tasks.TaskService')
    @patch('app.tasks.celery_tasks.get_db')
    def test_process_audit_async_success(self, mock_get_db, mock_task_service_class, mock_audit_service_class):
        """测试异步审核任务成功执行"""
        # Mock数据库会话
        mock_db_session = Mock()
        mock_get_db.return_value.__enter__.return_value = mock_db_session
        
        # Mock TaskService
        mock_task_service = Mock()
        mock_task_service_class.return_value = mock_task_service
        
        # Mock AuditService
        mock_audit_service = Mock()
        mock_audit_result = {
            "audit_result": "通过",
            "score": 95,
            "summary": "审核完成"
        }
        mock_audit_service.process_audit_sync.return_value = mock_audit_result
        mock_audit_service_class.return_value = mock_audit_service
        
        # 执行任务
        task_data = {
            "task_id": "test-task-001",
            "task_name": "测试任务",
            "config": "模型:\n  相关性模型: test-model",
            "main_doc": '{"title": "测试文档"}',
            "callback_url": None
        }
        
        result = process_audit_async(**task_data)
        
        # 验证结果
        assert result == mock_audit_result
        
        # 验证服务调用
        mock_task_service.update_task_status.assert_any_call(
            "test-task-001",
            TaskStatus.PROCESSING.value
        )
        mock_task_service.update_task_status.assert_any_call(
            "test-task-001",
            TaskStatus.COMPLETED.value,
            result=mock_audit_result
        )
        
        mock_audit_service.process_audit_sync.assert_called_once()
    
    @patch('app.tasks.celery_tasks.AuditService')
    @patch('app.tasks.celery_tasks.TaskService')
    @patch('app.tasks.celery_tasks.get_db')
    def test_process_audit_async_failure(self, mock_get_db, mock_task_service_class, mock_audit_service_class):
        """测试异步审核任务执行失败"""
        # Mock数据库会话
        mock_db_session = Mock()
        mock_get_db.return_value.__enter__.return_value = mock_db_session
        
        # Mock TaskService
        mock_task_service = Mock()
        mock_task_service_class.return_value = mock_task_service
        
        # Mock AuditService抛出异常
        mock_audit_service = Mock()
        mock_audit_service.process_audit_sync.side_effect = Exception("处理失败")
        mock_audit_service_class.return_value = mock_audit_service
        
        # 执行任务
        task_data = {
            "task_id": "test-task-002",
            "task_name": "失败任务",
            "config": "模型:\n  相关性模型: test-model",
            "main_doc": '{"title": "测试文档"}',
            "callback_url": None
        }
        
        with pytest.raises(Exception) as exc_info:
            process_audit_async(**task_data)
        
        assert "处理失败" in str(exc_info.value)
        
        # 验证失败状态更新
        mock_task_service.update_task_status.assert_any_call(
            "test-task-002",
            TaskStatus.PROCESSING.value
        )
        mock_task_service.update_task_status.assert_any_call(
            "test-task-002",
            TaskStatus.FAILED.value,
            exception_info="处理失败"
        )
    
    @patch('app.tasks.celery_tasks.HTTPService')
    @patch('app.tasks.celery_tasks.AuditService')
    @patch('app.tasks.celery_tasks.TaskService')
    @patch('app.tasks.celery_tasks.get_db')
    def test_process_audit_async_with_callback(self, mock_get_db, mock_task_service_class, 
                                               mock_audit_service_class, mock_http_service_class):
        """测试带回调的异步审核任务"""
        # Mock数据库会话
        mock_db_session = Mock()
        mock_get_db.return_value.__enter__.return_value = mock_db_session
        
        # Mock TaskService
        mock_task_service = Mock()
        mock_task_service_class.return_value = mock_task_service
        
        # Mock AuditService
        mock_audit_service = Mock()
        mock_audit_result = {"audit_result": "通过", "score": 95}
        mock_audit_service.process_audit_sync.return_value = mock_audit_result
        mock_audit_service_class.return_value = mock_audit_service
        
        # Mock HTTPService
        mock_http_service = Mock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_http_service.post.return_value = mock_response
        mock_http_service_class.return_value = mock_http_service
        
        # 执行任务
        task_data = {
            "task_id": "test-task-003",
            "task_name": "回调任务",
            "config": "模型:\n  相关性模型: test-model",
            "main_doc": '{"title": "测试文档"}',
            "callback_url": "http://example.com/callback"
        }
        
        result = process_audit_async(**task_data)
        
        # 验证结果
        assert result == mock_audit_result
        
        # 验证回调调用
        mock_http_service.post.assert_called_once()
        call_args = mock_http_service.post.call_args
        assert call_args[1]["url"] == "http://example.com/callback"
        assert "json_data" in call_args[1]
        
        callback_data = call_args[1]["json_data"]
        assert callback_data["task_id"] == "test-task-003"
        assert callback_data["status"] == "completed"
        assert callback_data["data"] == mock_audit_result
    
    @patch('app.tasks.celery_tasks.HTTPService')
    @patch('app.tasks.celery_tasks.AuditService')
    @patch('app.tasks.celery_tasks.TaskService')
    @patch('app.tasks.celery_tasks.get_db')
    def test_process_audit_async_callback_failure(self, mock_get_db, mock_task_service_class,
                                                   mock_audit_service_class, mock_http_service_class):
        """测试回调失败的处理"""
        # Mock数据库会话
        mock_db_session = Mock()
        mock_get_db.return_value.__enter__.return_value = mock_db_session
        
        # Mock TaskService
        mock_task_service = Mock()
        mock_task_service_class.return_value = mock_task_service
        
        # Mock AuditService
        mock_audit_service = Mock()
        mock_audit_result = {"audit_result": "通过", "score": 95}
        mock_audit_service.process_audit_sync.return_value = mock_audit_result
        mock_audit_service_class.return_value = mock_audit_service
        
        # Mock HTTPService回调失败
        mock_http_service = Mock()
        mock_http_service.post.side_effect = Exception("回调失败")
        mock_http_service_class.return_value = mock_http_service
        
        # 执行任务
        task_data = {
            "task_id": "test-task-004",
            "task_name": "回调失败任务",
            "config": "模型:\n  相关性模型: test-model",
            "main_doc": '{"title": "测试文档"}',
            "callback_url": "http://example.com/callback"
        }
        
        # 任务应该成功完成，即使回调失败
        result = process_audit_async(**task_data)
        
        # 验证任务结果正常
        assert result == mock_audit_result
        
        # 验证任务状态仍然是完成
        mock_task_service.update_task_status.assert_any_call(
            "test-task-004",
            TaskStatus.COMPLETED.value,
            result=mock_audit_result
        )


@pytest.mark.integration
@pytest.mark.celery
class TestCeleryConfiguration:
    """Celery配置测试"""
    
    def test_celery_app_configuration(self):
        """测试Celery应用配置"""
        # 验证Celery应用存在
        assert celery_app is not None
        
        # 验证任务注册
        registered_tasks = celery_app.tasks
        assert 'app.tasks.celery_tasks.process_audit_async' in registered_tasks
    
    def test_task_registration(self):
        """测试任务注册"""
        # 获取注册的任务
        task = celery_app.tasks.get('app.tasks.celery_tasks.process_audit_async')
        
        assert task is not None
        assert hasattr(task, 'delay')
        assert hasattr(task, 'apply_async')
    
    @patch('app.tasks.celery_tasks.process_audit_async.retry')
    def test_task_retry_mechanism(self, mock_retry):
        """测试任务重试机制"""
        # Mock重试异常
        mock_retry.side_effect = Retry("Retrying task")
        
        with patch('app.tasks.celery_tasks.AuditService') as mock_audit_service_class:
            with patch('app.tasks.celery_tasks.TaskService') as mock_task_service_class:
                with patch('app.tasks.celery_tasks.get_db') as mock_get_db:
                    
                    # Mock数据库会话
                    mock_db_session = Mock()
                    mock_get_db.return_value.__enter__.return_value = mock_db_session
                    
                    # Mock服务
                    mock_task_service = Mock()
                    mock_task_service_class.return_value = mock_task_service
                    
                    mock_audit_service = Mock()
                    # 第一次调用失败，触发重试
                    mock_audit_service.process_audit_sync.side_effect = Exception("临时失败")
                    mock_audit_service_class.return_value = mock_audit_service
                    
                    # 执行任务
                    task_data = {
                        "task_id": "retry-task-001",
                        "task_name": "重试任务",
                        "config": "模型:\n  相关性模型: test-model",
                        "main_doc": '{"title": "测试文档"}',
                        "callback_url": None
                    }
                    
                    # 应该触发重试
                    with pytest.raises(Retry):
                        process_audit_async(**task_data)
                    
                    # 验证重试被调用
                    mock_retry.assert_called_once()


@pytest.mark.integration
@pytest.mark.celery
@pytest.mark.slow
class TestCeleryTaskPerformance:
    """Celery任务性能测试"""
    
    @patch('app.tasks.celery_tasks.AuditService')
    @patch('app.tasks.celery_tasks.TaskService')
    @patch('app.tasks.celery_tasks.get_db')
    def test_task_execution_time(self, mock_get_db, mock_task_service_class, mock_audit_service_class):
        """测试任务执行时间"""
        import time
        
        # Mock数据库会话
        mock_db_session = Mock()
        mock_get_db.return_value.__enter__.return_value = mock_db_session
        
        # Mock TaskService
        mock_task_service = Mock()
        mock_task_service_class.return_value = mock_task_service
        
        # Mock AuditService（模拟处理时间）
        mock_audit_service = Mock()
        def slow_process(*args, **kwargs):
            time.sleep(0.1)  # 模拟100ms处理时间
            return {"audit_result": "通过", "score": 95}
        
        mock_audit_service.process_audit_sync.side_effect = slow_process
        mock_audit_service_class.return_value = mock_audit_service
        
        # 执行任务并测量时间
        task_data = {
            "task_id": "perf-task-001",
            "task_name": "性能测试任务",
            "config": "模型:\n  相关性模型: test-model",
            "main_doc": '{"title": "测试文档"}',
            "callback_url": None
        }
        
        start_time = time.time()
        result = process_audit_async(**task_data)
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # 验证任务完成
        assert result["audit_result"] == "通过"
        
        # 验证执行时间合理（应该大于100ms但小于1秒）
        assert 0.1 <= execution_time < 1.0
    
    @patch('app.tasks.celery_tasks.AuditService')
    @patch('app.tasks.celery_tasks.TaskService')
    @patch('app.tasks.celery_tasks.get_db')
    def test_concurrent_task_execution(self, mock_get_db, mock_task_service_class, mock_audit_service_class):
        """测试并发任务执行"""
        import concurrent.futures
        import threading
        
        # Mock数据库会话
        mock_db_session = Mock()
        mock_get_db.return_value.__enter__.return_value = mock_db_session
        
        # Mock TaskService
        mock_task_service = Mock()
        mock_task_service_class.return_value = mock_task_service
        
        # Mock AuditService
        mock_audit_service = Mock()
        mock_audit_service.process_audit_sync.return_value = {"audit_result": "通过", "score": 95}
        mock_audit_service_class.return_value = mock_audit_service
        
        def execute_task(task_index):
            task_data = {
                "task_id": f"concurrent-task-{task_index:03d}",
                "task_name": f"并发任务 {task_index}",
                "config": "模型:\n  相关性模型: test-model",
                "main_doc": '{"title": "测试文档"}',
                "callback_url": None
            }
            return process_audit_async(**task_data)
        
        # 并发执行5个任务
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(execute_task, i) for i in range(5)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # 验证所有任务都成功完成
        assert len(results) == 5
        for result in results:
            assert result["audit_result"] == "通过"
            assert result["score"] == 95
