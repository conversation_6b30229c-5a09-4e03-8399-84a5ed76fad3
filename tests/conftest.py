"""
测试配置文件
提供全局测试fixtures和配置
"""
import os
import sys
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from unittest.mock import Mock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from fastapi.testclient import TestClient
from redis import Redis
import tempfile
import shutil

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.main import app
from app.core.database import Base, get_db
from app.core.config import get_settings
from app.core.redis import get_redis
from app.models.database import RequestTracking, TaskStatus


# 测试配置
TEST_DATABASE_URL = "sqlite:///./test.db"
TEST_REDIS_URL = "redis://localhost:6379/15"  # 使用不同的数据库


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环用于异步测试"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_settings():
    """测试环境配置"""
    with patch.dict(os.environ, {
        "DATABASE_URL": TEST_DATABASE_URL,
        "REDIS_URL": TEST_REDIS_URL,
        "DEBUG": "true",
        "LOG_LEVEL": "DEBUG",
        "CELERY_BROKER_URL": TEST_REDIS_URL,
        "CELERY_RESULT_BACKEND": TEST_REDIS_URL,
        "EMBEDDING_API_URL": "http://mock-embedding-api/v1/",
        "LLM_API_URL": "http://mock-llm-api/v1/",
        "KBS_ADDRESS": "http://mock-kbs/query",
        "ENABLE_METRICS": "false",
    }):
        settings = get_settings()
        yield settings


@pytest.fixture(scope="session")
def test_engine(test_settings):
    """测试数据库引擎"""
    engine = create_engine(
        TEST_DATABASE_URL,
        connect_args={"check_same_thread": False}  # SQLite特定配置
    )
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # 清理
    Base.metadata.drop_all(bind=engine)
    if os.path.exists("./test.db"):
        os.remove("./test.db")


@pytest.fixture(scope="function")
def test_db_session(test_engine) -> Generator[Session, None, None]:
    """测试数据库会话"""
    TestingSessionLocal = sessionmaker(
        autocommit=False, 
        autoflush=False, 
        bind=test_engine
    )
    
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.rollback()
        session.close()


@pytest.fixture(scope="function")
def test_client(test_db_session, test_settings) -> Generator[TestClient, None, None]:
    """测试客户端"""
    def override_get_db():
        try:
            yield test_db_session
        finally:
            pass
    
    def override_get_redis():
        # 返回Mock Redis客户端用于测试
        mock_redis = Mock(spec=Redis)
        mock_redis.ping.return_value = True
        mock_redis.get.return_value = None
        mock_redis.set.return_value = True
        mock_redis.delete.return_value = 1
        return mock_redis
    
    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_redis] = override_get_redis
    
    with TestClient(app) as client:
        yield client
    
    # 清理依赖覆盖
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def mock_redis():
    """Mock Redis客户端"""
    mock_redis = Mock(spec=Redis)
    mock_redis.ping.return_value = True
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = 1
    mock_redis.exists.return_value = False
    mock_redis.expire.return_value = True
    return mock_redis


@pytest.fixture(scope="function")
def temp_dir():
    """临时目录"""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture(scope="function")
def sample_task_data():
    """示例任务数据"""
    return {
        "task_name": "test_audit_task",
        "config": """
模型:
  相关性模型: qwen2-7b-8k-simple_tasks_v1
  总结模型: Qwen2.5-72B-Instruct-GPTQ-Int4-kd
  温度: 1e-8

global:
  知识库地址: http://test-kbs/query
  知识库图谱: ["test_graph"]
        """.strip(),
        "main_doc": '{"title": "测试文档", "content": "这是一个测试文档"}',
        "callback_url": "http://test-callback/webhook"
    }


@pytest.fixture(scope="function")
def sample_task_record(test_db_session, sample_task_data):
    """示例任务记录"""
    task_record = RequestTracking(
        task_id="test-task-123",
        task_name=sample_task_data["task_name"],
        status=TaskStatus.PENDING.value
    )
    test_db_session.add(task_record)
    test_db_session.commit()
    test_db_session.refresh(task_record)
    return task_record


@pytest.fixture(scope="function")
def mock_celery():
    """Mock Celery应用"""
    with patch('app.tasks.celery_tasks.celery_app') as mock_celery_app:
        mock_task = Mock()
        mock_task.delay.return_value = Mock(id="mock-celery-task-id")
        mock_celery_app.task.return_value = mock_task
        yield mock_celery_app


@pytest.fixture(scope="function")
def mock_http_service():
    """Mock HTTP服务"""
    with patch('app.services.http_service.HTTPService') as mock_http:
        mock_instance = Mock()
        mock_response = Mock()
        mock_response.json.return_value = {
            "data": [{"embedding": [0.1, 0.2, 0.3]}],
            "choices": [{"message": {"content": "Mock LLM response"}}]
        }
        mock_response.status_code = 200
        mock_instance.post.return_value = mock_response
        mock_instance.get.return_value = mock_response
        mock_http.return_value = mock_instance
        yield mock_instance


@pytest.fixture(scope="function")
def mock_external_services(mock_http_service):
    """Mock所有外部服务"""
    with patch('app.utils.embedding_utils.get_embedding') as mock_embedding, \
         patch('app.utils.model_client.get_chat_completion') as mock_llm, \
         patch('app.utils.model_client.query_knowledge_base') as mock_kbs:
        
        # Mock embedding服务
        mock_embedding.return_value = [0.1, 0.2, 0.3]
        
        # Mock LLM服务
        mock_llm.return_value = "Mock LLM response"
        
        # Mock知识库服务
        mock_kbs.return_value = {"result": "Mock KBS response"}
        
        yield {
            "embedding": mock_embedding,
            "llm": mock_llm,
            "kbs": mock_kbs
        }


@pytest.fixture(scope="function")
def mock_file_operations():
    """Mock文件操作"""
    with patch('app.utils.file_utils.ensure_directory') as mock_ensure_dir, \
         patch('builtins.open', create=True) as mock_open, \
         patch('os.path.exists') as mock_exists, \
         patch('os.makedirs') as mock_makedirs:
        
        mock_ensure_dir.return_value = True
        mock_exists.return_value = True
        mock_makedirs.return_value = True
        
        # Mock文件读写
        mock_file = Mock()
        mock_file.read.return_value = "mock file content"
        mock_file.write.return_value = None
        mock_file.__enter__.return_value = mock_file
        mock_file.__exit__.return_value = None
        mock_open.return_value = mock_file
        
        yield {
            "ensure_dir": mock_ensure_dir,
            "open": mock_open,
            "exists": mock_exists,
            "makedirs": mock_makedirs
        }


# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "e2e: 端到端测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
    config.addinivalue_line(
        "markers", "external: 需要外部服务的测试"
    )


# 测试数据清理
@pytest.fixture(autouse=True)
def cleanup_test_data(test_db_session):
    """自动清理测试数据"""
    yield
    # 测试后清理数据
    test_db_session.query(RequestTracking).delete()
    test_db_session.commit()
