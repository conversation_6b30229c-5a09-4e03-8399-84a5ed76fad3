{"request": {"method": "POST", "urlPattern": "/v1/chat/completions"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "这是一个Mock LLM响应。文档审核结果：通过。评分：95分。总结：文档质量优秀，内容完整，符合要求。", "role": "assistant"}}], "created": 1699999999, "id": "chatcmpl-mock-id", "model": "Qwen2.5-72B-Instruct-GPTQ-Int4-kd", "object": "chat.completion", "usage": {"completion_tokens": 50, "prompt_tokens": 100, "total_tokens": 150}}}}