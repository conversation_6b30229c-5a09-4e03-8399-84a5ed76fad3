# 测试体系文档

本文档介绍审计服务项目的完整测试体系，包括测试架构、使用方法和最佳实践。

## 📁 测试目录结构

```
tests/
├── README.md                    # 本文档
├── conftest.py                  # 全局测试配置和fixtures
├── pytest.ini                  # pytest配置文件
├── run_tests.py                 # 测试运行脚本
├── verify_tests.py              # 测试环境验证脚本
├── factories.py                 # 测试数据工厂
├── docker-compose.test.yml      # Docker测试环境
├── mock_data/                   # Mock数据和配置
│   └── mappings/               # WireMock映射文件
├── unit/                       # 单元测试
│   ├── test_models.py          # 数据库模型测试
│   ├── test_schemas.py         # Pydantic模式测试
│   ├── test_services.py        # 服务层测试
│   └── test_utils.py           # 工具函数测试
├── integration/                # 集成测试
│   ├── test_api_endpoints.py   # API端点测试
│   ├── test_database_operations.py  # 数据库操作测试
│   └── test_celery_tasks.py    # Celery任务测试
├── e2e/                        # 端到端测试
│   └── test_audit_workflow.py  # 审核工作流测试
└── performance/                # 性能测试
    └── test_load_testing.py    # 负载测试
```

## 🚀 快速开始

### 1. 安装测试依赖

```bash
# 安装项目依赖（包含测试依赖）
pip install -r requirements.txt

# 或者单独安装测试依赖
pip install pytest pytest-cov pytest-asyncio pytest-xdist
```

### 2. 运行测试

#### 使用测试脚本（推荐）

```bash
# 运行所有测试
python tests/run_tests.py

# 运行特定类型的测试
python tests/run_tests.py unit          # 单元测试
python tests/run_tests.py integration   # 集成测试
python tests/run_tests.py e2e          # 端到端测试
python tests/run_tests.py performance  # 性能测试

# 详细输出
python tests/run_tests.py --verbose

# 并行运行
python tests/run_tests.py --parallel

# 运行特定标记的测试
python tests/run_tests.py -m "unit and not slow"
```

#### 直接使用pytest

```bash
# 运行所有测试
pytest tests/

# 运行特定目录
pytest tests/unit/
pytest tests/integration/

# 运行特定文件
pytest tests/unit/test_models.py

# 运行特定测试
pytest tests/unit/test_models.py::TestRequestTracking::test_create_request_tracking

# 带覆盖率报告
pytest tests/ --cov=app --cov-report=html

# 并行运行
pytest tests/ -n auto
```

### 3. Docker测试环境

```bash
# 启动测试环境
docker-compose -f tests/docker-compose.test.yml up -d

# 运行测试
docker-compose -f tests/docker-compose.test.yml up test_runner

# 清理测试环境
docker-compose -f tests/docker-compose.test.yml down -v
```

## 🧪 测试类型说明

### 单元测试 (Unit Tests)

**目标**：测试单个函数、类或模块的功能
**特点**：快速、独立、无外部依赖
**标记**：`@pytest.mark.unit`

```python
@pytest.mark.unit
def test_create_request_tracking(test_db_session):
    """测试创建请求跟踪记录"""
    task_record = RequestTracking(
        task_id="test-task-001",
        task_name="测试任务",
        status=TaskStatus.PENDING.value
    )
    
    test_db_session.add(task_record)
    test_db_session.commit()
    
    assert task_record.id is not None
```

### 集成测试 (Integration Tests)

**目标**：测试多个组件之间的交互
**特点**：涉及数据库、外部服务等
**标记**：`@pytest.mark.integration`

```python
@pytest.mark.integration
@pytest.mark.api
def test_audit_async_success(test_client, mock_celery):
    """测试异步审核提交成功"""
    response = test_client.post("/api/v1/audit/async", json=audit_request)
    assert response.status_code == 202
```

### 端到端测试 (E2E Tests)

**目标**：测试完整的用户工作流
**特点**：模拟真实用户操作
**标记**：`@pytest.mark.e2e`

```python
@pytest.mark.e2e
def test_complete_async_audit_workflow(test_client):
    """测试完整的异步审核工作流"""
    # 1. 提交任务
    # 2. 查询状态
    # 3. 等待完成
    # 4. 验证结果
```

### 性能测试 (Performance Tests)

**目标**：测试系统性能和负载能力
**特点**：测量响应时间、吞吐量等
**标记**：`@pytest.mark.performance`

```python
@pytest.mark.performance
@pytest.mark.slow
def test_concurrent_requests_performance(test_client):
    """测试并发请求性能"""
    # 并发执行多个请求
    # 测量响应时间和成功率
```

## 🔧 测试配置

### pytest配置 (pytest.ini)

```ini
[tool:pytest]
testpaths = tests
addopts = 
    -v
    --tb=short
    --strict-markers
    --cov=app
    --cov-report=html
    --cov-fail-under=80

markers =
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    slow: 慢速测试
    external: 需要外部服务的测试
```

### 测试标记

- `unit`: 单元测试
- `integration`: 集成测试
- `e2e`: 端到端测试
- `slow`: 慢速测试（运行时间较长）
- `external`: 需要外部服务的测试
- `api`: API接口测试
- `database`: 数据库相关测试
- `celery`: Celery任务测试
- `redis`: Redis相关测试
- `performance`: 性能测试

### 运行特定标记的测试

```bash
# 只运行单元测试
pytest -m unit

# 只运行快速测试（排除慢速测试）
pytest -m "not slow"

# 运行API和数据库测试
pytest -m "api or database"

# 排除需要外部服务的测试
pytest -m "not external"
```

## 🏭 测试工厂和Fixtures

### 测试数据工厂

```python
from tests.factories import TaskDataFactory, TestDataBuilder

# 使用工厂创建测试数据
audit_request = TaskDataFactory.create_audit_request_data()
task_record = TaskDataFactory.create_completed_task_record()

# 使用构建器创建自定义数据
builder = TestDataBuilder()
custom_request = (builder
    .with_task_name("自定义任务")
    .with_status(TaskStatus.PROCESSING)
    .build_audit_request())
```

### 全局Fixtures

```python
# 测试客户端
def test_api_endpoint(test_client):
    response = test_client.get("/api/v1/health")
    assert response.status_code == 200

# 数据库会话
def test_database_operation(test_db_session):
    # 数据库操作
    pass

# Mock外部服务
def test_with_mocks(mock_external_services):
    # 测试代码，外部服务已被mock
    pass
```

## 📊 测试报告

### 覆盖率报告

```bash
# 生成HTML覆盖率报告
pytest --cov=app --cov-report=html

# 查看报告
open htmlcov/index.html
```

### JUnit XML报告

```bash
# 生成JUnit XML报告（用于CI/CD）
pytest --junitxml=test-results.xml
```

### 自定义报告

```bash
# 使用测试脚本生成报告
python tests/run_tests.py --report
```

## 🐳 Docker测试

### 测试环境组件

- **test_mysql**: 测试数据库
- **test_redis**: 测试缓存
- **test_app**: 测试应用
- **test_celery_worker**: 测试Celery工作进程
- **mock_services**: Mock外部服务
- **test_runner**: 测试运行器

### 使用Docker测试

```bash
# 启动完整测试环境
docker-compose -f tests/docker-compose.test.yml up -d

# 运行测试
docker-compose -f tests/docker-compose.test.yml up test_runner

# 查看测试日志
docker-compose -f tests/docker-compose.test.yml logs test_runner

# 清理环境
docker-compose -f tests/docker-compose.test.yml down -v
```

## 🔍 调试测试

### 调试失败的测试

```bash
# 详细输出
pytest tests/unit/test_models.py -v -s

# 进入调试器
pytest tests/unit/test_models.py --pdb

# 只运行失败的测试
pytest --lf

# 停在第一个失败
pytest -x
```

### 查看测试覆盖率

```bash
# 查看未覆盖的代码行
pytest --cov=app --cov-report=term-missing

# 生成详细的HTML报告
pytest --cov=app --cov-report=html
open htmlcov/index.html
```

## 🚨 常见问题

### 1. 测试数据库连接失败

```bash
# 检查测试数据库配置
echo $DATABASE_URL

# 确保测试数据库运行
docker-compose -f tests/docker-compose.test.yml up test_mysql
```

### 2. Mock服务不工作

```bash
# 检查Mock服务状态
curl http://localhost:8080/__admin/mappings

# 重启Mock服务
docker-compose -f tests/docker-compose.test.yml restart mock_services
```

### 3. 测试运行缓慢

```bash
# 使用并行测试
pytest -n auto

# 只运行快速测试
pytest -m "not slow"

# 分析慢速测试
pytest --durations=10
```

## 📝 最佳实践

### 1. 测试命名

- 使用描述性的测试名称
- 遵循 `test_<功能>_<场景>_<期望结果>` 格式
- 使用中文注释说明测试目的

### 2. 测试组织

- 按功能模块组织测试文件
- 使用测试类组织相关测试
- 合理使用pytest标记

### 3. Mock和Fixtures

- 优先使用fixtures而不是全局变量
- 合理使用Mock避免外部依赖
- 保持测试的独立性

### 4. 断言

- 使用具体的断言而不是通用断言
- 提供有意义的断言消息
- 验证所有重要的输出

### 5. 测试数据

- 使用工厂模式创建测试数据
- 避免硬编码测试数据
- 确保测试数据的清理

## 🔄 CI/CD集成

### GitHub Actions示例

```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.11
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run tests
        run: python tests/run_tests.py
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```

### 测试报告集成

- 使用JUnit XML格式的测试报告
- 集成覆盖率报告到CI/CD平台
- 设置测试失败时的通知

## 📚 相关文档

- **[文档中心](../docs/README.md)** - 完整的文档索引
- **[测试体系总结](../docs/testing/TESTING_SYSTEM_SUMMARY.md)** - 测试架构总览
- **[项目主文档](../README.md)** - 项目概述和快速开始
- **[Docker部署指南](../docker/README.md)** - Docker部署文档

---

通过这套完整的测试体系，您可以确保审计服务的质量和可靠性。如有问题，请参考相关测试文件或联系开发团队。
