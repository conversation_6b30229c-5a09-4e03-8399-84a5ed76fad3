

# Gemini 对话导出

## 2025年7月27日

### 用户

This is the Gemini CLI. We are setting up the context for our chat.
  Today's date is 2025年7月27日星期日.
  My operating system is: win32
  I'm currently working in the directory: C:\Users\<USER>\Desktop\gemini对话
  Showing up to 200 items (files + folders).

C:\Users\<USER>\Desktop\gemini对话\
├───【20250415中信建投】基于AI的第二代智能审核系统需求说明书-股&债权.pdf
├───【最新】大模型交流材料-2025.07.pdf
├───图片\
│   ├───1753358304253.jpg
│   └───金证股份_网信算备440305975889301250017号(1).pdf
└───files\
    ├───容诚会计师事务所（特殊普通合伙）关于公司首次公开发行股票并在创业板上市的财务报表及审计报告.pdf
    ├───上海市通力律师事务所关于公司首次公开发行股票并在创业板上市的法律意见书.pdf
    ├───首次公开发行股票并在创业板上市招股说明书.pdf
    ├───兴业证券股份有限公司关于公司首次公开发行股票并在创业板上市的上市保荐书.pdf
    └───PDF文件比对结果.md

### Gemini

Got it. Thanks for the context!

### 用户

@【20250415中信建投】基于AI的第二代智能审核系统需求说明书-股&债权.pdf 这个是客户的需求，当前客户想将其分成两个招标，分别是“文本核查”及“数据校验”。请帮我从这份需求中，梳理出所有和“文本核查”相关的需求点，并汇总整理。

### Gemini

好的，我已经从您提供的需求说明书中梳理出所有与“文本核查”相关的需求点，并汇总整理如下：

### **文本核查相关需求汇总**

**一、 基础文本错误检测**

1.  **错别字与乱码检测**：检查文档中的错字、别字以及因格式问题产生的乱码。
2.  **标点规范性检测**：检查标点符号的缺失、多余、以及不当使用的情况。
3.  **重复冗余检测**：识别并提示重复的段落或词语。
4.  **语法逻辑检测**：检查语句的通顺性，包括主谓搭配不当、成分残缺、语序混乱等问题。
5.  **漏字/漏词检测**：检查非语法问题的词语或句子成分缺失的情况。
6.  **敏感词检测**：
    *   支持通用的敏感词库。
    *   支持用户根据业务需要自定义敏感词库。
    *   能结合字面与语义进行综合判断和过滤。

**二、 格式与结构一致性检测**

1.  **编号一致性检测**：确保文档中各级标题的段落编号、页眉页脚的页码以及表格内的编号是连续且正确的。
2.  **索引检测**：通过上下文语义理解及目录分析，判断正文中的索引标题与目录是否能够准确对应。
3.  **章节引用一致性检测**：核查正文中引用的章节号是否与目录中的章节保持一致。
4.  **表格内容显示检测**：检查因格式问题（非漏字）导致的表格内容显示不全。

**三、 外部信息与合规性检测**

1.  **工商信息一致性检测**：将文档中提及的公司名称、法人、注册资本、股东等信息与国家企业信用信息公示系统的最新公开信息进行比对，确保一致性。
2.  **外部法规引用检测**：
    *   **时效性**：检查文档中引用的法律法规、规章制度等是否为最新有效版本。
    *   **准确性**：确保引用的法规名称准确无误。
3.  **标准化表述检测**：对于监管或法规要求使用标准化表述的内容，支持与标准文本进行一致性比对。

**四、 系统功能与扩展性**

1.  **多文档交叉复核**：支持同时上传多个相关文档（如招股书、审计报告、保荐报告等），系统能自动复核不同文档间涉及相同内容的文段表述是否一致。
2.  **自定义规则支持**：
    *   支持根据业务部门日常积累的、具体的审核规则进行快速配置或开发。
    *   支持根据特定的业务需求进行相关功能的定制化开发。

### 用户

@智遇文档审核平台使用手册.pdf 请阅读并理解我们当前的“智遇文档平台”，并结合刚才对于“文本核查相关需求”，帮我生成解决方案：即要在现有平台上，如何升级新的功能，才能满足需求。请注意：最终的解决方案，要尽可能通过“大模型能力”来实现，具有可扩展性，让用户可以灵活配置。

### Gemini

好的，在深入理解了您现有的“智遇文档(CoDoc)审核平台”功能和架构后，结合我们梳理的“文本核查”需求，我为您设计了一套充分利用大模型能力、具备高度灵活性和可扩展性的升级解决方案。

**核心升级思路：**

当前“智遇文档平台”的核心是**“配置助手”**，它通过“审核规则库”、“文档模板”、“领域知识库”和“自定义审核”四种模式来驱动审核，这已经是一个非常好的基础。

我们的升级思路不是推倒重来，而是在现有框架内，**将核心驱动力从“固定的规则和模板”全面转向“灵活、强大的大模型原生能力”**。我们将引入一个新的、统一的审核任务类型，或对现有任务类型进行深度改造，让用户通过更自然、更灵活的方式，将复杂的文本核查需求转化为大模型可以理解并执行的任务。

---

### **文本核查解决方案：智遇文档平台升级方案**

我们建议在“配置助手”中，新增或整合出一个核心功能模块：**“智能文本核查任务”**。用户在创建一个“审核助手”时，可以添加这种类型的任务。该任务将内置多种基于大模型的核查能力，用户可以像搭积木一样按需选用和配置。

#### **第一部分：功能模块升级**

**1. 新增“基础文本质量”原子能力**

*   **现状**：当前平台似乎缺少对基础文本错误的通用性检查。
*   **升级方案**：在“智能文本核查任务”中，增加一个**“基础文本质量”**的开关式选项。用户只需勾选，即可启用大模型对以下问题的全面检查，无需配置复杂规则：
    *   `错别字与乱码`
    *   `标点规范性`
    *   `重复冗余（词语、段落）`
    *   `语法逻辑（搭配、成分、语序）`
    *   `漏字/漏词`
*   **实现方式**：后端调用大模型时，使用一个高度优化的通用Prompt，指令模型对文本进行通识性的润色和校对，并以结构化方式返回错误列表。这充分利用了大模型原生的语言能力。

**2. 升级“格式与结构”核查能力为“智能结构校验”**

*   **现状**：“基于审核规则库”可以实现部分结构校验，但不够灵活，难以应对复杂的文档结构。
*   **升级方案**：在“智能文本核查任务”中，提供**“智能结构校验”**选项。用户可以配置需要校验的结构类型，例如：
    *   **编号一致性**：模型自动识别并检查全文的章节号、图表编号、表格编号是否连续、正确。
    *   **索引与目录一致性**：模型自动比对目录与正文标题，报告不匹配或缺失的条目。
    *   **章节引用正确性**：模型能理解“详见第X章”之类的表述，并检查引用的章节是否存在且相关。
*   **实现方式**：这需要大模型具备**文档布局与结构理解能力**。通过向大模型提供整个文档的结构化信息（或全文），并使用特定的Prompt（例如：“请分析本文档的目录与正文，校验所有章节标题是否一致并列出差异”），让模型完成智能校验。

**3. 引入“外部信息与合规”核查的“知识增强校验”**

*   **现状**：“基于领域知识库”是很好的基础，但缺少从外部实时获取信息和进行复杂合规比对的能力。
*   **升级方案**：在“智能文本核查任务”中，增加**“知识增强校验”**模块，并赋予大模型调用外部工具的能力（Function Calling / Agents）：
    *   **实时工商信息核验**：配置此项后，模型可以自动提取文中的公司名称、法人、注册资本等信息，通过调用**外部API（如天眼查、企查查或国家企业信用信息公示系统）**进行实时比对，并报告不一致之处。
    *   **法律法规时效性与准确性检测**：用户可维护一个“法律法规知识库”。模型在校验时，不仅会在库内检索，还能通过**Web搜索**等工具，检查引用的法规是否为最新版本、名称是否准确。
    *   **自定义合规知识库校验**：用户可以上传内部的合规手册、标准化表述文件等作为“领域知识库”。模型将依据这些内部知识，对文档内容进行一致性检查。
*   **实现方式**：这是本次升级的核心。通过为大模型配备`web_search`、`api_call`等工具，使其从一个纯粹的文本处理器，升级为能够与外部世界交互的**“智能审核代理（Agent）”**。

#### **第二部分：用户体验与配置流程升级**

**目标**：让不懂技术的业务专家，也能通过自然语言和简单配置，灵活地创建出强大的文本核查助手。

**1. 统一的“审核任务”配置中心**
   在“配置助手”页面，当用户添加一个“智能文本核查任务”时，UI会呈现一个清晰的配置界面：

   *   **任务名称**：例如，“招股书文本质量与合规性初审”。
   *   **核查能力选择**：以勾选列表的形式提供所有可用的原子能力：
        *   [√] 基础文本质量
        *   [√] 智能结构校验
        *   [  ] 知识增强校验
   *   **详细配置**：用户勾选某个能力后，会展开相应的配置项。
        *   **基础文本质量**：可进一步选择检查项（如：仅检查错别字和标点）。
        *   **智能结构校验**：可配置要检查的编号类型（章节、图表等）。
        *   **知识增强校验**：
            *   选择要启用的功能（如：工商信息核验）。
            *   **字段映射**：引导用户将文档中的内容与核查功能进行关联。例如，配置“将文档中出现的‘发行人全称’字段，用于工商信息核验中的‘公司名称’查询”。
            *   **Prompt微调接口**：为高级用户提供一个简单的文本框，让他们可以用自然语言微调模型的行为。例如：“进行工商信息核验时，如果发现股东不一致，请标记为高风险问题”。

**2. 灵活的“自定义规则”与“大模型”结合**
   对于需求中提到的“支持根据业务方日常积累的审核规则进行敏捷配置”，可以这样实现：

   *   在“规则库”中，除了现有的结构化规则，增加一种**“自然语言规则”**。
   *   用户可以直接输入一条审核要求，例如：“检查文档中是否明确说明了董事会会议的通知方式和时间，并且通知时间需早于会议召开日至少5天”。
   *   在“智能文本核查任务”中，用户可以直接引用这些“自然语言规则”。系统会将这些规则作为核心指令，融入到给大模型的最终Prompt中，从而实现敏捷、灵活的规则配置。

#### **最终效果**

通过以上升级，您的“智遇文档平台”将：

1.  **更智能**：从依赖固定规则，转变为依赖大模型的深度理解、推理和工具调用能力，能处理更复杂、更模糊的审核任务。
2.  **更灵活**：用户无需编写代码，通过勾选和自然语言描述，即可自由组合各种核查能力，快速响应业务变化。
3.  **可扩展性更强**：未来若有新的核查需求（如财务数据核查、图片内容核查），只需开发相应的“原子能力”模块，即可无缝集成到“智能文本核查任务”中，供用户选用。
4.  **用户体验更佳**：将强大的AI能力封装在简洁的UI背后，让业务人员能够真正成为AI的主人，轻松驾驭大模型来完成日常的文档审核工作。

### 用户

方案一定要具有可行性。大模型每次请求有token限制，因此我们无法把500页的招股说明书，完整写到提示词中给到大模型。另外对于页码、序号这些信息，也需要经过前置的对文档进行版面分析后，才能拿到这些特征，然后通过提示词把这些特征给到大模型，让大模型进行判断。另外，对于上下文一致性，应该要如何设计方案实现，也请仔细认真思考后，一并给出。

### Gemini

您提出了非常深刻且关键的三个问题，这正是在实际工程中将大模型落地应用必须解决的核心挑战。我之前的方案偏向于功能设想，现在我将结合您的顾虑，为您设计一个更具工程可行性的、分层级的技术解决方案。

这个方案的核心思想是：**我们不将大模型视为一个无所不能的“黑箱”，而是将其作为一个强大的“推理引擎”嵌入到一个精密的、自动化的处理流水线（Pipeline）中。**

---

### **文本核查可行性解决方案（分层架构）**

我们将整个核查流程分解为三个独立的、依次执行的阶段：**文档解析层**、**原子任务处理层**、和**全局一致性校验层**。

#### **第一阶段：文档解析与结构化（解决版面分析和特征提取问题）**

这一阶段的目标是将非结构化的文档（PDF、Word）转换为机器可以完全理解的结构化数据。我们称之为构建 **“数字文档孪生体（Digital Document Twin）”**。

1.  **文档解析**：
    *   使用如 `PyMuPDF`、`python-docx` 等库，提取文档的原始文本内容。
    *   对于PDF，特别是扫描件，必须引入**OCR（光学字符识别）**，并结合**版面分析（Layout Analysis）**模型。这能识别出文本块、标题、表格、图片、列表等元素。

2.  **结构化与特征标注**：
    *   解析的输出不是一个巨大的文本字符串，而是一个**结构化的JSON对象**。这个JSON将文档表示为一个树状结构，每个节点都是一个内容块（Block）。
    *   **内容块（Block）的属性应至少包含**：
        *   `block_id`: 唯一标识符。
        *   `page_number`: 所在的页码。
        *   `text`: 该块的文本内容。
        *   `block_type`: 块类型（例如：`paragraph`, `header_1`, `header_2`, `table`, `list_item`, `image_caption`）。
        *   `coordinates`: 在页面上的坐标位置（用于定位和高亮）。
        *   `metadata`:
            *   `font_size`, `font_weight`: 字体信息，有助于判断标题级别。
            *   `sequence_info`: 如果是列表或标题，其**序号**（如 "2.2.1"）应被显式提取出来。

**产出**：一个完整的、结构化的文档JSON。**这个JSON就是我们后续所有操作的基础，它已经解决了您提出的“需要前置版面分析拿到特征”的问题。**

#### **第二阶段：原子任务处理（解决Token限制问题）**

有了结构化的文档，我们就可以避免一次性将500页内容塞给大模型。我们将采用**“分块处理、逐一击破”**的策略。

1.  **任务分解与分块（Chunking）**：
    *   系统不按固定字数切分文本，而是根据第一阶段生成的**文档结构**进行**语义分块**。例如，一个“原子任务”的处理单元可以是一个**自然段**、一个**完整的表格**、或一个**三级标题及其下的所有段落**。
    *   这种分块方式能最大程度地保留上下文的完整性，远优于粗暴的文本切割。

2.  **无上下文依赖任务处理**：
    *   对于像**“基础文本质量检查”（错别字、病句、标点）**这类不依赖全局信息的任务，可以并行处理。
    *   **工作流程**：
        1.  从任务队列中取出一个内容块（Chunk）。
        2.  构建一个高度优化的Prompt，例如：`“你是一个专业的文档校对员，请检查以下文本块中的语法、拼写和标点错误，并以JSON格式返回错误列表，包含原文、修正建议和错误类型。文本块内容：{chunk.text}”`
        3.  将请求发送给大模型。
        4.  收集返回的错误信息，并关联回`block_id`和`page_number`。

3.  **局部上下文依赖任务处理**：
    *   对于**“编号一致性”**这类任务，虽然需要上下文，但通常是局部的。
    *   **工作流程**：
        1.  系统遍历第一阶段生成的JSON，提取出所有`block_type`为`header`的块及其`sequence_info`（序号）。
        2.  将这个序号列表（例如 `['1', '1.1', '1.2', '3', '3.1']`）提供给大模型。
        3.  构建Prompt：`“这是一个文档的标题序号列表，请检查其中是否存在跳号、错号或乱序的问题。列表：{sequence_list}”`
        4.  大模型可以轻易地发现 "1.2" 后面直接跳到了 "3"，从而报告错误。

**产出**：针对每个内容块的、独立的、无全局上下文依赖的审核结果。

#### **第三阶段：全局一致性校验（解决跨上下文一致性问题）**

这是最复杂的部分，需要一个**“多遍处理（Multi-Pass）”**和**“状态记忆”**的机制。我们将引入一个核心组件：**“一致性事实库（Consistency Fact Store）”**。

1.  **第一遍：事实提取（Fact Extraction Pass）**
    *   **目标**：遍历整个文档，提取出所有需要进行一致性校验的关键信息点（我们称之为“事实”）。
    *   **用户配置**：用户需要预先定义他们关心哪些“事实”的一致性。例如，通过自然语言规则配置：“我需要校验全文所有‘注册资本’的金额保持一致”、“我需要校验‘法人代表’的姓名在全文中是同一个人”。
    *   **工作流程**：
        1.  系统再次**分块**遍历文档。
        2.  对每个块，使用一个专门用于信息提取的Prompt调用大模型。例如：`“根据以下规则：['注册资本', '法人代表']，从下面的文本中提取对应的信息，以JSON格式返回{'key': '...', 'value': '...'}}。文本：{chunk.text}”`
        3.  将提取出的事实，连同其来源（`page_number`, `block_id`），存入一个临时的、在本次审核任务中持续存在的**“事实库”**中。
        *   **事实库结构示例**：
            ```json
            {
              "注册资本": [
                { "value": "5,000万元人民币", "page": 15, "block_id": "b-102" },
                { "value": "伍仟万元", "page": 88, "block_id": "b-821" }
              ],
              "法人代表": [
                { "value": "张三", "page": 16, "block_id": "b-115" },
                { "value": "张三", "page": 120, "block_id": "b-1055" }
              ]
            }
            ```

2.  **第二遍：事实比对（Fact Comparison Pass）**
    *   **目标**：分析“事实库”，找出不一致的地方。
    *   **工作流程**：
        1.  遍历事实库中的每一个key（如“注册资本”）。
        2.  检查其value列表中的所有条目。如果列表中的值经过**归一化处理**后（例如，将“伍仟万元”转换为“5000万元”）不完全相同，则判定为不一致。
        3.  **归一化处理**本身也可以交给大模型完成。例如，可以对每个值调用大模型：`“请将以下金额统一转换为阿拉伯数字的元为单位：'{value}'”`。
        4.  记录所有不一致的“事实”，并附上它们各自的来源（页码和块ID），形成最终的一致性审核报告。

**最终产出**：一份完整的审核报告，它整合了第二阶段的原子任务结果和第三阶段的全局一致性校验结果，每个问题点都精确地定位到原文的具体位置。

### **总结**

这个三层架构的解决方案：
*   **具有可行性**：通过**文档结构化**和**分块处理**，完美规避了LLM的Token限制。
*   **考虑了前置处理**：**文档解析层**专门负责版面分析和特征提取，为后续处理提供了坚实基础。
*   **解决了上下文一致性**：通过创新的**“一致性事实库”**和**“两遍处理”**机制，实现了对整个文档的全局信息校验，突破了单次请求的上下文局限。
*   **模块化和可扩展**：每一层、每一个处理步骤都可以独立优化或替换。未来可以轻松加入更多类型的校验任务（如图片内容校验、财务逻辑校验等），只需在相应的处理层中增加新的处理单元即可。

### 用户

你给的建议已经有了明显进步。现在进一步思考两个问题：1.我们的文档审核任务，除了要给出校验未通过的结论，还需要进行原文溯源。因此对于有上下文依赖的情形，需要思考下原文溯源方案。 2.用户并不想要预先定义他们关心的“事实库”，他们更希望我们可以直接预置在系统里面。请注意，用户很“懒”，只想简单便捷的使用我们的工具。 请结合这两点，继续完善方案。你需要仔细认真思考后再给我回复。

### Gemini

您好，您再次提出的这两个问题精准地切中了将AI方案产品化的关键核心：**用户体验的闭环**和**开箱即用的便捷性**。一个技术上可行的方案，如果不能让用户轻松溯源、简单使用，那么它在商业上就是失败的。

我已对原方案进行了深入的重构，将这两点作为核心设计原则，提出以下更完善、更具操作性的解决方案。

---

### **完善后的文本核查解决方案**

我们将继续沿用**“文档解析 -> 原子任务 -> 全局校验”**的三层架构，但对第三层（全局校验）进行重大升级，并重新设计了用户与系统的交互模式。

#### **核心升级一：针对上下文依赖的“关联性原文溯源”方案**

**问题：** 当系统报告“第15页的‘注册资本’与第88页不一致”时，用户需要能立刻、并排看到这两处的原文上下文，而不是一个孤立的结论。

**解决方案：** 我们引入**“矛盾证据组（Contradiction Group）”**的概念来解决这个问题。

1.  **改造“事实库”为“证据库”：**
    在全局校验的“事实提取”阶段，我们不再仅仅提取一个干巴巴的`value`。大模型在提取信息时，必须同时捕获其**“证据上下文”**。

    *   **新的提取指令（Prompt）**：`“你是一名专业的金融分析师。请从以下文本中，根据规则:['注册资本', '法人代表']提取关键信息。对于每一个找到的信息，请以JSON格式返回其：1. 提取值(value)；2. 标准化值(normalized_value)，如将'伍仟万'转为'50000000'；3. 包含该信息的完整句子(exact_quote)。”`

    *   **“证据库”结构**：事实库升级为证据库，存储的是“证据”对象，而不再是简单的键值对。
        ```json
        {
          "注册资本": [
            {
              "value": "5,000万元人民币",
              "normalized_value": "50000000", // <-- 关键！
              "exact_quote": "本公司经批准设立，注册资本为5,000万元人民币。",
              "page": 15,
              "block_id": "b-102"
            },
            {
              "value": "伍仟万元",
              "normalized_value": "50000000", // <-- 关键！
              "exact_quote": "在另一章节中，我们再次确认公司的注册资本为伍仟万元。",
              "page": 88,
              "block_id": "b-821"
            },
            {
              "value": "肆仟万元",
              "normalized_value": "40000000", // <-- 关键！
              "exact_quote": "根据最新的工商变更，发行人注册资本为肆仟万元。",
              "page": 150,
              "block_id": "b-1530"
            }
          ]
        }
        ```

2.  **生成“矛盾证据组”：**
    在“事实比对”阶段，当系统通过对比`normalized_value`发现不一致时（5000万 vs 4000万），它不会只生成一个简单的错误。它会创建一个**“矛盾证据组”**。

    *   **“矛盾证据组”结构示例**：
        ```json
        {
          "error_type": "全局一致性错误",
          "fact_key": "注册资本",
          "message": "文档中关于“注册资本”的表述存在不一致。",
          "evidence_group": [
            // 证据A
            { "value": "5,000万元人民币", "quote": "...", "page": 15, "block_id": "..." },
            // 证据B
            { "value": "肆仟万元", "quote": "...", "page": 150, "block_id": "..." }
          ]
        }
        ```
    *   注意：所有值为“5000万”的证据被视为一致，不会放入这个矛盾组。

3.  **UI/UX呈现与交互：**
    *   在最终的审核报告界面，用户看到的不再是“A和B不一致”，而是**一个可展开的错误项：“注册资本表述不一致”**。
    *   当用户展开这个错误项时，界面会**并排或分栏显示**该“矛盾证据组”中的所有证据卡片。每个卡片都清晰地展示：
        *   **页码：** Page 15
        *   **冲突内容：** 5,000万元人民币
        *   **原文快照：** “本公司经批准设立，注册资本为5,000万元人民币。”
    *   **点击溯源：** 用户点击任何一个证据卡片，文档视图会**立即跳转**到对应的页面，并将`exact_quote`所在的句子**高亮显示**。

**效果：** 这个方案实现了真正的**“关联溯源”**。用户可以一目了然地看到所有相互矛盾的原文片段，并能一键跳转，极大地提升了审核效率和体验。

#### **核心升级二：为“懒人”设计的“预置通用事实库”**

**问题：** 用户不想学习和配置需要校验的“事实”列表。他们期望系统“天生就懂”。

**解决方案：** 我们将“事实库”的定义工作从用户端转移到系统端，提供一个**“开箱即用”的默认配置**，同时保留扩展性。

1.  **内置“通用核心事实库”：**
    系统将预置一个经过行业专家和AI训练师共同梳理的**“通用核心事实库”**。这个库包含了对于一份标准的招股说明书或类似财经法律文件中，最常见、最重要、最容易出错的一致性校验点。

    *   **事实库分类与示例：**
        *   **公司基本信息：**
            *   `公司全称`、`公司简称`、`英文名称`
            *   `法人代表`
            *   `注册资本`、`实收资本`
            *   `成立日期`、`注册地址`
        *   **本次发行信息：**
            *   `发行股票类型`、`发行股数`
            *   `每股面值`、`发行价格`
            *   `保荐机构全称`、`主承销商`
        *   **关键财务数据与指标：**
            *   `报告期`（如“2022年至2024年”）
            *   `总资产`、`净利润`、`营业收入`（系统会尝试校验在文本中提及的数字与表格中的是否一致）
        *   **核心人员与股东：**
            *   `控股股东名称`、`实际控制人姓名`
            *   `核心技术人员姓名`

2.  **自动化应用与“零配置”体验：**
    *   当用户上传文档，选择“文本核查”任务时，系统**默认启用**这个“通用核心事实库”进行全局一致性校验。
    *   用户**无需任何配置**，即可自动获得对几十个关键信息点的一致性检查。这完全满足了“懒人用户”的需求。

3.  **保留灵活的扩展性（可选）：**
    虽然默认是零配置，但我们依然为高级用户或特殊需求提供扩展能力。
    *   **项目级自定义事实：** 在“配置助手”的高级选项中，用户可以为**当前项目**临时添加几个自定义的校验事实。例如，如果这是一个医药公司的招股书，用户可以临时添加一个事实：“核心在研药品A期临床试验状态”。
    *   **租户级扩展库：** 对于企业级客户，可以允许他们维护自己的“扩展事实库”，这个库会自动与“通用核心事实库”合并，共同生效。

**效果：** 这个方案实现了**“便捷性”与“灵活性”的统一**。绝大多数用户可以享受零配置带来的便捷，而有特殊需求的客户则可以通过可选的高级功能实现定制化，而不会让基础操作变得复杂。

### **总结后的工作流程**

1.  **用户操作：** 上传文档 -> 选择“文本核查”助手 -> 点击“开始审核”。
2.  **系统后台：**
    *   **第一阶段（解析）：** 将文档转化为结构化的“数字文档孪生体”。
    *   **第二阶段（原子任务）：** 并行处理各文本块的“基础文本质量”等无上下文依赖任务。
    *   **第三阶段（全局校验）：**
        *   **自动加载**“通用核心事实库”。
        *   **第一遍（提取）：** 遍历文档，提取所有库中定义的事实，并存入包含完整上下文的“证据库”。
        *   **第二遍（比对）：** 分析“证据库”，生成包含所有相关原文的“矛盾证据组”。
3.  **结果呈现：** UI界面展示一份整合的报告，用户可对任何错误（特别是上下文一致性错误）进行**点击、展开、并排比对、一键跳转高亮原文**的闭环操作。

### 用户

很好，你的回答又有了明显进步。还有2个关键问题：1.“证据库”中的“矛盾证据组”查找，是用向量匹配，还是走大模型？如果是向量匹配（比如embedding模型，准确率会低），如果是大模型速度是否会很慢。 2.“事实库”的定义工作从用户端转移到系统端，那么“系统端”要如何预置全面？你刚才只是列举了几个分类和示例，但并未穷举。已财务数据为例，就有上百个财务指标；并且除了标准事实库外，也还有很多招股书内容，并非通用的，而是每家公司独有的指标。 面对这些问题，又该怎样处理？请仔细思考、逻辑自洽后，再回复我。

### Gemini

您好，您提出的这两个问题是真正将AI从“演示”推向“生产”的“最后一公里”，它们分别触及了AI系统的**性能/成本效益**和**知识库的完备性/可维护性**两大核心。这表明我们必须放弃单一依赖大模型的“暴力美学”，转向更加精巧和务实的工程化设计。

我将针对这两个问题，提供一个逻辑自洽且深度优化的解决方案。

---

### **解决方案一：高效精准的“矛盾证据组”查找机制**

**核心思想：** 我们不使用向量匹配（不够准），也不滥用大模型（太慢）。我们采用一种**“大模型负责理解，确定性算法负责比对”**的混合策略。

这个过程分为两步：**1. 智能提取与归一化** 和 **2. 高速分组与识别**。

**第一步：智能提取与归一化（The LLM's Job）**

这是整个流程中**唯一需要大规模使用大模型进行推理的地方**，也是最能发挥其价值的地方。在“事实提取”阶段，我们对Prompt提出更高要求：

*   **指令（Prompt）升级：** `“你是一个精通财报和法律文书的分析师。请从以下文本中，根据规则:['注册资本']，提取关键信息。以JSON格式返回：1. 提取值(value)；2. 标准化值(normalized_value)，此项至关重要，请将所有同义表述转为唯一的、机器可读的格式（例如，金额统一转为不带逗号的阿拉伯数字，日期转为'YYYY-MM-DD'格式）；3. 包含该信息的完整句子(exact_quote)。”`

*   **示例：**
    *   输入文本：“公司注册资本为伍仟万元整。”
    *   LLM输出的证据对象：
        ```json
        {
          "value": "伍仟万元整",
          "normalized_value": "50000000", // <-- 关键！
          "exact_quote": "公司注册资本为伍仟万元整。",
          "page": 88,
          "block_id": "b-821"
        }
        ```
    *   输入文本：“...注册资本变更为5,000万元...”
    *   LLM输出的证据对象：
        ```json
        {
          "value": "5,000万元",
          "normalized_value": "50000000", // <-- 关键！
          "exact_quote": "经股东会决议，公司注册资本变更为5,000万元。",
          "page": 15,
          "block_id": "b-102"
        }
        ```

**第二步：高速分组与识别（The Algorithm's Job）**

在所有事实都经过大模型“清洗”并转化为包含`normalized_value`的结构化证据后，**我们不再需要任何向量或大模型来进行比对**。接下来的工作由高效的、确定性的后端算法完成：

1.  **构建哈希映射（Hash Map）：** 系统在内存中创建一个嵌套的哈希映射（或字典），这个操作速度极快。
    *   **数据结构：** `Map<FactKey, Map<NormalizedValue, List<Evidence>>>`
    *   **构建过程：** 遍历所有提取出的证据。
        *   对于第一个证据（注册资本，50000000），Map变为：`{"注册资本": {"50000000": [evidence_obj_1]}}`
        *   对于第二个证据（注册资本，50000000），Map更新为：`{"注册资本": {"50000000": [evidence_obj_1, evidence_obj_2]}}`
        *   假设来了第三个证据（注册资本，40000000），Map更新为：`{"注册资本": {"50000000": [...], "40000000": [evidence_obj_3]}}`

2.  **识别矛盾：** 遍历构建好的哈希映射。如果对于任何一个`FactKey`，其内部的Map尺寸（key的数量）**大于1**，那么就存在矛盾。
    *   在上面的例子中，`"注册资本"`这个key对应的内部Map有两个key（`"50000000"` 和 `"40000000"`），因此系统立刻识别出这是一个矛盾。
    *   **生成“矛盾证据组”：** 系统将这个内部Map中所有`List<Evidence>`合并，就形成了我们在上一版方案中定义的“矛盾证据组”。

**结论：** 这个方案完美地结合了AI和传统算法的优势。
*   **准确性：** 由大模型负责最困难的自然语言理解和标准化，保证了输入的质量。
*   **性能与成本：** 由算法负责海量的、机械性的比对工作，速度快、成本低、100%确定。我们避免了N*N的LLM调用，将成本从指数级降低到了线性级。

---

### **解决方案二：应对“事实库”的完备性与独特性挑战**

**核心思想：** 我们采用**“通用基础库 + 动态发现引擎”**的双重策略来解决。用户无需配置，系统自动完成事实的定义和发现。

**第一部分：构建“通用事实库”（处理标准问题）**

您说得对，简单列举是不够的。我们需要一个系统性的方法来构建这个库。

1.  **来源：**
    *   **XBRL分类标准：** 这是最权威、最全面的来源。XBRL（可扩展商业报告语言）的分类法（Taxonomy）为财务报告中的每一个项目（如“营业收入”、“净利润”、“资产负债率”）都提供了唯一的、机器可读的标识。我们可以将中国会计准则（CAS）对应的XBRL分类法直接导入，作为财务事实库的基础。
    *   **法律法规与监管问询：** 分析证监会、交易所的上市规则、信息披露准则以及过往的问询函，可以提取出监管最关注的非财务类事实点（如“董监高持股比例”、“关联交易金额”等）。
    *   **行业专家知识：** 与保荐人、律师、会计师合作，将他们的经验和Checklist转化为结构化的事实定义。

2.  **结构：** 库中的每一项事实，不仅仅是一个名字，而是一个对象：
    ```json
    {
      "fact_key": "NetProfit",
      "display_name": "净利润",
      "aliases": ["归属于母公司所有者的净利润", "净利润额", "net profit"],
      "normalization_type": "currency", // 指导LLM如何进行标准化
      "description": "衡量公司盈利能力的核心指标之一。"
    }
    ```

**第二部分：创建“动态发现引擎”（处理独特性问题）**

这是解决“每家公司独有指标”问题的关键。系统在处理任何一篇文档时，都会在后台启动一个“事实发现”的预处理流程。

**工作流程：**

1.  **第一遍扫描：候选事实识别**
    *   系统分块读取文档，并使用一个专门的LLM Prompt：`“分析以下文本。请识别并提取其中可能构成关键绩效指标(KPI)、核心业务数据、或重要量化承诺的短语。只提取那些看起来是公司特有且可能在后文重复提及的名词和数值。例如‘A产品线毛利率’、‘主要供应商B采购占比’。以列表形式返回你发现的候选事实名称。”`
    *   这一步会产生大量候选事实，例如 `["主要客户销售额", "研发投入资本化率", "XX技术专利数", "员工平均薪酬"]`。

2.  **第二遍扫描：频率与显著性分析**
    *   系统带着第一遍生成的“候选事实列表”，再次遍历整个文档。
    *   这一次，它会统计每个候选事实在全文中**出现的频率**。
    *   **设定阈值：** 系统会有一个内置规则，例如：“任何被独立提及超过3次（可配置）的候选事实，将被自动提升为本次文档审核的‘动态事实’”。

3.  **动态加入事实库：**
    *   所有通过了频率检验的“动态事实”（如“A产品线毛利率”）会被自动加入到本次审核任务的“证据库”提取规则中，与“通用事实库”的规则一同生效。
    *   这意味着，即使系统从未见过“A产品线毛利率”这个指标，但因为它在这份文档中多次出现，系统便“智能地”判断出这是一个需要进行一致性校验的关键点，并自动开始追踪它在全文中的数值是否一致。

**最终效果：**
*   **开箱即用：** 用户拿到手时，系统已经内置了数百个基于行业标准的“通用事实”，覆盖了80%的常见需求。
*   **自适应学习：** 对于剩下的20%公司特有的、非标准的事实，**“动态发现引擎”** 能够自动识别并纳入监控，实现了对未知信息的自适应处理。
*   **用户体验：** 整个过程对用户完全透明。用户感觉到的就是，无论通用的还是专属的指标，系统似乎“都懂”，都能自动帮他检查一致性。
