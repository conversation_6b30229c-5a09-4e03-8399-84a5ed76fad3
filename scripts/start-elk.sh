#!/bin/bash

# Start ELK stack for audit service
# This script starts the ELK stack along with the audit service

set -e

echo "Starting ELK Stack for Audit Service..."

# Create necessary directories
mkdir -p logs
mkdir -p elk/elasticsearch/data
mkdir -p elk/logstash/data
mkdir -p elk/kibana/data

# Set proper permissions
chmod -R 755 elk/
chmod -R 777 logs/

# Create network if it doesn't exist
docker network create audit_network 2>/dev/null || true

# Start ELK services first
echo "Starting Elasticsearch..."
docker-compose -f docker-compose.elk.yml up -d elasticsearch

echo "Waiting for Elasticsearch to be ready..."
until curl -s http://localhost:9200/_cluster/health | grep -q '"status":"green\|yellow"'; do
    echo "Waiting for Elasticsearch..."
    sleep 10
done
echo "Elasticsearch is ready!"

echo "Starting Logstash..."
docker-compose -f docker-compose.elk.yml up -d logstash

echo "Waiting for Logstash to be ready..."
until curl -s http://localhost:9600 | grep -q "ok"; do
    echo "Waiting for Logstash..."
    sleep 10
done
echo "Logstash is ready!"

echo "Starting Kibana..."
docker-compose -f docker-compose.elk.yml up -d kibana

echo "Waiting for Kibana to be ready..."
until curl -s http://localhost:5601/api/status | grep -q "available"; do
    echo "Waiting for Kibana..."
    sleep 10
done
echo "Kibana is ready!"

echo "Starting Filebeat and Metricbeat..."
docker-compose -f docker-compose.elk.yml up -d filebeat metricbeat

# Start the main application services
echo "Starting Audit Service..."
docker-compose up -d

echo ""
echo "🎉 ELK Stack and Audit Service started successfully!"
echo ""
echo "Services available at:"
echo "  - Audit Service API: http://localhost:8000"
echo "  - Elasticsearch: http://localhost:9200"
echo "  - Kibana: http://localhost:5601"
echo "  - Logstash: http://localhost:9600"
echo "  - Flower (Celery): http://localhost:5555"
echo ""
echo "To view logs in Kibana:"
echo "  1. Open http://localhost:5601"
echo "  2. Go to 'Discover' in the left menu"
echo "  3. Create an index pattern: 'audit-service-logs-*'"
echo "  4. Set '@timestamp' as the time field"
echo ""
echo "To stop all services: ./scripts/stop-elk.sh"
