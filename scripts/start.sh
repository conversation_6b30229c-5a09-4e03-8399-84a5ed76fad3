#!/bin/bash

# Start script for the audit service

set -e

echo "Starting Audit Service..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Creating .env file from template..."
    cp .env.example .env
    echo "Please edit .env file with your configuration before running again."
    exit 1
fi

# Create necessary directories
mkdir -p uploads temp logs

# Start services with docker-compose
echo "Starting services with Docker Compose..."
docker-compose up -d

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 10

# Check health
echo "Checking service health..."
curl -f http://localhost:8000/api/v1/health || {
    echo "Health check failed. Checking logs..."
    docker-compose logs api
    exit 1
}

echo "✅ Audit Service started successfully!"
echo ""
echo "Services available at:"
echo "  - API: http://localhost:8000"
echo "  - API Docs: http://localhost:8000/docs"
echo "  - Flower (Celery monitoring): http://localhost:5555"
echo "  - MySQL: localhost:3306"
echo "  - Redis: localhost:6379"
echo ""
echo "To view logs: docker-compose logs -f [service_name]"
echo "To stop services: docker-compose down"
