#!/bin/bash

# 审计服务日志管理脚本
# 提供日志清理、压缩、分析等功能

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$PROJECT_ROOT/logs"
BACKUP_DIR="$PROJECT_ROOT/logs/backup"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
审计服务日志管理脚本

用法: $0 [命令] [选项]

命令:
    clean           清理过期日志文件
    compress        压缩日志文件
    analyze         分析日志统计信息
    backup          备份日志文件
    rotate          手动执行日志轮转
    monitor         监控日志文件大小
    tail            实时查看日志
    search          搜索日志内容

选项:
    -d, --days      保留天数（默认30天）
    -s, --size      文件大小阈值（默认100MB）
    -t, --type      日志类型（app|celery|error|access|all）
    -f, --file      指定日志文件
    -q, --query     搜索查询字符串
    -n, --lines     显示行数（默认100）
    -h, --help      显示此帮助信息

示例:
    $0 clean -d 7                    # 清理7天前的日志
    $0 compress -t app               # 压缩应用日志
    $0 analyze                       # 分析所有日志
    $0 tail -f app.log              # 实时查看应用日志
    $0 search -q "ERROR" -t error   # 搜索错误日志中的ERROR

EOF
}

# 默认参数
COMMAND=""
DAYS=30
SIZE="100M"
LOG_TYPE="all"
LOG_FILE=""
QUERY=""
LINES=100

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        clean|compress|analyze|backup|rotate|monitor|tail|search)
            COMMAND="$1"
            shift
            ;;
        -d|--days)
            DAYS="$2"
            shift 2
            ;;
        -s|--size)
            SIZE="$2"
            shift 2
            ;;
        -t|--type)
            LOG_TYPE="$2"
            shift 2
            ;;
        -f|--file)
            LOG_FILE="$2"
            shift 2
            ;;
        -q|--query)
            QUERY="$2"
            shift 2
            ;;
        -n|--lines)
            LINES="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查命令
if [[ -z "$COMMAND" ]]; then
    log_error "请指定命令"
    show_help
    exit 1
fi

# 确保日志目录存在
mkdir -p "$LOG_DIR" "$BACKUP_DIR"

# 获取日志文件列表
get_log_files() {
    local type="$1"
    case $type in
        app)
            find "$LOG_DIR" -name "app*.log" -type f
            ;;
        celery)
            find "$LOG_DIR" -name "celery*.log" -type f
            ;;
        error)
            find "$LOG_DIR" -name "error*.log" -type f
            ;;
        access)
            find "$LOG_DIR" -name "access*.log" -type f
            ;;
        all)
            find "$LOG_DIR" -name "*.log" -type f
            ;;
        *)
            log_error "未知的日志类型: $type"
            exit 1
            ;;
    esac
}

# 清理过期日志
clean_logs() {
    log_info "开始清理 $DAYS 天前的日志文件..."
    
    local cleaned=0
    local total_size=0
    
    while IFS= read -r -d '' file; do
        local size=$(stat -f%z "$file" 2>/dev/null || echo 0)
        rm "$file"
        ((cleaned++))
        ((total_size+=size))
        log_info "删除: $(basename "$file") ($(numfmt --to=iec $size))"
    done < <(find "$LOG_DIR" -name "*.log*" -type f -mtime +$DAYS -print0)
    
    if [[ $cleaned -gt 0 ]]; then
        log_success "清理完成: 删除 $cleaned 个文件，释放 $(numfmt --to=iec $total_size) 空间"
    else
        log_info "没有找到需要清理的文件"
    fi
}

# 压缩日志文件
compress_logs() {
    log_info "开始压缩日志文件..."
    
    local compressed=0
    local files=$(get_log_files "$LOG_TYPE")
    
    for file in $files; do
        if [[ -f "$file" && ! "$file" =~ \.gz$ ]]; then
            local size_before=$(stat -f%z "$file")
            gzip "$file"
            local size_after=$(stat -f%z "$file.gz")
            local ratio=$(( (size_before - size_after) * 100 / size_before ))
            
            ((compressed++))
            log_info "压缩: $(basename "$file") -> $(basename "$file.gz") (压缩率: ${ratio}%)"
        fi
    done
    
    if [[ $compressed -gt 0 ]]; then
        log_success "压缩完成: 处理 $compressed 个文件"
    else
        log_info "没有找到需要压缩的文件"
    fi
}

# 分析日志统计
analyze_logs() {
    log_info "开始分析日志统计信息..."
    
    echo "=== 日志文件统计 ==="
    echo "总文件数: $(find "$LOG_DIR" -name "*.log*" -type f | wc -l)"
    echo "总大小: $(du -sh "$LOG_DIR" | cut -f1)"
    echo ""
    
    echo "=== 按类型分类 ==="
    for type in app celery error access; do
        local count=$(get_log_files "$type" | wc -l)
        local size=$(get_log_files "$type" | xargs du -ch 2>/dev/null | tail -1 | cut -f1 || echo "0")
        echo "$type 日志: $count 个文件, $size"
    done
    echo ""
    
    echo "=== 最大的日志文件 ==="
    find "$LOG_DIR" -name "*.log*" -type f -exec ls -lh {} \; | sort -k5 -hr | head -5
    echo ""
    
    echo "=== 最近的日志活动 ==="
    find "$LOG_DIR" -name "*.log" -type f -exec ls -lt {} \; | head -5
}

# 备份日志文件
backup_logs() {
    log_info "开始备份日志文件..."
    
    local backup_name="audit-logs-$(date +%Y%m%d-%H%M%S).tar.gz"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    tar -czf "$backup_path" -C "$LOG_DIR" . --exclude="backup"
    
    local backup_size=$(stat -f%z "$backup_path")
    log_success "备份完成: $backup_name ($(numfmt --to=iec $backup_size))"
}

# 实时查看日志
tail_logs() {
    if [[ -n "$LOG_FILE" ]]; then
        local file_path="$LOG_DIR/$LOG_FILE"
        if [[ -f "$file_path" ]]; then
            log_info "实时查看: $LOG_FILE"
            tail -f "$file_path"
        else
            log_error "文件不存在: $LOG_FILE"
            exit 1
        fi
    else
        log_info "实时查看所有日志文件"
        tail -f "$LOG_DIR"/*.log
    fi
}

# 搜索日志内容
search_logs() {
    if [[ -z "$QUERY" ]]; then
        log_error "请指定搜索查询字符串 (-q)"
        exit 1
    fi
    
    log_info "搜索日志内容: \"$QUERY\""
    
    local files=$(get_log_files "$LOG_TYPE")
    local results=0
    
    for file in $files; do
        if [[ -f "$file" ]]; then
            local matches=$(grep -c "$QUERY" "$file" 2>/dev/null || echo 0)
            if [[ $matches -gt 0 ]]; then
                echo "=== $(basename "$file") ($matches 个匹配) ==="
                grep -n --color=always "$QUERY" "$file" | head -$LINES
                echo ""
                ((results+=matches))
            fi
        fi
    done
    
    log_success "搜索完成: 找到 $results 个匹配结果"
}

# 执行命令
case $COMMAND in
    clean)
        clean_logs
        ;;
    compress)
        compress_logs
        ;;
    analyze)
        analyze_logs
        ;;
    backup)
        backup_logs
        ;;
    tail)
        tail_logs
        ;;
    search)
        search_logs
        ;;
    *)
        log_error "未实现的命令: $COMMAND"
        exit 1
        ;;
esac
