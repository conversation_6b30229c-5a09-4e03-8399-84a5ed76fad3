#!/bin/bash

# ELK Stack Maintenance Script
# Provides various maintenance operations for the ELK stack

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check if service is running
check_service() {
    local service_name=$1
    local port=$2
    
    if curl -s -f "http://localhost:$port" > /dev/null 2>&1; then
        print_status "$service_name is running on port $port"
        return 0
    else
        print_error "$service_name is not responding on port $port"
        return 1
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local port=$2
    local max_attempts=${3:-30}
    local attempt=1
    
    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if check_service "$service_name" "$port"; then
            return 0
        fi
        
        echo "Attempt $attempt/$max_attempts - waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    print_error "$service_name failed to start within $((max_attempts * 10)) seconds"
    return 1
}

# Function to show service status
show_status() {
    print_header "ELK Stack Status"
    
    echo "Checking services..."
    
    # Check Elasticsearch
    if check_service "Elasticsearch" "9200"; then
        echo "  - Cluster health: $(curl -s http://localhost:9200/_cluster/health | jq -r '.status' 2>/dev/null || echo 'unknown')"
        echo "  - Indices: $(curl -s http://localhost:9200/_cat/indices?h=index | wc -l 2>/dev/null || echo 'unknown')"
    fi
    
    # Check Logstash
    if check_service "Logstash" "9600"; then
        echo "  - Pipeline status: $(curl -s http://localhost:9600/_node/stats/pipeline | jq -r '.pipeline.main.events.in' 2>/dev/null || echo 'unknown') events processed"
    fi
    
    # Check Kibana
    check_service "Kibana" "5601"
    
    # Check Docker containers
    echo ""
    print_status "Docker containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(audit_|elk)" || echo "No ELK containers running"
}

# Function to clean old indices
clean_indices() {
    local days=${1:-7}
    print_header "Cleaning indices older than $days days"
    
    if ! check_service "Elasticsearch" "9200"; then
        print_error "Elasticsearch is not running"
        return 1
    fi
    
    # Get indices older than specified days
    local cutoff_date=$(date -d "$days days ago" +%Y.%m.%d)
    
    print_status "Looking for indices older than $cutoff_date..."
    
    # List indices and filter by date
    local old_indices=$(curl -s "http://localhost:9200/_cat/indices/audit-service-logs-*?h=index" | \
        grep -E "audit-service-logs-[0-9]{4}\.[0-9]{2}\.[0-9]{2}" | \
        while read index; do
            index_date=$(echo "$index" | grep -o '[0-9]{4}\.[0-9]{2}\.[0-9]{2}$')
            if [[ "$index_date" < "$cutoff_date" ]]; then
                echo "$index"
            fi
        done)
    
    if [ -z "$old_indices" ]; then
        print_status "No old indices found"
        return 0
    fi
    
    echo "Found old indices:"
    echo "$old_indices"
    
    read -p "Delete these indices? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        for index in $old_indices; do
            print_status "Deleting index: $index"
            curl -X DELETE "http://localhost:9200/$index"
            echo
        done
        print_status "Old indices cleaned up"
    else
        print_status "Cleanup cancelled"
    fi
}

# Function to backup indices
backup_indices() {
    local backup_name=${1:-"backup_$(date +%Y%m%d_%H%M%S)"}
    print_header "Creating backup: $backup_name"
    
    if ! check_service "Elasticsearch" "9200"; then
        print_error "Elasticsearch is not running"
        return 1
    fi
    
    # Create snapshot repository if it doesn't exist
    print_status "Setting up snapshot repository..."
    curl -X PUT "http://localhost:9200/_snapshot/backup" -H 'Content-Type: application/json' -d'{
        "type": "fs",
        "settings": {
            "location": "/usr/share/elasticsearch/backup"
        }
    }'
    echo
    
    # Create snapshot
    print_status "Creating snapshot: $backup_name"
    curl -X PUT "http://localhost:9200/_snapshot/backup/$backup_name" -H 'Content-Type: application/json' -d'{
        "indices": "audit-service-logs-*",
        "ignore_unavailable": true,
        "include_global_state": false
    }'
    echo
    
    print_status "Backup initiated. Check status with: curl http://localhost:9200/_snapshot/backup/$backup_name"
}

# Function to restore from backup
restore_backup() {
    local backup_name=$1
    
    if [ -z "$backup_name" ]; then
        print_error "Please specify backup name"
        echo "Available backups:"
        curl -s "http://localhost:9200/_snapshot/backup/_all" | jq -r '.snapshots[].snapshot'
        return 1
    fi
    
    print_header "Restoring from backup: $backup_name"
    
    if ! check_service "Elasticsearch" "9200"; then
        print_error "Elasticsearch is not running"
        return 1
    fi
    
    print_warning "This will restore indices from backup. Existing data may be overwritten."
    read -p "Continue? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        curl -X POST "http://localhost:9200/_snapshot/backup/$backup_name/_restore" -H 'Content-Type: application/json' -d'{
            "ignore_unavailable": true,
            "include_global_state": false
        }'
        echo
        print_status "Restore initiated"
    else
        print_status "Restore cancelled"
    fi
}

# Function to optimize indices
optimize_indices() {
    print_header "Optimizing indices"
    
    if ! check_service "Elasticsearch" "9200"; then
        print_error "Elasticsearch is not running"
        return 1
    fi
    
    print_status "Force merging audit service indices..."
    curl -X POST "http://localhost:9200/audit-service-logs-*/_forcemerge?max_num_segments=1"
    echo
    
    print_status "Refreshing indices..."
    curl -X POST "http://localhost:9200/audit-service-logs-*/_refresh"
    echo
    
    print_status "Indices optimized"
}

# Function to show logs
show_logs() {
    local service=${1:-"all"}
    print_header "Showing logs for: $service"
    
    case $service in
        "elasticsearch"|"es")
            docker logs -f audit_elasticsearch
            ;;
        "logstash"|"ls")
            docker logs -f audit_logstash
            ;;
        "kibana"|"kb")
            docker logs -f audit_kibana
            ;;
        "filebeat"|"fb")
            docker logs -f audit_filebeat
            ;;
        "metricbeat"|"mb")
            docker logs -f audit_metricbeat
            ;;
        "all")
            docker-compose -f docker-compose.elk.yml logs -f
            ;;
        *)
            print_error "Unknown service: $service"
            echo "Available services: elasticsearch, logstash, kibana, filebeat, metricbeat, all"
            return 1
            ;;
    esac
}

# Function to restart services
restart_service() {
    local service=${1:-"all"}
    print_header "Restarting service: $service"
    
    case $service in
        "elasticsearch"|"es")
            docker-compose -f docker-compose.elk.yml restart elasticsearch
            wait_for_service "Elasticsearch" "9200"
            ;;
        "logstash"|"ls")
            docker-compose -f docker-compose.elk.yml restart logstash
            wait_for_service "Logstash" "9600"
            ;;
        "kibana"|"kb")
            docker-compose -f docker-compose.elk.yml restart kibana
            wait_for_service "Kibana" "5601"
            ;;
        "filebeat"|"fb")
            docker-compose -f docker-compose.elk.yml restart filebeat
            ;;
        "metricbeat"|"mb")
            docker-compose -f docker-compose.elk.yml restart metricbeat
            ;;
        "all")
            docker-compose -f docker-compose.elk.yml restart
            wait_for_service "Elasticsearch" "9200"
            wait_for_service "Logstash" "9600"
            wait_for_service "Kibana" "5601"
            ;;
        *)
            print_error "Unknown service: $service"
            echo "Available services: elasticsearch, logstash, kibana, filebeat, metricbeat, all"
            return 1
            ;;
    esac
    
    print_status "Service $service restarted successfully"
}

# Function to show help
show_help() {
    echo "ELK Stack Maintenance Script"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  status                    - Show status of all ELK services"
    echo "  clean [days]             - Clean indices older than N days (default: 7)"
    echo "  backup [name]            - Create backup snapshot"
    echo "  restore <name>           - Restore from backup snapshot"
    echo "  optimize                 - Optimize indices (force merge)"
    echo "  logs [service]           - Show logs for service (default: all)"
    echo "  restart [service]        - Restart service (default: all)"
    echo "  help                     - Show this help message"
    echo ""
    echo "Services: elasticsearch, logstash, kibana, filebeat, metricbeat, all"
    echo ""
    echo "Examples:"
    echo "  $0 status"
    echo "  $0 clean 14"
    echo "  $0 backup daily_backup"
    echo "  $0 logs elasticsearch"
    echo "  $0 restart logstash"
}

# Main script logic
main() {
    cd "$PROJECT_DIR"
    
    case "${1:-help}" in
        "status")
            show_status
            ;;
        "clean")
            clean_indices "${2:-7}"
            ;;
        "backup")
            backup_indices "$2"
            ;;
        "restore")
            restore_backup "$2"
            ;;
        "optimize")
            optimize_indices
            ;;
        "logs")
            show_logs "$2"
            ;;
        "restart")
            restart_service "$2"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
