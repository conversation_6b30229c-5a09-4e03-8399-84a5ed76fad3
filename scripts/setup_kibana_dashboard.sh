#!/bin/bash

# Kibana仪表盘快速配置脚本
# 用于自动创建索引模式和导入仪表盘模板

set -e

# 配置变量
KIBANA_URL="http://localhost:5601"
ELASTICSEARCH_URL="http://localhost:9200"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查Elasticsearch
    if curl -s "$ELASTICSEARCH_URL/_cluster/health" > /dev/null; then
        log_success "Elasticsearch服务正常"
    else
        log_error "Elasticsearch服务不可用，请先启动服务"
        exit 1
    fi
    
    # 检查Kibana
    if curl -s "$KIBANA_URL/api/status" > /dev/null; then
        log_success "Kibana服务正常"
    else
        log_error "Kibana服务不可用，请先启动服务"
        exit 1
    fi
}

# 等待Kibana完全启动
wait_for_kibana() {
    log_info "等待Kibana完全启动..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$KIBANA_URL/api/status" | grep -q "available"; then
            log_success "Kibana已准备就绪"
            return 0
        fi
        
        log_info "等待Kibana启动... ($attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    log_error "Kibana启动超时"
    exit 1
}

# 创建索引模式
create_index_patterns() {
    log_info "创建索引模式..."
    
    # 创建审计日志索引模式
    log_info "创建audit-logs-*索引模式..."
    curl -X POST "$KIBANA_URL/api/saved_objects/index-pattern/audit-logs-pattern" \
        -H "Content-Type: application/json" \
        -H "kbn-xsrf: true" \
        -d '{
            "attributes": {
                "title": "audit-logs-*",
                "timeFieldName": "@timestamp",
                "fields": "[{\"name\":\"@timestamp\",\"type\":\"date\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"level\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"service\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"message\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":false},{\"name\":\"user_id\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"endpoint\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"method\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"response_time\",\"type\":\"number\",\"searchable\":true,\"aggregatable\":true}]"
            }
        }' 2>/dev/null
    
    if [ $? -eq 0 ]; then
        log_success "audit-logs-*索引模式创建成功"
    else
        log_warning "audit-logs-*索引模式可能已存在"
    fi
    
    # 创建系统指标索引模式（如果需要）
    log_info "创建metricbeat-*索引模式..."
    curl -X POST "$KIBANA_URL/api/saved_objects/index-pattern/metricbeat-pattern" \
        -H "Content-Type: application/json" \
        -H "kbn-xsrf: true" \
        -d '{
            "attributes": {
                "title": "metricbeat-*",
                "timeFieldName": "@timestamp"
            }
        }' 2>/dev/null
    
    if [ $? -eq 0 ]; then
        log_success "metricbeat-*索引模式创建成功"
    else
        log_warning "metricbeat-*索引模式可能已存在"
    fi
}

# 设置默认索引模式
set_default_index_pattern() {
    log_info "设置默认索引模式..."
    
    curl -X POST "$KIBANA_URL/api/kibana/settings/defaultIndex" \
        -H "Content-Type: application/json" \
        -H "kbn-xsrf: true" \
        -d '{
            "value": "audit-logs-pattern"
        }' 2>/dev/null
    
    if [ $? -eq 0 ]; then
        log_success "默认索引模式设置成功"
    else
        log_warning "默认索引模式设置可能失败"
    fi
}

# 导入仪表盘模板
import_dashboard_templates() {
    log_info "导入仪表盘模板..."
    
    local template_file="$PROJECT_ROOT/docs/monitoring/kibana_dashboard_templates.json"
    
    if [ -f "$template_file" ]; then
        curl -X POST "$KIBANA_URL/api/saved_objects/_import" \
            -H "kbn-xsrf: true" \
            -F "file=@$template_file" 2>/dev/null
        
        if [ $? -eq 0 ]; then
            log_success "仪表盘模板导入成功"
        else
            log_warning "仪表盘模板导入可能失败，请手动导入"
        fi
    else
        log_warning "仪表盘模板文件不存在: $template_file"
    fi
}

# 创建示例数据（用于测试）
create_sample_data() {
    log_info "创建示例数据..."
    
    # 创建一些示例日志数据
    local current_time=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
    
    for i in {1..10}; do
        curl -X POST "$ELASTICSEARCH_URL/audit-logs-$(date +%Y.%m.%d)/_doc" \
            -H "Content-Type: application/json" \
            -d "{
                \"@timestamp\": \"$current_time\",
                \"level\": \"INFO\",
                \"service\": \"audit_api\",
                \"message\": \"API request processed\",
                \"user_id\": \"user$i\",
                \"endpoint\": \"/api/v1/audit\",
                \"method\": \"POST\",
                \"response_time\": $((RANDOM % 1000 + 100)),
                \"audit_event_type\": \"CREATE\",
                \"compliance_status\": \"COMPLIANT\"
            }" 2>/dev/null
    done
    
    log_success "示例数据创建完成"
}

# 显示访问信息
show_access_info() {
    log_success "Kibana仪表盘配置完成！"
    echo ""
    echo "🌐 访问地址："
    echo "   Kibana主页: $KIBANA_URL"
    echo "   仪表盘: $KIBANA_URL/app/dashboards"
    echo "   日志搜索: $KIBANA_URL/app/discover"
    echo ""
    echo "📊 可用的仪表盘："
    echo "   - 审计服务总览仪表盘"
    echo "   - 审计事件分析仪表盘"
    echo ""
    echo "🔍 常用搜索查询："
    echo "   - 查看错误日志: level:ERROR"
    echo "   - 查看API日志: service:\"audit_api\""
    echo "   - 查看慢请求: response_time:>1000"
    echo ""
    echo "📚 详细配置指南："
    echo "   $PROJECT_ROOT/docs/monitoring/KIBANA_DASHBOARD_GUIDE.md"
}

# 主函数
main() {
    log_info "开始配置Kibana仪表盘..."
    
    check_services
    wait_for_kibana
    create_index_patterns
    set_default_index_pattern
    import_dashboard_templates
    create_sample_data
    show_access_info
    
    log_success "Kibana仪表盘配置完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
