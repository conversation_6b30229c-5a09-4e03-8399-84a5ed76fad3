#!/bin/bash

# 审计服务状态检查脚本
# Audit Service Status Check Script

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="审计服务"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
审计服务状态检查脚本

用法: $0 [选项]

选项:
    -h, --help      显示此帮助信息
    -v, --verbose   详细输出
    -j, --json      JSON格式输出
    --elk           检查ELK服务状态

示例:
    $0              # 检查所有服务状态
    $0 --verbose    # 详细检查
    $0 --elk        # 检查ELK服务
    $0 --json       # JSON格式输出

EOF
}

# 默认参数
VERBOSE=false
JSON_OUTPUT=false
CHECK_ELK=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -j|--json)
            JSON_OUTPUT=true
            shift
            ;;
        --elk)
            CHECK_ELK=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查服务状态
check_service_status() {
    local service_name="$1"
    local check_command="$2"
    local expected_output="$3"
    
    if eval "$check_command" > /dev/null 2>&1; then
        if [[ "$JSON_OUTPUT" == true ]]; then
            echo "\"$service_name\": \"running\","
        else
            echo -e "  ✅ $service_name: ${GREEN}正常${NC}"
        fi
        return 0
    else
        if [[ "$JSON_OUTPUT" == true ]]; then
            echo "\"$service_name\": \"stopped\","
        else
            echo -e "  ❌ $service_name: ${RED}停止${NC}"
        fi
        return 1
    fi
}

# 检查URL状态
check_url_status() {
    local service_name="$1"
    local url="$2"
    local expected_code="${3:-200}"
    
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    
    if [[ "$response_code" == "$expected_code" ]] || [[ "$response_code" == "302" ]]; then
        if [[ "$JSON_OUTPUT" == true ]]; then
            echo "\"$service_name\": \"running\","
        else
            echo -e "  ✅ $service_name: ${GREEN}正常${NC} ($response_code) - $url"
        fi
        return 0
    else
        if [[ "$JSON_OUTPUT" == true ]]; then
            echo "\"$service_name\": \"error\","
        else
            echo -e "  ❌ $service_name: ${RED}异常${NC} ($response_code) - $url"
        fi
        return 1
    fi
}

# 检查数据状态
check_data_status() {
    if [[ "$JSON_OUTPUT" == true ]]; then
        echo "\"data\": {"
    else
        echo -e "\n${BLUE}📈 数据状态:${NC}"
    fi
    
    # 检查Elasticsearch中的数据
    if command -v jq > /dev/null && curl -s "http://localhost:9200/audit-logs-*/_search?size=0" > /dev/null 2>&1; then
        local doc_count=$(curl -s "http://localhost:9200/audit-logs-*/_search?size=0" | jq '.hits.total.value' 2>/dev/null || echo "0")
        if [[ "$JSON_OUTPUT" == true ]]; then
            echo "\"elasticsearch_docs\": $doc_count,"
        else
            if [[ "$doc_count" -gt 0 ]]; then
                echo -e "  ✅ 日志数据: ${GREEN}$doc_count 条记录${NC}"
            else
                echo -e "  ⚠️  日志数据: ${YELLOW}暂无数据${NC}"
            fi
        fi
    else
        if [[ "$JSON_OUTPUT" == true ]]; then
            echo "\"elasticsearch_docs\": \"unknown\","
        else
            echo -e "  ℹ️  日志数据: 无法检查（需要jq工具或Elasticsearch不可用）"
        fi
    fi
    
    if [[ "$JSON_OUTPUT" == true ]]; then
        echo "},"
    fi
}

# 主检查函数
main() {
    if [[ "$JSON_OUTPUT" == true ]]; then
        echo "{"
        echo "\"timestamp\": \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\","
        echo "\"services\": {"
    else
        echo -e "${BLUE}🔍 检查审计服务状态${NC}"
        echo "=================================================="
        echo ""
        echo -e "${BLUE}📋 基础服务状态:${NC}"
    fi
    
    local total_services=0
    local running_services=0
    
    # 检查API服务
    if check_url_status "API服务" "http://localhost:8000/api/v1/health"; then
        ((running_services++))
    fi
    ((total_services++))
    
    # 检查MySQL
    if check_service_status "MySQL" "docker ps --filter 'name=audit_mysql_dev' --filter 'status=running' | grep -q audit_mysql_dev"; then
        ((running_services++))
    fi
    ((total_services++))
    
    # 检查Redis
    if check_service_status "Redis" "docker ps --filter 'name=audit_redis_dev' --filter 'status=running' | grep -q audit_redis_dev"; then
        ((running_services++))
    fi
    ((total_services++))
    
    # 检查Celery Worker
    if check_service_status "Celery Worker" "docker ps --filter 'name=audit_celery_worker_dev' --filter 'status=running' | grep -q audit_celery_worker_dev"; then
        ((running_services++))
    fi
    ((total_services++))
    
    # 检查Flower
    if check_service_status "Flower" "docker ps --filter 'name=audit_flower_dev' --filter 'status=running' | grep -q audit_flower_dev"; then
        ((running_services++))
    fi
    ((total_services++))
    
    # 检查ELK服务（如果启用）
    if [[ "$CHECK_ELK" == true ]] || docker ps --filter "name=audit_elasticsearch" --filter "status=running" | grep -q audit_elasticsearch; then
        if [[ "$JSON_OUTPUT" != true ]]; then
            echo -e "\n${BLUE}📊 ELK日志分析系统:${NC}"
        fi
        
        # Elasticsearch
        if check_url_status "Elasticsearch" "http://localhost:9200/_cluster/health"; then
            ((running_services++))
        fi
        ((total_services++))
        
        # Kibana
        if check_url_status "Kibana" "http://localhost:5601/api/status"; then
            ((running_services++))
        fi
        ((total_services++))
        
        # Logstash
        if check_service_status "Logstash" "docker ps --filter 'name=audit_logstash' --filter 'status=running' | grep -q audit_logstash"; then
            ((running_services++))
        fi
        ((total_services++))
        
        # Filebeat
        if check_service_status "Filebeat" "docker ps --filter 'name=audit_filebeat' --filter 'status=running' | grep -q audit_filebeat"; then
            ((running_services++))
        fi
        ((total_services++))
    fi
    
    # 检查数据状态
    check_data_status
    
    if [[ "$JSON_OUTPUT" == true ]]; then
        echo "},"
        echo "\"summary\": {"
        echo "\"total_services\": $total_services,"
        echo "\"running_services\": $running_services,"
        echo "\"health_percentage\": $((running_services * 100 / total_services))"
        echo "},"
        echo "\"status\": \"$([ $running_services -eq $total_services ] && echo 'healthy' || echo 'degraded')\""
        echo "}"
    else
        echo ""
        echo -e "${BLUE}📊 服务总览:${NC}"
        echo -e "  运行中: ${GREEN}$running_services${NC}/$total_services"
        echo -e "  健康度: ${GREEN}$((running_services * 100 / total_services))%${NC}"
        
        if [[ $running_services -eq $total_services ]]; then
            echo -e "  状态: ${GREEN}健康${NC}"
        elif [[ $running_services -gt 0 ]]; then
            echo -e "  状态: ${YELLOW}部分可用${NC}"
        else
            echo -e "  状态: ${RED}不可用${NC}"
        fi
        
        echo ""
        echo -e "${BLUE}🌐 访问地址:${NC}"
        echo "  📊 Kibana仪表盘: http://localhost:5601/app/dashboards"
        echo "  🔍 日志搜索: http://localhost:5601/app/discover"
        echo "  🌐 API文档: http://localhost:8000/docs"
        echo "  ❤️  健康检查: http://localhost:8000/api/v1/health"
        echo "  🌸 Flower监控: http://localhost:5555"
        
        echo ""
        echo -e "${BLUE}🔧 管理命令:${NC}"
        echo "  启动服务: ./start.sh docker --with-elk"
        echo "  停止服务: ./stop.sh all"
        echo "  查看日志: docker logs audit_api_dev"
        echo "  重启ELK: ./docker/scripts/elk-manage.sh restart"
        
        echo ""
        echo -e "${GREEN}✅ 状态检查完成！${NC}"
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
