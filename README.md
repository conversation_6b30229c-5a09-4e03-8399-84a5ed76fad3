# Audit Service Standalone

A modern, scalable audit service built with FastAPI, Celery, Redis, and MySQL. This service is extracted and refactored from the original Flask-based audit service with significant improvements in architecture, performance, and maintainability.

## Features

- **FastAPI**: Modern, fast web framework with automatic API documentation
- **Celery**: Distributed task queue for asynchronous processing
- **Redis**: High-performance caching and message broker
- **MySQL**: Reliable relational database for persistent storage
- **Docker**: Containerized deployment with docker-compose
- **ELK Stack**: Complete logging and monitoring solution (Elasticsearch, Logstash, Kibana, Filebeat)
- **Monitoring**: Health checks, metrics, and task monitoring with Flower
- **Structured Logging**: JSON-formatted logs with business context
- **Database Migrations**: Alembic for database schema management

## 📚 Documentation

For detailed documentation, please visit the [Documentation Center](docs/README.md):

- **🚀 [Quick Start Guide](docs/README.md#quick-start)** - Get up and running quickly
- **📖 [API Documentation](docs/api/)** - REST API reference
- **🔧 [Deployment Guide](docs/deployment/)** - Docker and production deployment
- **👨‍💻 [Development Guide](docs/development/)** - Local development setup and coding standards
- **📊 [Logging System](docs/logging/)** - Complete logging and ELK integration guide
- **📈 [Monitoring Guide](docs/monitoring/)** - Kibana dashboards and monitoring setup
- **🛠️ [Scripts Guide](docs/scripts/)** - Management scripts and tools
- **🧪 [Testing Guide](docs/testing/)** - Testing framework and best practices

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI App   │    │  Celery Worker  │    │  Celery Beat    │
│                 │    │                 │    │   (Scheduler)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
         │      Redis      │    │      MySQL      │    │     Flower      │
         │   (Broker/Cache)│    │   (Database)    │    │  (Monitoring)   │
         └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Database Schema

The service uses a MySQL database with the following main table:

### request_tracking
- `id` (BIGINT, PRIMARY KEY) - Auto-incrementing ID
- `request_start_time` (DATETIME) - When the request was initiated
- `request_end_time` (DATETIME) - When the request was completed
- `task_id` (VARCHAR(255), UNIQUE) - Unique identifier for the Celery task
- `task_name` (VARCHAR(255)) - Name/type of the task being executed
- `request_result` (JSON) - The result/response of the request
- `exception_info` (TEXT) - Any error or exception information
- `status` (ENUM) - Task status: pending, processing, completed, failed
- `created_at` (TIMESTAMP) - Record creation time
- `updated_at` (TIMESTAMP) - Record update time

## API Endpoints

### Health Check
- `GET /api/v1/health` - Service health status

### Audit Processing
- `POST /api/v1/audit/async` - Submit audit for asynchronous processing
- `POST /api/v1/audit/sync` - Process audit synchronously
- `GET /api/v1/audit/status/{task_id}` - Get task status
- `DELETE /api/v1/audit/task/{task_id}` - Cancel pending task

### Documentation
- `GET /docs` - Swagger UI (development only)
- `GET /redoc` - ReDoc documentation (development only)

## Quick Start

### 🐳 Docker部署（推荐）

我们提供了完整的Docker部署解决方案，支持开发、测试和生产环境。

#### 快速启动

```bash
# 开发环境
./docker/quick-start.sh dev

# 测试环境
./docker/quick-start.sh test

# 生产环境
./docker/quick-start.sh prod
```

#### 访问服务

**开发环境**：
- API服务：http://localhost:8000
- API文档：http://localhost:8000/docs
- Flower监控：http://localhost:5555
- 监控指标：http://localhost:8001/metrics

**测试环境**：
- API服务：http://localhost:8002

**生产环境**：
- API服务：http://localhost:80

#### 详细Docker使用指南

请参考 [Docker部署文档](docker/README.md) 获取完整的部署指南，包括：
- 多环境配置
- 镜像构建和优化
- 服务管理和监控
- 数据备份和恢复
- 故障排除

### 🔧 传统Docker Compose部署

如果你更喜欢使用现有的docker-compose.yml：

1. Clone the repository:
```bash
git clone <repository-url>
cd audit_service_standalone
```

2. Copy environment file:
```bash
cp .env.example .env
```

3. Start all services:
```bash
docker-compose up -d
```

4. Check service health:
```bash
curl http://localhost:8000/api/v1/health
```

5. Access services:
- API: http://localhost:8000
- API Documentation: http://localhost:8000/docs
- Flower (Celery monitoring): http://localhost:5555
- MySQL: localhost:3306
- Redis: localhost:6379

### 🔧 简化启动（一键启动）

我们提供了简化的启动脚本，支持多种启动模式：

```bash
# 默认启动（Docker开发环境）
./start.sh

# Docker模式启动
./start.sh docker

# 本地环境启动
./start.sh local

# 开发模式启动
./start.sh dev

# 生产环境启动
./start.sh docker -e prod

# 停止服务
./stop.sh
```

### 🛠️ 本地开发环境

如果您需要在本地环境进行开发：

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your local configuration
```

3. Start with local mode:
```bash
./start.sh local
```

或者手动启动：

4. Start external services (MySQL, Redis):
```bash
docker-compose up -d mysql redis
```

5. Run database migrations:
```bash
alembic upgrade head
```

6. Start the FastAPI application:
```bash
uvicorn app.main:app --reload
```

7. Start Celery worker (in another terminal):
```bash
celery -A app.tasks.celery_tasks worker --loglevel=info
```

8. Start Celery beat (in another terminal):
```bash
celery -A app.tasks.celery_tasks beat --loglevel=info
```

## Usage Examples

### Asynchronous Audit Processing

```bash
curl -X POST "http://localhost:8000/api/v1/audit/async" \
  -H "Content-Type: application/json" \
  -d '{
    "task_name": "document_audit",
    "config": "global:\n  RELEVANCE_MODEL: qwen2-7b-8k-simple_tasks_v1\n  SUMMARY_MODEL: Qwen2-72B-Instruct-GPTQ-Int4",
    "main_doc": "{\"content\": \"Sample document content\"}",
    "callback_url": "http://your-service.com/callback"
  }'
```

### Synchronous Audit Processing

```bash
curl -X POST "http://localhost:8000/api/v1/audit/sync" \
  -H "Content-Type: application/json" \
  -d '{
    "task_name": "document_audit",
    "config": "global:\n  RELEVANCE_MODEL: qwen2-7b-8k-simple_tasks_v1",
    "main_doc": "{\"content\": \"Sample document content\"}"
  }'
```

### Check Task Status

```bash
curl "http://localhost:8000/api/v1/audit/status/{task_id}"
```

## Configuration

Key configuration options in `.env`:

- `DATABASE_URL`: MySQL connection string
- `REDIS_URL`: Redis connection string
- `CELERY_BROKER_URL`: Celery broker URL (Redis)
- `EMBEDDING_API_URL`: External embedding service URL
- `KBS_ADDRESS`: Knowledge base service address
- `UPLOAD_DIR`: Directory for uploaded files
- `TEMP_DIR`: Directory for temporary files

## Monitoring

### Health Checks
The service provides comprehensive health checks:
- Database connectivity
- Redis connectivity
- Celery worker status

### Metrics
Prometheus metrics are available at `/metrics` (if enabled).

### Task Monitoring
Use Flower to monitor Celery tasks:
- Task status and history
- Worker status and statistics
- Real-time task monitoring

## Development

### Code Structure
```
app/
├── api/v1/          # API endpoints
├── core/            # Core configuration and database
├── models/          # Database and Pydantic models
├── services/        # Business logic services
├── tasks/           # Celery tasks
└── utils/           # Utility functions
```

### Adding New Features
1. Add database models in `app/models/database.py`
2. Create Pydantic schemas in `app/models/schemas.py`
3. Implement business logic in `app/services/`
4. Add API endpoints in `app/api/v1/`
5. Create Celery tasks in `app/tasks/`

### Database Migrations
```bash
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

## Deployment

### Production Considerations
1. Use environment-specific configuration
2. Set up proper logging and monitoring
3. Configure load balancing for multiple workers
4. Set up database backups
5. Use secrets management for sensitive data
6. Configure SSL/TLS termination

### Scaling
- Scale Celery workers horizontally
- Use Redis Cluster for high availability
- Set up MySQL replication
- Use container orchestration (Kubernetes)

## Troubleshooting

### Common Issues
1. **Database connection errors**: Check MySQL service and credentials
2. **Redis connection errors**: Verify Redis service is running
3. **Celery tasks not processing**: Check worker status and broker connection
4. **File upload errors**: Verify upload directory permissions

### Logs
- Application logs: `./logs/`
- Docker logs: `docker-compose logs [service]`
- Celery logs: Available in Flower UI

## 📚 Documentation Center

For comprehensive documentation, please visit our [Documentation Center](docs/README.md):

### 🎯 Quick Links
- **[📚 Documentation Hub](docs/README.md)** - Complete documentation index and navigation
- **[🚀 Quick Start](docs/README.md#quick-start)** - Get started in minutes
- **[🐳 Docker Deployment](docker/README.md)** - Detailed Docker deployment guide
- **[🧪 Testing Guide](tests/README.md)** - Complete testing framework documentation

### 📖 Detailed Guides
- **[👨‍💻 Development Setup](docs/development/setup.md)** - Local development environment
- **[📝 Coding Standards](docs/development/coding-standards.md)** - Code quality and best practices
- **[📊 Logging System](docs/logging/LOGGING_SYSTEM_ENHANCEMENT.md)** - Complete logging architecture
- **[📈 ELK Integration](docs/logging/README_ELK.md)** - Elasticsearch, Logstash, Kibana setup
- **[🛠️ Script Tools](docs/scripts/README_SCRIPTS.md)** - Management scripts and utilities
- **[📊 Monitoring](docs/monitoring/)** - Kibana dashboards and monitoring setup

### 🔗 Quick Access
- **API Documentation**: http://localhost:8000/docs
- **Kibana Dashboard**: http://localhost:5601/app/dashboards
- **Flower Monitoring**: http://localhost:5555
- **Health Check**: http://localhost:8000/api/v1/health

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]
