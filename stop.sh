#!/bin/bash
# 审计服务停止脚本
# Audit Service Stop Script

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="审计服务"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
审计服务停止脚本

用法: $0 [选项] [模式]

模式:
    all         停止所有服务（默认）
    docker      仅停止Docker服务
    local       仅停止本地服务
    elk         仅停止ELK日志系统
    clean       停止服务并清理资源

选项:
    -h, --help          显示此帮助信息
    -e, --env           指定环境 (dev|test|prod，仅Docker模式)
    -f, --force         强制停止
    -q, --quiet         静默模式
    --rm-containers     停止后删除容器
    --rm-images         停止后删除镜像
    --rm-volumes        停止后删除数据卷
    --rm-all            停止后删除所有资源（容器+镜像+数据卷）

示例:
    $0                          # 停止所有服务
    $0 docker                   # 仅停止Docker服务
    $0 local                    # 仅停止本地服务
    $0 elk                      # 仅停止ELK服务
    $0 clean                    # 停止服务并清理资源
    $0 docker --rm-containers   # 停止Docker服务并删除容器
    $0 all --rm-all             # 停止所有服务并删除所有资源
    $0 docker -e prod           # 停止生产环境Docker服务

EOF
}

# 默认参数
MODE="all"
ENVIRONMENT="dev"
FORCE=false
QUIET=false
RM_CONTAINERS=false
RM_IMAGES=false
RM_VOLUMES=false
RM_ALL=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -q|--quiet)
            QUIET=true
            shift
            ;;
        --rm-containers)
            RM_CONTAINERS=true
            shift
            ;;
        --rm-images)
            RM_IMAGES=true
            shift
            ;;
        --rm-volumes)
            RM_VOLUMES=true
            shift
            ;;
        --rm-all)
            RM_ALL=true
            RM_CONTAINERS=true
            RM_IMAGES=true
            RM_VOLUMES=true
            shift
            ;;
        all|docker|local|clean|elk)
            MODE="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 显示停止信息
if [[ "$QUIET" != true ]]; then
    cat << EOF
🛑 $PROJECT_NAME 停止脚本
================================
模式: $MODE
环境: $ENVIRONMENT
时间: $(date)
================================

EOF
fi

# 停止本地进程
stop_local_processes() {
    log_step "停止本地进程..."
    
    # 停止FastAPI应用
    log_info "停止FastAPI应用..."
    if pgrep -f "uvicorn app.main:app" > /dev/null; then
        pkill -f "uvicorn app.main:app" || true
        log_success "FastAPI应用已停止"
    else
        log_info "FastAPI应用未运行"
    fi
    
    # 停止Celery Worker
    log_info "停止Celery Worker..."
    if pgrep -f "celery.*worker" > /dev/null; then
        pkill -f "celery.*worker" || true
        log_success "Celery Worker已停止"
    else
        log_info "Celery Worker未运行"
    fi
    
    # 停止Flower监控
    log_info "停止Flower监控..."
    if pgrep -f "celery.*flower" > /dev/null; then
        pkill -f "celery.*flower" || true
        log_success "Flower监控已停止"
    else
        log_info "Flower监控未运行"
    fi
    
    # 如果强制模式，杀死所有相关进程
    if [[ "$FORCE" == true ]]; then
        log_warning "强制模式：杀死所有Python进程..."
        pkill -f "python.*app" || true
        pkill -f "python.*celery" || true
    fi
}

# 停止Docker服务
stop_docker_services() {
    log_step "停止Docker服务..."
    
    # 检查是否有新的Docker部署体系
    if [[ -f "docker/scripts/deploy.sh" ]]; then
        log_info "使用新的Docker部署体系停止服务..."
        ./docker/scripts/deploy.sh "$ENVIRONMENT" down
    else
        log_info "使用传统Docker Compose停止服务..."
        
        # 停止docker-compose服务
        if [[ -f "docker-compose.yml" ]]; then
            if [[ "$FORCE" == true ]]; then
                docker-compose down --volumes --remove-orphans
            else
                docker-compose down
            fi
            log_success "Docker Compose服务已停止"
        else
            log_warning "docker-compose.yml文件不存在"
        fi
        
        # 停止ELK服务（如果存在）
        if [[ -f "docker-compose.elk.yml" ]]; then
            log_info "停止ELK服务..."
            docker-compose -f docker-compose.elk.yml down
            log_success "ELK服务已停止"
        fi
    fi
    
    # 停止相关容器
    log_info "检查并停止相关容器..."
    local containers=$(docker ps --format "{{.Names}}" | grep -E "(audit|mysql|redis|elasticsearch|logstash|kibana)" || true)
    
    if [[ -n "$containers" ]]; then
        log_info "发现运行中的相关容器，正在停止..."
        echo "$containers" | xargs docker stop || true
        log_success "相关容器已停止"
    else
        log_info "没有发现运行中的相关容器"
    fi
}

# 清理资源
clean_resources() {
    log_step "清理系统资源..."
    
    # 清理Docker资源
    if command -v docker &> /dev/null; then
        log_info "清理Docker资源..."
        
        # 清理停止的容器
        docker container prune -f || true
        
        # 清理未使用的镜像
        docker image prune -f || true
        
        # 清理未使用的网络
        docker network prune -f || true
        
        # 如果强制模式，清理所有未使用的资源
        if [[ "$FORCE" == true ]]; then
            log_warning "强制模式：清理所有未使用的Docker资源..."
            docker system prune -af || true
        fi
        
        log_success "Docker资源清理完成"
    fi
    
    # 清理临时文件
    log_info "清理临时文件..."
    if [[ -d "temp" ]]; then
        find temp -type f -mtime +1 -delete 2>/dev/null || true
    fi
    
    # 清理日志文件（如果强制模式）
    if [[ "$FORCE" == true ]]; then
        log_warning "强制模式：清理日志文件..."
        if [[ -d "logs" ]]; then
            find logs -name "*.log.*" -mtime +7 -delete 2>/dev/null || true
        fi
    fi
    
    log_success "资源清理完成"
}

# 删除容器
remove_containers() {
    if [[ "$RM_CONTAINERS" == true ]] || [[ "$RM_ALL" == true ]]; then
        log_step "删除审计服务相关容器..."

        # 获取所有相关容器
        local containers=$(docker ps -aq --filter "name=audit_" 2>/dev/null || true)

        if [[ -n "$containers" ]]; then
            log_info "删除容器: $(echo $containers | tr '\n' ' ')"
            docker rm -f $containers 2>/dev/null || true
            log_success "容器删除完成"
        else
            log_info "没有找到需要删除的容器"
        fi
    fi
}

# 删除镜像
remove_images() {
    if [[ "$RM_IMAGES" == true ]] || [[ "$RM_ALL" == true ]]; then
        log_step "删除审计服务相关镜像..."

        # 获取所有相关镜像
        local images=$(docker images --filter "reference=audit_*" -q 2>/dev/null || true)
        local elk_images=$(docker images --filter "reference=*elasticsearch*" --filter "reference=*kibana*" --filter "reference=*logstash*" --filter "reference=*filebeat*" --filter "reference=*metricbeat*" -q 2>/dev/null || true)

        if [[ -n "$images" ]]; then
            log_info "删除审计服务镜像..."
            docker rmi -f $images 2>/dev/null || true
        fi

        if [[ -n "$elk_images" ]]; then
            log_info "删除ELK相关镜像..."
            docker rmi -f $elk_images 2>/dev/null || true
        fi

        if [[ -n "$images" ]] || [[ -n "$elk_images" ]]; then
            log_success "镜像删除完成"
        else
            log_info "没有找到需要删除的镜像"
        fi
    fi
}

# 删除数据卷
remove_volumes() {
    if [[ "$RM_VOLUMES" == true ]] || [[ "$RM_ALL" == true ]]; then
        log_step "删除审计服务相关数据卷..."

        # 获取所有相关数据卷
        local volumes=$(docker volume ls --filter "name=audit_" -q 2>/dev/null || true)
        local elk_volumes=$(docker volume ls --filter "name=elasticsearch" --filter "name=kibana" --filter "name=logstash" -q 2>/dev/null || true)

        if [[ -n "$volumes" ]]; then
            log_info "删除审计服务数据卷..."
            docker volume rm $volumes 2>/dev/null || true
        fi

        if [[ -n "$elk_volumes" ]]; then
            log_info "删除ELK相关数据卷..."
            docker volume rm $elk_volumes 2>/dev/null || true
        fi

        if [[ -n "$volumes" ]] || [[ -n "$elk_volumes" ]]; then
            log_success "数据卷删除完成"
        else
            log_info "没有找到需要删除的数据卷"
        fi
    fi
}

# 执行清理操作
perform_cleanup() {
    if [[ "$RM_CONTAINERS" == true ]] || [[ "$RM_IMAGES" == true ]] || [[ "$RM_VOLUMES" == true ]] || [[ "$RM_ALL" == true ]]; then
        log_step "执行资源清理..."

        remove_containers
        remove_images
        remove_volumes

        # 清理未使用的资源
        if [[ "$RM_ALL" == true ]]; then
            log_info "清理所有未使用的Docker资源..."
            docker system prune -af --volumes 2>/dev/null || true
        fi

        log_success "资源清理完成"
    fi
}

# 检查服务状态
check_service_status() {
    log_step "检查服务状态..."
    
    local running_services=0
    
    # 检查本地进程
    if pgrep -f "uvicorn app.main:app" > /dev/null; then
        log_warning "FastAPI应用仍在运行"
        running_services=$((running_services + 1))
    fi
    
    if pgrep -f "celery.*worker" > /dev/null; then
        log_warning "Celery Worker仍在运行"
        running_services=$((running_services + 1))
    fi
    
    # 检查Docker容器
    if command -v docker &> /dev/null; then
        local containers=$(docker ps --format "{{.Names}}" | grep -E "(audit|mysql|redis)" | wc -l)
        if [[ $containers -gt 0 ]]; then
            log_warning "仍有 $containers 个相关Docker容器在运行"
            running_services=$((running_services + containers))
        fi
    fi
    
    if [[ $running_services -eq 0 ]]; then
        log_success "所有服务已完全停止"
    else
        log_warning "仍有 $running_services 个服务在运行"
        
        if [[ "$FORCE" != true ]]; then
            log_info "使用 -f 参数强制停止所有服务"
        fi
    fi
}

# 显示停止完成信息
show_completion_info() {
    if [[ "$QUIET" == true ]]; then
        return 0
    fi
    
    cat << EOF

🎉 服务停止完成！

🔧 相关命令:
   重新启动: ./start.sh
   查看状态: docker ps
   清理资源: ./stop.sh clean

EOF
}

# 主执行逻辑
main() {
    case $MODE in
        all)
            stop_local_processes
            stop_docker_services
            perform_cleanup
            ;;
        docker)
            stop_docker_services
            perform_cleanup
            ;;
        local)
            stop_local_processes
            perform_cleanup
            ;;
        clean)
            stop_local_processes
            stop_docker_services
            clean_resources
            perform_cleanup
            ;;
        elk)
            # 停止ELK日志系统
            log_info "停止ELK日志系统..."
            if [[ -f "docker/scripts/elk-manage.sh" ]]; then
                if [[ "$FORCE" == true ]]; then
                    ./docker/scripts/elk-manage.sh stop --force
                else
                    ./docker/scripts/elk-manage.sh stop
                fi
            else
                log_error "ELK管理脚本不存在"
                exit 1
            fi
            perform_cleanup
            ;;
        *)
            log_error "不支持的停止模式: $MODE"
            exit 1
            ;;
    esac
    
    # 检查服务状态
    check_service_status
    
    # 显示完成信息
    show_completion_info
}

# 执行主函数
main
