# 测试依赖包
# Test Dependencies

# 核心测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-xdist>=3.3.0
pytest-mock>=3.11.0

# HTTP测试
httpx>=0.24.0
requests-mock>=1.11.0

# 数据库测试
pytest-alembic>=0.10.0
sqlalchemy-utils>=0.41.0

# 性能和负载测试
pytest-benchmark>=4.0.0
locust>=2.15.0

# 测试报告
pytest-html>=3.2.0
pytest-json-report>=1.5.0
coverage[toml]>=7.2.0

# Mock和测试工具
factory-boy>=3.3.0
faker>=19.0.0
freezegun>=1.2.0
responses>=0.23.0

# 代码质量检查
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0
mypy>=1.4.0

# 安全测试
bandit>=1.7.0
safety>=2.3.0

# 文档测试
pytest-doctestplus>=1.0.0

# 并发测试
pytest-parallel>=0.1.0

# 测试数据生成
mimesis>=10.0.0

# API测试
tavern>=2.0.0

# 容器测试
testcontainers>=3.7.0

# 时间相关测试
time-machine>=2.10.0

# 环境变量测试
pytest-env>=0.8.0

# 临时文件测试
pytest-tmp-path>=0.1.0

# 日志测试
pytest-logging>=2016.11.4

# 参数化测试增强
pytest-cases>=3.6.0

# 测试顺序控制
pytest-order>=1.1.0

# 测试重试
pytest-rerunfailures>=11.1.0

# 测试超时
pytest-timeout>=2.1.0

# 测试标记增强
pytest-markers>=1.0.0

# 测试配置
pytest-ini>=1.0.0

# 测试清理
pytest-cleanuptotal>=0.1.0
