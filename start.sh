#!/bin/bash
# 审计服务启动脚本
# Audit Service Start Script

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="审计服务"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
审计服务启动脚本

用法: $0 [选项] [模式]

模式:
    docker      使用Docker启动（推荐）
    local       本地启动（需要本地环境）
    dev         开发模式启动
    test        测试模式启动

选项:
    -h, --help      显示此帮助信息
    -e, --env       指定环境 (dev|test|prod，仅Docker模式)
    -q, --quiet     静默模式
    --no-check      跳过环境检查
    --with-elk      同时启动ELK日志系统

示例:
    $0                      # 默认使用Docker开发环境启动
    $0 docker               # Docker开发环境启动
    $0 docker -e prod       # Docker生产环境启动
    $0 local                # 本地环境启动
    $0 dev                  # 开发模式启动
    $0 test                 # 测试模式启动
    $0 docker --with-elk    # Docker环境启动并包含ELK

EOF
}

# 默认参数
MODE="docker"
ENVIRONMENT="dev"
QUIET=false
NO_CHECK=false
WITH_ELK=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -q|--quiet)
            QUIET=true
            shift
            ;;
        --no-check)
            NO_CHECK=true
            shift
            ;;
        --with-elk)
            WITH_ELK=true
            shift
            ;;
        docker|local|dev|test)
            MODE="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 显示启动信息
if [[ "$QUIET" != true ]]; then
    cat << EOF
🚀 $PROJECT_NAME 启动脚本
================================
模式: $MODE
环境: $ENVIRONMENT
时间: $(date)
================================

EOF
fi

# 环境检查函数
check_requirements() {
    if [[ "$NO_CHECK" == true ]]; then
        log_info "跳过环境检查"
        return 0
    fi
    
    log_step "检查系统环境..."
    
    local failed=0
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        failed=1
    else
        local python_version=$(python3 --version | cut -d' ' -f2)
        log_info "Python版本: $python_version"
    fi
    
    # 检查Docker（如果需要）
    if [[ "$MODE" == "docker" ]]; then
        if ! command -v docker &> /dev/null; then
            log_error "Docker 未安装"
            failed=1
        else
            log_info "Docker版本: $(docker --version | cut -d' ' -f3 | tr -d ',')"
        fi
        
        if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
            log_error "Docker Compose 未安装"
            failed=1
        else
            if docker compose version &> /dev/null; then
                log_info "Docker Compose版本: $(docker compose version --short)"
            else
                log_info "Docker Compose版本: $(docker-compose --version | cut -d' ' -f3 | tr -d ',')"
            fi
        fi
    fi
    
    if [[ $failed -eq 1 ]]; then
        log_error "环境检查失败，请安装缺失的软件"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 创建必要目录
create_directories() {
    log_step "创建必要目录..."
    
    mkdir -p logs temp uploads
    
    log_success "目录创建完成"
}

# Docker模式启动
start_docker() {
    log_step "使用Docker启动服务..."
    
    if [[ -f "docker/quick-start.sh" ]]; then
        log_info "使用新的Docker部署体系启动..."
        ./docker/quick-start.sh "$ENVIRONMENT"

        # 如果需要启动ELK
        if [[ "$WITH_ELK" == true ]]; then
            log_info "启动ELK日志系统..."
            if [[ -f "docker/scripts/elk-manage.sh" ]]; then
                ./docker/scripts/elk-manage.sh start
            else
                log_warning "ELK管理脚本不存在，跳过ELK启动"
            fi
        fi
    else
        log_warning "新的Docker部署体系不存在，使用传统方式..."
        
        # 检查环境文件
        if [[ ! -f ".env" ]]; then
            if [[ -f ".env.example" ]]; then
                log_info "复制环境配置文件..."
                cp .env.example .env
            else
                log_error "环境配置文件不存在"
                exit 1
            fi
        fi
        
        # 启动服务
        if [[ -f "docker-compose.yml" ]]; then
            log_info "启动Docker Compose服务..."
            docker-compose up -d
        else
            log_error "docker-compose.yml 文件不存在"
            exit 1
        fi
        
        # 等待服务启动
        log_info "等待服务启动..."
        sleep 30
        
        # 健康检查
        if curl -f -s http://localhost:8000/api/v1/health > /dev/null; then
            log_success "服务启动成功"
        else
            log_warning "服务可能未完全启动，请稍后检查"
        fi
    fi
}

# 本地模式启动
start_local() {
    log_step "本地模式启动服务..."
    
    # 检查依赖
    if [[ ! -f "requirements.txt" ]]; then
        log_error "requirements.txt 文件不存在"
        exit 1
    fi
    
    # 安装依赖
    log_info "检查Python依赖..."
    if ! pip3 show fastapi > /dev/null 2>&1; then
        log_info "安装Python依赖..."
        pip3 install -r requirements.txt
    fi
    
    # 检查环境文件
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.example" ]]; then
            log_info "复制环境配置文件..."
            cp .env.example .env
            log_warning "请编辑 .env 文件配置数据库连接信息"
        fi
    fi
    
    # 启动外部服务（如果有docker-compose）
    if [[ -f "docker-compose.yml" ]]; then
        log_info "启动外部服务（MySQL, Redis）..."
        docker-compose up -d mysql redis
        sleep 15
    fi
    
    # 启动应用
    log_info "启动FastAPI应用..."
    nohup python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload > logs/app.log 2>&1 &
    
    # 启动Celery Worker
    log_info "启动Celery Worker..."
    nohup python3 -m celery -A app.tasks.celery_tasks worker --loglevel=info > logs/celery.log 2>&1 &
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 健康检查
    local retries=0
    while [[ $retries -lt 30 ]]; do
        if curl -f -s http://localhost:8000/api/v1/health > /dev/null; then
            log_success "服务启动成功"
            break
        fi
        sleep 2
        retries=$((retries + 1))
    done
    
    if [[ $retries -eq 30 ]]; then
        log_warning "服务启动超时，请检查日志文件"
    fi
}

# 开发模式启动
start_dev() {
    log_step "开发模式启动服务..."

    # 使用Docker开发环境
    if [[ -f "docker/quick-start.sh" ]]; then
        ./docker/quick-start.sh dev
    else
        # 回退到本地开发模式
        start_local
    fi
}

# 测试模式启动
start_test() {
    log_step "测试模式启动服务..."

    # 使用Docker测试环境
    if [[ -f "docker/quick-start.sh" ]]; then
        ./docker/quick-start.sh test
    else
        log_warning "新的Docker部署体系不存在，使用开发模式..."
        start_dev
    fi
}

# 显示服务信息
show_service_info() {
    if [[ "$QUIET" == true ]]; then
        return 0
    fi
    
    cat << EOF

🎉 服务启动完成！

📋 服务地址:
   🌐 API服务: http://localhost:8000
   📚 API文档: http://localhost:8000/docs
   ❤️  健康检查: http://localhost:8000/api/v1/health

EOF

    if [[ "$MODE" == "docker" ]] && [[ "$ENVIRONMENT" == "dev" ]]; then
        cat << EOF
   🌸 Flower监控: http://localhost:5555
   📊 监控指标: http://localhost:8001/metrics

EOF
    fi

    cat << EOF
🔧 管理命令:
   停止服务: ./stop.sh
   查看日志: tail -f logs/app.log
   启动ELK: ./docker/scripts/elk-manage.sh start

📊 日志分析 (可选):
   启动ELK后可访问: http://localhost:5601
   自动配置的仪表盘将包含完整的监控功能

EOF
}

# 主执行逻辑
main() {
    # 环境检查
    check_requirements
    
    # 创建目录
    create_directories
    
    # 根据模式启动服务
    case $MODE in
        docker)
            start_docker
            ;;
        local)
            start_local
            ;;
        dev)
            start_dev
            ;;
        test)
            start_test
            ;;
        *)
            log_error "不支持的启动模式: $MODE"
            exit 1
            ;;
    esac
    
    # 显示服务信息
    show_service_info
}

# 执行主函数
main
