# 📖 API接口文档

审计服务提供RESTful API接口，支持同步和异步审计处理。

## 🌐 API概览

### 基础信息
- **Base URL**: `http://localhost:8000`
- **API版本**: `v1`
- **API前缀**: `/api/v1`
- **文档地址**: http://localhost:8000/docs
- **OpenAPI规范**: http://localhost:8000/openapi.json

### 认证方式
目前API不需要认证，后续版本将支持：
- API Key认证
- JWT Token认证
- OAuth 2.0认证

## 🎯 核心接口

### 1. 健康检查
```http
GET /api/v1/health
```

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-08-01T06:00:00Z",
  "version": "1.0.0",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "celery": "healthy"
  }
}
```

### 2. 异步审计
```http
POST /api/v1/audit/async
Content-Type: application/json

{
  "task_name": "文档审计任务",
  "config": "yaml配置内容",
  "main_doc": "主文档JSON内容（可选）",
  "callback_url": "回调URL（可选）"
}
```

**响应示例**:
```json
{
  "task_id": "task_123456789",
  "status": "pending",
  "message": "任务已提交处理",
  "created_at": "2025-08-01T06:00:00Z"
}
```

### 3. 同步审计
```http
POST /api/v1/audit/sync
Content-Type: application/json

{
  "task_name": "文档审计任务",
  "config": "yaml配置内容",
  "main_doc": "主文档JSON内容（可选）"
}
```

**响应示例**:
```json
{
  "task_id": "task_123456789",
  "status": "completed",
  "message": "审计处理完成",
  "result": {
    "audit_results": [...],
    "summary": {...}
  },
  "created_at": "2025-08-01T06:00:00Z",
  "completed_at": "2025-08-01T06:00:30Z"
}
```

### 4. 任务状态查询
```http
GET /api/v1/audit/status/{task_id}
```

**响应示例**:
```json
{
  "task_id": "task_123456789",
  "status": "completed",
  "progress": 100,
  "result": {
    "audit_results": [...],
    "summary": {...}
  },
  "created_at": "2025-08-01T06:00:00Z",
  "started_at": "2025-08-01T06:00:01Z",
  "completed_at": "2025-08-01T06:00:30Z",
  "execution_time": 29.5
}
```

## 📊 数据模型

### AuditRequest
```json
{
  "task_name": "string (required)",
  "config": "string (required)",
  "main_doc": "string (optional)",
  "callback_url": "string (optional)"
}
```

### AuditResponse
```json
{
  "task_id": "string",
  "status": "pending|processing|completed|failed",
  "message": "string",
  "result": "object (optional)",
  "created_at": "datetime",
  "started_at": "datetime (optional)",
  "completed_at": "datetime (optional)",
  "execution_time": "float (optional)"
}
```

### TaskStatus
```json
{
  "task_id": "string",
  "status": "pending|processing|completed|failed",
  "progress": "integer (0-100)",
  "result": "object (optional)",
  "error": "string (optional)",
  "created_at": "datetime",
  "started_at": "datetime (optional)",
  "completed_at": "datetime (optional)",
  "execution_time": "float (optional)"
}
```

### HealthResponse
```json
{
  "status": "healthy|degraded|unhealthy",
  "timestamp": "datetime",
  "version": "string",
  "services": {
    "database": "healthy|unhealthy",
    "redis": "healthy|unhealthy",
    "celery": "healthy|unhealthy"
  },
  "metrics": {
    "active_tasks": "integer",
    "completed_tasks": "integer",
    "failed_tasks": "integer"
  }
}
```

## 🔧 错误处理

### HTTP状态码
- **200 OK**: 请求成功
- **202 Accepted**: 异步任务已接受
- **400 Bad Request**: 请求参数错误
- **404 Not Found**: 资源不存在
- **422 Unprocessable Entity**: 数据验证失败
- **500 Internal Server Error**: 服务器内部错误

### 错误响应格式
```json
{
  "error": "error_code",
  "message": "错误描述",
  "details": {
    "field": "具体错误信息"
  },
  "timestamp": "2025-08-01T06:00:00Z"
}
```

## 📝 使用示例

### Python示例
```python
import requests

# 提交异步审计任务
response = requests.post(
    "http://localhost:8000/api/v1/audit/async",
    json={
        "task_name": "测试审计",
        "config": "audit_config: test"
    }
)
task_data = response.json()
task_id = task_data["task_id"]

# 查询任务状态
status_response = requests.get(
    f"http://localhost:8000/api/v1/audit/status/{task_id}"
)
status_data = status_response.json()
print(f"任务状态: {status_data['status']}")
```

### cURL示例
```bash
# 健康检查
curl -X GET http://localhost:8000/api/v1/health

# 提交异步任务
curl -X POST http://localhost:8000/api/v1/audit/async \
  -H "Content-Type: application/json" \
  -d '{
    "task_name": "测试审计",
    "config": "audit_config: test"
  }'

# 查询任务状态
curl -X GET http://localhost:8000/api/v1/audit/status/task_123456789
```

### JavaScript示例
```javascript
// 提交异步审计任务
const response = await fetch('http://localhost:8000/api/v1/audit/async', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    task_name: '测试审计',
    config: 'audit_config: test'
  })
});

const taskData = await response.json();
const taskId = taskData.task_id;

// 查询任务状态
const statusResponse = await fetch(
  `http://localhost:8000/api/v1/audit/status/${taskId}`
);
const statusData = await statusResponse.json();
console.log('任务状态:', statusData.status);
```

## 🔍 API测试

### 使用Swagger UI
访问 http://localhost:8000/docs 使用交互式API文档进行测试。

### 使用Postman
导入OpenAPI规范文件：http://localhost:8000/openapi.json

### 自动化测试
参考 [测试文档](../testing/) 了解如何运行API测试套件。

## 📚 相关文档

- [开发指南](../development/setup.md) - 本地开发环境搭建
- [部署指南](../deployment/) - 生产环境部署
- [监控指南](../monitoring/) - API监控和日志分析
- [测试指南](../testing/) - API测试最佳实践

---

如有疑问，请参考 [主文档](../README.md) 或联系开发团队。
