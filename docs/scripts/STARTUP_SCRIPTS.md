# 启动脚本使用指南

本文档介绍项目的启动和停止脚本的使用方法。

## 📁 脚本文件

- `start.sh` - 统一启动脚本
- `stop.sh` - 统一停止脚本

## 🚀 启动脚本 (start.sh)

### 基本用法

```bash
# 默认启动（Docker开发环境）
./start.sh

# 显示帮助信息
./start.sh --help
```

### ✅ 已验证的启动模式

所有启动模式都已经过测试验证，确保参数解析正确：

- ✅ `docker` - Docker模式启动
- ✅ `local` - 本地模式启动
- ✅ `dev` - 开发模式启动
- ✅ `test` - 测试模式启动
- ✅ 环境参数 (`-e dev|test|prod`)
- ✅ 选项参数 (`--quiet`, `--no-check`, `--with-elk`)
- ✅ 参数组合和错误处理

### 启动模式

#### 1. Docker模式（推荐）

```bash
# Docker开发环境启动
./start.sh docker

# Docker测试环境启动
./start.sh docker -e test

# Docker生产环境启动
./start.sh docker -e prod
```

**特点**：
- 使用新的Docker部署体系
- 自动环境检查和配置
- 支持多环境部署
- 包含完整的服务栈

#### 2. 本地模式

```bash
# 本地环境启动
./start.sh local
```

**特点**：
- 在本地环境运行Python应用
- 使用Docker运行外部服务（MySQL、Redis）
- 适合本地开发和调试
- 自动安装Python依赖

#### 3. 开发模式

```bash
# 开发模式启动
./start.sh dev
```

**特点**：
- 优先使用Docker开发环境
- 如果Docker不可用，回退到本地模式
- 包含开发工具和调试功能

### 选项参数

```bash
# 指定环境（仅Docker模式）
./start.sh docker -e prod

# 静默模式（减少输出）
./start.sh --quiet

# 跳过环境检查
./start.sh --no-check
```

### 启动流程

1. **环境检查**：检查Python、Docker等必需软件
2. **目录创建**：创建logs、temp、uploads等必要目录
3. **服务启动**：根据模式启动相应服务
4. **健康检查**：验证服务是否正常启动
5. **信息显示**：显示服务地址和管理命令

## 🛑 停止脚本 (stop.sh)

### 基本用法

```bash
# 停止所有服务
./stop.sh

# 显示帮助信息
./stop.sh --help
```

### 停止模式

#### 1. 全部停止（默认）

```bash
# 停止所有服务
./stop.sh all
```

**功能**：
- 停止本地Python进程
- 停止Docker服务
- 检查服务状态

#### 2. 仅停止Docker服务

```bash
# 仅停止Docker服务
./stop.sh docker

# 停止指定环境的Docker服务
./stop.sh docker -e prod
```

#### 3. 仅停止本地服务

```bash
# 仅停止本地进程
./stop.sh local
```

#### 4. 清理模式

```bash
# 停止服务并清理资源
./stop.sh clean

# 强制清理所有资源
./stop.sh clean --force
```

**功能**：
- 停止所有服务
- 清理Docker资源
- 清理临时文件
- 清理旧日志文件（强制模式）

### 选项参数

```bash
# 强制停止
./stop.sh --force

# 静默模式
./stop.sh --quiet

# 指定环境
./stop.sh docker -e prod
```

## 🔧 使用场景

### 开发环境

```bash
# 启动开发环境
./start.sh dev

# 开发完成后停止
./stop.sh
```

### 测试环境

```bash
# 启动测试环境
./start.sh docker -e test

# 测试完成后停止
./stop.sh docker -e test
```

### 生产环境

```bash
# 启动生产环境
./start.sh docker -e prod

# 停止生产环境
./stop.sh docker -e prod

# 清理资源
./stop.sh clean
```

### 本地调试

```bash
# 本地模式启动
./start.sh local

# 停止本地服务
./stop.sh local
```

## 📊 服务地址

启动成功后，可以访问以下地址：

### 开发环境
- **API服务**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/v1/health
- **Flower监控**: http://localhost:5555
- **监控指标**: http://localhost:8001/metrics

### 测试环境
- **API服务**: http://localhost:8002
- **健康检查**: http://localhost:8002/api/v1/health

### 生产环境
- **API服务**: http://localhost:80
- **健康检查**: http://localhost:80/api/v1/health

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8000
   
   # 强制停止服务
   ./stop.sh --force
   ```

2. **Docker服务启动失败**
   ```bash
   # 检查Docker状态
   docker ps
   
   # 查看日志
   docker logs container_name
   
   # 重新启动
   ./stop.sh docker
   ./start.sh docker
   ```

3. **环境检查失败**
   ```bash
   # 跳过环境检查
   ./start.sh --no-check
   
   # 手动安装依赖
   pip install -r requirements.txt
   ```

4. **服务无法访问**
   ```bash
   # 检查服务状态
   curl http://localhost:8000/api/v1/health
   
   # 查看应用日志
   tail -f logs/app.log
   ```

### 日志文件

- **应用日志**: `logs/app.log`
- **Celery日志**: `logs/celery.log`
- **Docker日志**: `docker logs container_name`

### 清理和重置

```bash
# 完全清理并重新启动
./stop.sh clean --force
./start.sh docker
```

## 🔄 脚本迁移说明

### 旧脚本对比

| 旧脚本 | 新脚本 | 说明 |
|--------|--------|------|
| `start_now.sh` | `./start.sh` | 统一启动脚本 |
| `start_simple.sh` | `./start.sh local` | 本地模式启动 |
| `stop_simple.sh` | `./stop.sh` | 统一停止脚本 |
| `quick_start.py` | `./start.sh docker` | Docker模式启动 |

### 迁移建议

1. **删除旧脚本**：旧的启动脚本已被删除
2. **使用新脚本**：使用新的`start.sh`和`stop.sh`
3. **更新文档**：更新相关文档和CI/CD配置

## 📚 相关文档

- [Docker部署指南](docker/README.md)
- [项目主文档](README.md)
- [Docker部署体系](DOCKER_DEPLOYMENT.md)

## 💡 最佳实践

1. **开发环境**：使用`./start.sh dev`
2. **生产部署**：使用`./start.sh docker -e prod`
3. **定期清理**：使用`./stop.sh clean`清理资源
4. **监控服务**：定期检查服务健康状态
5. **日志管理**：定期查看和清理日志文件

---

通过这套启动脚本，您可以轻松管理审计服务的启动和停止，支持多种部署模式和环境配置。
