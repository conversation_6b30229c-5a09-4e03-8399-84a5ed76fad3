# 🚀 审计服务脚本使用指南

## 📋 脚本概览

| 脚本 | 功能 | 主要用途 |
|------|------|----------|
| **start.sh** | 启动服务 | 启动审计服务（支持ELK集成） |
| **stop.sh** | 停止服务 | 停止服务并可选择清理资源 |
| **status.sh** | 状态检查 | 检查所有服务运行状态 |

## 🎯 快速开始

### ⚡ 一键启动（推荐）
```bash
# 启动所有服务（包含ELK日志分析系统）
./start.sh docker --with-elk
```

### 📊 检查状态
```bash
# 检查所有服务状态
./status.sh --elk
```

### 🛑 停止服务
```bash
# 停止所有服务
./stop.sh all
```

## 📚 详细使用说明

### 🚀 start.sh - 启动脚本

#### 基本语法
```bash
./start.sh [选项] [模式]
```

#### 模式选项
- **docker** - 使用Docker启动（推荐）
- **local** - 本地启动（需要本地环境）
- **dev** - 开发模式启动
- **test** - 测试模式启动

#### 启动选项
- **-e, --env** - 指定环境 (dev|test|prod)
- **-q, --quiet** - 静默模式
- **--no-check** - 跳过环境检查
- **--with-elk** - 同时启动ELK日志系统

#### 使用示例
```bash
# 基础启动
./start.sh                      # 默认Docker开发环境
./start.sh docker               # Docker开发环境
./start.sh docker -e prod       # Docker生产环境

# 包含ELK日志系统
./start.sh docker --with-elk    # 启动服务+ELK
./start.sh docker -e prod --with-elk  # 生产环境+ELK

# 其他模式
./start.sh local                # 本地环境启动
./start.sh dev                  # 开发模式启动
```

### 🛑 stop.sh - 停止脚本

#### 基本语法
```bash
./stop.sh [选项] [模式]
```

#### 模式选项
- **all** - 停止所有服务（默认）
- **docker** - 仅停止Docker服务
- **local** - 仅停止本地服务
- **elk** - 仅停止ELK日志系统
- **clean** - 停止服务并清理资源

#### 停止选项
- **-e, --env** - 指定环境 (dev|test|prod)
- **-f, --force** - 强制停止
- **-q, --quiet** - 静默模式
- **--rm-containers** - 停止后删除容器
- **--rm-images** - 停止后删除镜像
- **--rm-volumes** - 停止后删除数据卷
- **--rm-all** - 停止后删除所有资源

#### 使用示例
```bash
# 基础停止
./stop.sh                       # 停止所有服务
./stop.sh docker                # 仅停止Docker服务
./stop.sh elk                   # 仅停止ELK服务

# 清理资源
./stop.sh docker --rm-containers  # 停止并删除容器
./stop.sh all --rm-volumes        # 停止并删除数据卷
./stop.sh all --rm-all             # 停止并删除所有资源

# 特定环境
./stop.sh docker -e prod        # 停止生产环境
./stop.sh clean                 # 停止并清理
```

### 📊 status.sh - 状态检查脚本

#### 基本语法
```bash
./status.sh [选项]
```

#### 检查选项
- **-v, --verbose** - 详细输出
- **-j, --json** - JSON格式输出
- **--elk** - 检查ELK服务状态

#### 使用示例
```bash
# 基础检查
./status.sh                     # 检查基础服务
./status.sh --elk               # 检查所有服务（包含ELK）

# 特殊格式
./status.sh --verbose           # 详细输出
./status.sh --json              # JSON格式输出
./status.sh --elk --json        # ELK状态JSON输出
```

## 🎯 常用操作场景

### 🔄 日常开发
```bash
# 启动开发环境
./start.sh docker --with-elk

# 检查状态
./status.sh --elk

# 停止服务
./stop.sh all
```

### 🧹 完全清理
```bash
# 停止所有服务并删除所有资源
./stop.sh all --rm-all

# 重新启动
./start.sh docker --with-elk
```

### 🔧 故障排除
```bash
# 检查详细状态
./status.sh --elk --verbose

# 强制停止
./stop.sh all --force

# 清理并重启
./stop.sh clean
./start.sh docker --with-elk
```

### 🚀 生产部署
```bash
# 生产环境启动
./start.sh docker -e prod --with-elk

# 检查生产状态
./status.sh --elk

# 生产环境停止
./stop.sh docker -e prod
```

## 🌐 服务访问地址

启动成功后，可以访问以下地址：

| 服务 | 地址 | 说明 |
|------|------|------|
| **🎯 Kibana仪表盘** | **http://localhost:5601/app/dashboards** | **日志分析仪表盘** |
| **🔍 日志搜索** | **http://localhost:5601/app/discover** | **日志搜索界面** |
| 🌐 API服务 | http://localhost:8000 | 审计API接口 |
| 📚 API文档 | http://localhost:8000/docs | 交互式API文档 |
| ❤️ 健康检查 | http://localhost:8000/api/v1/health | 服务健康状态 |
| 🌸 Flower监控 | http://localhost:5555 | Celery任务监控 |

## 🔍 日志查看

### Docker日志
```bash
# 查看API服务日志
docker logs audit_api_dev

# 查看Celery Worker日志
docker logs audit_celery_worker_dev

# 查看ELK服务日志
docker logs audit_elasticsearch
docker logs audit_kibana
docker logs audit_logstash
```

### 应用日志
```bash
# 查看应用日志文件
tail -f logs/app.log

# 查看Celery日志文件
tail -f logs/celery.log
```

## ⚠️ 注意事项

### 端口占用
确保以下端口未被占用：
- **3307** - MySQL
- **6380** - Redis
- **8000** - API服务
- **5555** - Flower
- **9200** - Elasticsearch
- **5601** - Kibana
- **5044** - Logstash
- **9600** - Logstash API

### 资源要求
- **内存**: 建议至少8GB（包含ELK时）
- **磁盘**: 建议至少10GB可用空间
- **Docker**: 需要Docker和Docker Compose

### 数据持久化
- 使用 `--rm-volumes` 会删除所有数据
- 生产环境请谨慎使用清理选项
- 重要数据请提前备份

## 🆘 故障排除

### 服务启动失败
```bash
# 检查端口占用
lsof -i :8000

# 强制清理并重启
./stop.sh all --rm-all
./start.sh docker --with-elk
```

### ELK服务问题
```bash
# 单独重启ELK
./stop.sh elk
./docker/scripts/elk-manage.sh start

# 检查ELK状态
./status.sh --elk
```

### 容器问题
```bash
# 查看所有容器
docker ps -a

# 清理异常容器
./stop.sh docker --rm-containers
```

---

通过这些脚本，您可以轻松管理整个审计服务系统，包括完整的ELK日志分析功能！🎉
