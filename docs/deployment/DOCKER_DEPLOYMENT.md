# Docker镜像构建与部署体系

本文档总结了为审计服务项目完善的Docker镜像构建与部署体系。

## 📁 完整的目录结构

```
audit_service_standalone/
├── docker/                          # Docker配置目录
│   ├── README.md                    # Docker部署详细指南
│   ├── quick-start.sh              # 快速启动脚本
│   │
│   ├── dockerfiles/                # Dockerfile文件
│   │   ├── Dockerfile.dev          # 开发环境（包含开发工具）
│   │   ├── Dockerfile.prod         # 生产环境（多阶段构建优化）
│   │   └── Dockerfile.celery       # Celery工作进程专用
│   │
│   ├── compose/                    # Docker Compose配置
│   │   ├── docker-compose.dev.yml  # 开发环境配置
│   │   ├── docker-compose.test.yml # 测试环境配置
│   │   ├── docker-compose.prod.yml # 生产环境配置
│   │   └── docker-compose.elk.yml  # ELK日志系统配置
│   │
│   ├── scripts/                    # 管理脚本
│   │   ├── build.sh               # 镜像构建脚本
│   │   ├── deploy.sh              # 部署管理脚本
│   │   ├── health-check.sh        # 健康检查脚本
│   │   ├── manage.sh              # 环境管理脚本
│   │   └── elk-manage.sh          # ELK日志系统管理脚本
│   │
│   ├── configs/                    # 配置文件
│   │   ├── env/                   # 环境变量配置
│   │   │   ├── .env.dev           # 开发环境变量
│   │   │   ├── .env.test          # 测试环境变量
│   │   │   └── .env.prod.template # 生产环境模板
│   │   ├── mysql/                 # MySQL配置
│   │   │   └── my.cnf             # MySQL生产配置
│   │   └── redis/                 # Redis配置
│   │       └── redis.conf         # Redis生产配置
│   │
│   ├── nginx/                      # Nginx反向代理配置
│   │   ├── nginx.conf             # 主配置文件
│   │   └── conf.d/                # 站点配置
│   │       └── audit-service.conf # 审计服务配置
│   │
│   └── secrets/                    # 密钥文件模板
│       ├── mysql_root_password.txt.template
│       ├── mysql_password.txt.template
│       └── secret_key.txt.template
│
├── .dockerignore                   # Docker构建忽略文件
├── DOCKER_DEPLOYMENT.md           # 本文档
└── README.md                      # 更新的项目文档
```

## 🚀 核心特性

### 1. 多环境支持
- **开发环境**：包含开发工具，支持代码热重载
- **测试环境**：自动化测试运行环境
- **生产环境**：优化的生产部署配置

### 2. 镜像优化
- **多阶段构建**：减少生产镜像大小
- **分层缓存**：优化构建速度
- **安全配置**：非root用户运行

### 3. 服务管理
- **健康检查**：自动监控服务状态
- **负载均衡**：Nginx反向代理
- **资源限制**：CPU和内存限制

### 4. 数据持久化
- **数据卷管理**：持久化数据库和文件
- **备份恢复**：自动化数据备份
- **配置管理**：环境变量和密钥管理

## 🛠️ 使用方式

### 快速启动

```bash
# 开发环境
./docker/quick-start.sh dev

# 测试环境
./docker/quick-start.sh test

# 生产环境
./docker/quick-start.sh prod
```

### 镜像构建

```bash
# 构建开发环境镜像
./docker/scripts/build.sh dev

# 构建生产环境镜像
./docker/scripts/build.sh prod

# 构建所有镜像
./docker/scripts/build.sh all

# 并行构建并推送
./docker/scripts/build.sh all --parallel --push -r myregistry.com/
```

### 服务部署

```bash
# 启动服务
./docker/scripts/deploy.sh prod up -d

# 扩缩容
./docker/scripts/deploy.sh prod scale --scale api=3

# 查看状态
./docker/scripts/deploy.sh prod status

# 查看日志
./docker/scripts/deploy.sh prod logs api
```

### 环境管理

```bash
# 初始化环境
./docker/scripts/manage.sh init -e prod

# 设置生产密钥
./docker/scripts/manage.sh setup-secrets

# 备份数据
./docker/scripts/manage.sh backup -e prod

# 监控服务
./docker/scripts/manage.sh monitor -e prod
```

### ELK日志系统管理

```bash
# 启动ELK服务
./docker/scripts/elk-manage.sh start

# 查看ELK状态
./docker/scripts/elk-manage.sh status

# 健康检查
./docker/scripts/elk-manage.sh health

# 停止ELK服务
./docker/scripts/elk-manage.sh stop

# 与主服务集成启动
./start.sh docker --with-elk
```

## 🔧 配置说明

### 环境变量配置

每个环境都有独立的配置文件：
- 开发环境：调试模式，详细日志
- 测试环境：测试数据库，模拟服务
- 生产环境：性能优化，安全配置

### 服务配置

- **MySQL**：性能优化，字符集配置，日志管理
- **Redis**：内存管理，持久化配置，性能调优
- **Nginx**：反向代理，负载均衡，SSL支持

### 安全配置

- **密钥管理**：Docker secrets管理敏感信息
- **网络隔离**：独立的Docker网络
- **用户权限**：非root用户运行容器

## 📊 监控和日志

### 健康检查

```bash
# 检查所有服务
./docker/scripts/health-check.sh prod

# 详细检查
./docker/scripts/health-check.sh prod --verbose

# 等待服务就绪
./docker/scripts/health-check.sh prod --wait 300
```

### 监控指标

- **Prometheus指标**：http://localhost:8001/metrics
- **容器状态**：docker stats
- **服务日志**：集中化日志管理

### ELK集成

支持Elasticsearch、Logstash、Kibana日志系统集成。

## 🔄 CI/CD集成

### 构建流水线

```yaml
# 示例GitHub Actions配置
- name: Build Docker Images
  run: ./docker/scripts/build.sh all --no-cache

- name: Run Tests
  run: ./docker/scripts/deploy.sh test up test_runner

- name: Deploy to Production
  run: ./docker/scripts/deploy.sh prod up -d
```

### 部署策略

- **蓝绿部署**：零停机部署
- **滚动更新**：逐步更新服务
- **回滚机制**：快速回滚到上一版本

## 🚨 故障排除

### 常见问题

1. **端口冲突**：检查端口占用，修改配置
2. **资源不足**：调整资源限制
3. **网络问题**：检查Docker网络配置
4. **权限问题**：检查文件权限和用户配置

### 调试工具

```bash
# 进入容器调试
docker exec -it container_name bash

# 查看容器日志
docker logs container_name

# 检查网络连接
docker network ls
docker network inspect network_name
```

## 📈 性能优化

### 镜像优化

- 使用Alpine Linux基础镜像
- 多阶段构建减少镜像大小
- 优化.dockerignore文件

### 运行时优化

- 合理设置资源限制
- 使用健康检查
- 配置日志轮转

### 数据库优化

- 调整MySQL配置参数
- 使用连接池
- 定期维护和备份

## 🔐 安全最佳实践

### 容器安全

- 使用非root用户
- 最小权限原则
- 定期更新基础镜像

### 网络安全

- 网络隔离
- 防火墙配置
- SSL/TLS加密

### 数据安全

- 密钥管理
- 数据加密
- 访问控制

## 📚 相关文档

- [Docker部署详细指南](docker/README.md)
- [项目主文档](README.md)
- [API文档](http://localhost:8000/docs)

## 🎯 总结

这套Docker部署体系提供了：

✅ **完整的多环境支持**：开发、测试、生产环境配置
✅ **优化的镜像构建**：多阶段构建，缓存优化
✅ **自动化脚本**：一键部署，健康检查，环境管理
✅ **生产级配置**：负载均衡，监控，备份恢复
✅ **安全配置**：密钥管理，网络隔离，权限控制
✅ **可扩展架构**：支持水平扩展和负载均衡
✅ **完善的文档**：详细的使用指南和故障排除

通过这套体系，可以实现：
- 开发环境的快速搭建和调试
- 测试环境的自动化测试
- 生产环境的稳定部署和运维
- 统一的配置管理和版本控制
- 高效的CI/CD流水线集成
