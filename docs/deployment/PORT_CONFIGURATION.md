# 端口配置说明

## 📋 概述

为了避免端口冲突，不同环境使用不同的端口配置。本文档说明各环境的端口分配。

## 🔌 端口分配表

### 主要服务端口

| 服务 | 开发环境 | 测试环境 | 生产环境 | 说明 |
|------|----------|----------|----------|------|
| **API服务** | 8000 | 8002 | 80/443 | FastAPI应用端口 |
| **MySQL** | 3307 | 3308 | 3306 | 数据库端口 |
| **Redis** | 6380 | 6380 | 6381 | 缓存服务端口 |
| **Flower** | 5555 | - | - | Celery监控端口 |
| **Nginx** | - | - | 80/443 | 反向代理端口 |

### ELK日志系统端口

| 服务 | 端口 | 说明 |
|------|------|------|
| **Elasticsearch** | 9200 | 搜索引擎端口 |
| **Logstash** | 5044, 9600 | 日志处理端口 |
| **Kibana** | 5601 | 日志可视化端口 |

## 🏗️ 环境配置详情

### 开发环境 (dev)

```yaml
services:
  api:
    ports:
      - "8000:8000"
  
  mysql:
    ports:
      - "3307:3306"
  
  redis:
    ports:
      - "6380:6379"
  
  flower:
    ports:
      - "5555:5555"
```

**访问地址**:
- API服务: http://localhost:8000
- API文档: http://localhost:8000/docs
- 数据库: localhost:3307
- Redis: localhost:6380
- Flower监控: http://localhost:5555

### 测试环境 (test)

```yaml
services:
  api:
    ports:
      - "8002:8000"
  
  mysql:
    ports:
      - "3308:3306"
  
  redis:
    ports:
      - "6380:6379"
```

**访问地址**:
- API服务: http://localhost:8002
- API文档: http://localhost:8002/docs
- 数据库: localhost:3308
- Redis: localhost:6380

### 生产环境 (prod)

```yaml
services:
  nginx:
    ports:
      - "80:80"
      - "443:443"
  
  mysql:
    ports:
      - "3306:3306"
  
  redis:
    ports:
      - "6381:6379"
```

**访问地址**:
- API服务: http://your-domain.com
- HTTPS: https://your-domain.com
- 数据库: localhost:3306 (内部访问)
- Redis: localhost:6381

## 🚨 端口冲突解决

### 常见冲突情况

1. **Redis端口6379被占用**
   ```bash
   # 检查占用进程
   lsof -i :6379
   
   # 停止占用的服务
   docker stop <container_name>
   ```

2. **MySQL端口3306被占用**
   ```bash
   # 检查占用进程
   lsof -i :3306
   
   # 停止本地MySQL服务
   brew services stop mysql  # macOS
   sudo systemctl stop mysql  # Linux
   ```

3. **API端口8000被占用**
   ```bash
   # 检查占用进程
   lsof -i :8000
   
   # 杀死占用进程
   kill -9 <PID>
   ```

### 解决步骤

1. **识别冲突**
   ```bash
   # 检查所有相关端口
   lsof -i :8000,3306,6379,5555
   ```

2. **停止冲突服务**
   ```bash
   # 停止Docker容器
   docker stop $(docker ps -q)
   
   # 停止本地服务
   brew services stop redis
   brew services stop mysql
   ```

3. **清理Docker资源**
   ```bash
   # 清理停止的容器
   docker container prune -f
   
   # 清理未使用的网络
   docker network prune -f
   ```

4. **重新启动服务**
   ```bash
   # 使用新的端口配置启动
   ./start.sh docker -e dev
   ```

## 🔧 自定义端口配置

### 修改端口的方法

1. **修改Docker Compose文件**
   ```yaml
   services:
     redis:
       ports:
         - "6382:6379"  # 使用自定义端口6382
   ```

2. **更新环境变量**
   ```bash
   # 在.env文件中更新
   REDIS_URL=redis://localhost:6382/0
   ```

3. **更新应用配置**
   ```python
   # 在配置文件中更新
   REDIS_URL = "redis://localhost:6382/0"
   ```

### 端口选择建议

- **避免使用常用端口**: 3306, 6379, 5432等
- **使用高位端口**: 8000+, 避免需要root权限
- **保持一致性**: 同类服务使用相邻端口
- **文档记录**: 及时更新端口配置文档

## 📝 配置检查清单

### 启动前检查

- [ ] 检查端口是否被占用
- [ ] 确认Docker Compose配置正确
- [ ] 验证环境变量设置
- [ ] 检查防火墙设置

### 启动后验证

- [ ] 服务健康检查通过
- [ ] 端口监听正常
- [ ] 服务间通信正常
- [ ] 外部访问正常

## 🛠️ 故障排除

### 端口仍然冲突

1. **重启Docker服务**
   ```bash
   # macOS
   osascript -e 'quit app "Docker"'
   open -a Docker
   
   # Linux
   sudo systemctl restart docker
   ```

2. **检查系统服务**
   ```bash
   # 检查系统级服务
   sudo netstat -tulpn | grep :6379
   ```

3. **使用不同端口范围**
   ```bash
   # 使用更高的端口号
   redis: "16379:6379"
   mysql: "13306:3306"
   ```

### 服务无法访问

1. **检查容器状态**
   ```bash
   docker ps
   docker logs <container_name>
   ```

2. **检查网络连接**
   ```bash
   docker network ls
   docker network inspect <network_name>
   ```

3. **验证端口映射**
   ```bash
   docker port <container_name>
   ```

---

通过合理的端口配置，可以避免服务冲突，确保多环境并存和服务的稳定运行。
