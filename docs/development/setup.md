# 🛠️ 开发环境搭建指南

本文档介绍如何搭建审计服务的本地开发环境。

## 📋 环境要求

### 🐍 Python环境
- **Python版本**: 3.9+
- **包管理器**: pip 或 poetry
- **虚拟环境**: venv 或 conda

### 🐳 容器环境
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

### 🗄️ 数据库环境
- **MySQL**: 8.0+
- **Redis**: 6.0+

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd audit_service_standalone
```

### 2. 创建虚拟环境
```bash
# 使用venv
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 使用conda
conda create -n audit-service python=3.9
conda activate audit-service
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 5. 启动开发环境
```bash
# 使用Docker（推荐）
./start.sh docker --with-elk

# 或本地启动
./start.sh local
```

## 🔧 开发工具配置

### IDE配置
推荐使用以下IDE和配置：

#### VS Code
```json
{
  "python.defaultInterpreterPath": "./venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "python.formatting.provider": "black",
  "python.sortImports.args": ["--profile", "black"]
}
```

#### PyCharm
- 配置Python解释器指向虚拟环境
- 启用代码格式化（Black）
- 配置导入排序（isort）

### 代码质量工具
```bash
# 安装开发工具
pip install black isort pylint pytest

# 代码格式化
black app/
isort app/

# 代码检查
pylint app/

# 运行测试
pytest tests/
```

## 📊 开发服务

### 启动核心服务
```bash
# 启动数据库和缓存
docker compose -f docker/compose/docker-compose.dev.yml up -d mysql redis

# 启动API服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动Celery Worker
celery -A app.tasks.celery_tasks worker --loglevel=info

# 启动Flower监控
celery -A app.tasks.celery_tasks flower --port=5555
```

### 启动ELK服务
```bash
# 启动ELK栈
docker compose -f docker/compose/docker-compose.elk.yml up -d

# 检查服务状态
./status.sh --elk
```

## 🧪 开发测试

### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_api.py

# 生成覆盖率报告
pytest --cov=app tests/
```

### API测试
```bash
# 健康检查
curl http://localhost:8000/api/v1/health

# 查看API文档
open http://localhost:8000/docs
```

## 🔍 调试技巧

### 日志调试
```python
from app.utils.audit_logger import get_audit_logger

logger = get_audit_logger(__name__)
logger.debug("调试信息", extra_data="debug_value")
```

### 断点调试
```python
import pdb; pdb.set_trace()  # Python调试器
# 或使用IDE断点
```

### 性能分析
```python
from app.utils.audit_logger import log_performance

@log_performance("operation_name")
def my_function():
    # 函数逻辑
    pass
```

## 📝 开发规范

### 代码风格
- 使用Black进行代码格式化
- 使用isort进行导入排序
- 遵循PEP 8编码规范
- 添加类型注解

### 提交规范
```bash
# 提交格式
git commit -m "feat: 添加新功能"
git commit -m "fix: 修复bug"
git commit -m "docs: 更新文档"
git commit -m "test: 添加测试"
```

### 分支管理
- `main`: 主分支，稳定版本
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 🚨 常见问题

### 端口冲突
```bash
# 检查端口占用
lsof -i :8000

# 修改端口配置
export API_PORT=8001
```

### 依赖问题
```bash
# 清理pip缓存
pip cache purge

# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

### 数据库连接问题
```bash
# 检查数据库状态
docker logs audit_mysql_dev

# 重置数据库
docker compose -f docker/compose/docker-compose.dev.yml down -v
docker compose -f docker/compose/docker-compose.dev.yml up -d mysql
```

## 📚 参考资源

- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Celery官方文档](https://docs.celeryproject.org/)
- [SQLAlchemy文档](https://docs.sqlalchemy.org/)
- [Docker官方文档](https://docs.docker.com/)

## 🤝 贡献指南

1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request
5. 等待代码审查

---

如有问题，请参考 [项目文档](../README.md) 或联系开发团队。
