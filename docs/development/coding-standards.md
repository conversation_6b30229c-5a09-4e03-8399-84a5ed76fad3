# 📝 编码规范和最佳实践

本文档定义了审计服务项目的编码规范和最佳实践，确保代码质量和团队协作效率。

## 🐍 Python编码规范

### 基础规范
- 遵循 [PEP 8](https://pep8.org/) 编码规范
- 使用 [Black](https://black.readthedocs.io/) 进行代码格式化
- 使用 [isort](https://pycqa.github.io/isort/) 进行导入排序
- 使用 [pylint](https://pylint.org/) 进行代码检查

### 代码格式化配置

#### pyproject.toml
```toml
[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
```

### 命名规范

#### 变量和函数
```python
# ✅ 好的命名
user_id = "12345"
task_name = "audit_task"
def process_audit_request():
    pass

# ❌ 避免的命名
uid = "12345"
tn = "audit_task"
def proc_req():
    pass
```

#### 类和常量
```python
# ✅ 类名使用PascalCase
class AuditProcessor:
    pass

class TaskService:
    pass

# ✅ 常量使用UPPER_CASE
MAX_RETRY_COUNT = 3
DEFAULT_TIMEOUT = 30
API_VERSION = "v1"
```

### 类型注解
```python
from typing import Dict, List, Optional, Union
from datetime import datetime

# ✅ 函数类型注解
def process_task(
    task_id: str,
    config: Dict[str, Any],
    timeout: Optional[int] = None
) -> Dict[str, Any]:
    """处理任务的函数"""
    pass

# ✅ 类属性类型注解
class TaskResult:
    task_id: str
    status: str
    created_at: datetime
    result: Optional[Dict[str, Any]] = None
```

## 📚 文档规范

### 函数文档字符串
```python
def create_audit_task(
    task_name: str,
    config: str,
    main_doc: Optional[str] = None
) -> str:
    """
    创建新的审计任务
    
    根据提供的配置创建审计任务，并返回任务ID。
    支持主文档的可选输入。
    
    Args:
        task_name: 任务名称，用于标识和显示
        config: YAML格式的审计配置字符串
        main_doc: 主文档JSON字符串（可选）
        
    Returns:
        str: 生成的唯一任务ID
        
    Raises:
        ValueError: 当配置格式无效时
        DatabaseError: 当数据库操作失败时
        
    Example:
        >>> task_id = create_audit_task(
        ...     "测试任务",
        ...     "config: test",
        ...     '{"content": "test"}'
        ... )
        >>> print(task_id)
        'task_123456'
    """
    pass
```

### 类文档字符串
```python
class AuditService:
    """
    审计服务核心类
    
    提供审计任务的创建、执行和管理功能。
    支持同步和异步两种处理模式。
    
    Attributes:
        config_service: 配置管理服务实例
        task_service: 任务管理服务实例
        
    Example:
        >>> service = AuditService()
        >>> result = service.process_sync("config", "doc")
        >>> print(result['status'])
        'completed'
    """
    
    def __init__(self):
        """初始化审计服务"""
        self.config_service = None
        self.task_service = None
```

## 🏗️ 架构规范

### 目录结构
```
app/
├── api/                    # API路由层
│   └── v1/                # API版本
├── core/                  # 核心配置
├── models/                # 数据模型
├── services/              # 业务服务层
├── tasks/                 # 异步任务
├── utils/                 # 工具函数
└── main.py               # 应用入口
```

### 分层架构
```python
# ✅ 正确的分层调用
# API层 -> 服务层 -> 数据层

# api/v1/audit.py
@router.post("/audit/async")
async def audit_async(request: AuditRequest, db: Session = Depends(get_db)):
    task_service = TaskService(db)  # 服务层
    return task_service.create_task(...)

# services/task_service.py
class TaskService:
    def create_task(self, ...):
        # 调用数据层
        task = RequestTracking(...)
        self.db.add(task)
        return task.task_id
```

### 依赖注入
```python
# ✅ 使用FastAPI依赖注入
from fastapi import Depends
from app.core.database import get_db

@router.post("/endpoint")
async def endpoint(
    request: RequestModel,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    pass
```

## 🔍 错误处理规范

### 异常处理
```python
# ✅ 具体的异常处理
try:
    result = process_data(data)
except ValueError as e:
    logger.error(f"数据格式错误: {e}")
    raise HTTPException(status_code=400, detail="数据格式无效")
except DatabaseError as e:
    logger.error(f"数据库操作失败: {e}")
    raise HTTPException(status_code=500, detail="数据库操作失败")
except Exception as e:
    logger.error(f"未知错误: {e}")
    raise HTTPException(status_code=500, detail="服务器内部错误")

# ❌ 避免捕获所有异常
try:
    result = process_data(data)
except:  # 不要这样做
    pass
```

### 自定义异常
```python
# ✅ 定义业务异常
class AuditServiceError(Exception):
    """审计服务基础异常"""
    pass

class ConfigurationError(AuditServiceError):
    """配置错误异常"""
    pass

class TaskNotFoundError(AuditServiceError):
    """任务不存在异常"""
    pass
```

## 📊 日志规范

### 日志记录
```python
from app.utils.audit_logger import get_audit_logger

logger = get_audit_logger(__name__)

# ✅ 结构化日志
logger.info("任务创建成功", 
           task_id=task_id,
           task_name=task_name,
           user_id=user_id)

logger.error("任务处理失败",
            task_id=task_id,
            error_type=type(e).__name__,
            error_message=str(e))

# ✅ 业务日志
logger.business_log("task", "create", "创建审计任务",
                   task_id=task_id,
                   config_size=len(config))

# ✅ 性能日志
with logger.operation_context("process_document"):
    result = process_document(doc)
```

### 日志级别使用
- **DEBUG**: 详细的调试信息，仅开发环境
- **INFO**: 一般信息，正常业务流程
- **WARNING**: 警告信息，需要关注但不影响功能
- **ERROR**: 错误信息，功能异常需要处理
- **CRITICAL**: 严重错误，系统级别问题

## 🧪 测试规范

### 单元测试
```python
import pytest
from unittest.mock import Mock, patch

class TestAuditService:
    """审计服务测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.service = AuditService()
    
    def test_create_task_success(self):
        """测试成功创建任务"""
        # Arrange
        task_name = "test_task"
        config = "test_config"
        
        # Act
        result = self.service.create_task(task_name, config)
        
        # Assert
        assert result is not None
        assert len(result) > 0
    
    @patch('app.services.audit_service.TaskService')
    def test_create_task_with_mock(self, mock_task_service):
        """测试使用Mock的任务创建"""
        # Arrange
        mock_task_service.return_value.create_task.return_value = "task_123"
        
        # Act
        result = self.service.create_task("test", "config")
        
        # Assert
        assert result == "task_123"
        mock_task_service.return_value.create_task.assert_called_once()
```

### 集成测试
```python
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

class TestAuditAPI:
    """审计API集成测试"""
    
    def test_health_check(self):
        """测试健康检查接口"""
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"
    
    def test_create_async_task(self):
        """测试创建异步任务"""
        payload = {
            "task_name": "test_task",
            "config": "test_config"
        }
        response = client.post("/api/v1/audit/async", json=payload)
        assert response.status_code == 202
        assert "task_id" in response.json()
```

## 🔒 安全规范

### 输入验证
```python
from pydantic import BaseModel, validator

class AuditRequest(BaseModel):
    task_name: str
    config: str
    
    @validator('task_name')
    def validate_task_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('任务名称不能为空')
        if len(v) > 100:
            raise ValueError('任务名称长度不能超过100字符')
        return v.strip()
```

### 敏感信息处理
```python
# ✅ 不记录敏感信息
logger.info("用户登录成功", user_id=user_id)  # 不记录密码

# ✅ 脱敏处理
def mask_sensitive_data(data: str) -> str:
    """脱敏处理敏感数据"""
    if len(data) <= 4:
        return "*" * len(data)
    return data[:2] + "*" * (len(data) - 4) + data[-2:]
```

## 📈 性能规范

### 数据库查询优化
```python
# ✅ 使用索引字段查询
task = db.query(RequestTracking).filter(
    RequestTracking.task_id == task_id
).first()

# ✅ 批量操作
tasks = db.query(RequestTracking).filter(
    RequestTracking.status.in_(['pending', 'processing'])
).all()

# ✅ 分页查询
tasks = db.query(RequestTracking).offset(skip).limit(limit).all()
```

### 缓存使用
```python
from app.core.cache import get_redis

# ✅ 缓存频繁查询的数据
def get_task_config(task_id: str) -> dict:
    redis = get_redis()
    cache_key = f"task_config:{task_id}"
    
    # 尝试从缓存获取
    cached_config = redis.get(cache_key)
    if cached_config:
        return json.loads(cached_config)
    
    # 从数据库查询
    config = db.query(TaskConfig).filter_by(task_id=task_id).first()
    
    # 存入缓存
    redis.setex(cache_key, 3600, json.dumps(config.to_dict()))
    return config.to_dict()
```

## 🔧 工具配置

### pre-commit配置
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
  - repo: https://github.com/pycqa/isort
    rev: 5.10.1
    hooks:
      - id: isort
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
```

### Makefile
```makefile
.PHONY: format lint test

format:
	black app/ tests/
	isort app/ tests/

lint:
	pylint app/
	flake8 app/ tests/

test:
	pytest tests/ -v --cov=app

install:
	pip install -r requirements.txt
	pre-commit install

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
```

---

遵循这些编码规范将确保代码质量、可维护性和团队协作效率。如有疑问，请参考相关文档或联系开发团队。
