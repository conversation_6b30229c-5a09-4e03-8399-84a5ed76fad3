# 🗄️ 数据库迁移指南 (Alembic)

## 🎯 什么是 Alembic

**Alembic** 是 SQLAlchemy 的数据库迁移工具，用于管理数据库结构的版本控制和变更。它允许开发者：

- 📝 **版本控制数据库结构**：跟踪数据库表结构的变化
- 🔄 **自动生成迁移脚本**：基于模型变化自动创建迁移文件
- ⬆️ **升级数据库**：将数据库升级到最新版本
- ⬇️ **回滚数据库**：回滚到之前的版本
- 🤝 **团队协作**：确保团队成员的数据库结构一致

## 📁 项目中的 Alembic 文件

### `alembic.ini` - 主配置文件
```ini
[alembic]
# 迁移脚本存放位置
script_location = migrations

# 数据库连接URL
sqlalchemy.url = mysql+pymysql://audit_user:audit_password@localhost:3306/audit_db

# 版本号格式
version_num_format = %04d

# 日志配置
[loggers]
keys = root,sqlalchemy,alembic
```

### `migrations/` - 迁移文件目录
```
migrations/
├── env.py              # Alembic环境配置
├── script.py.mako      # 迁移脚本模板
└── versions/           # 迁移版本文件（自动生成）
    ├── 0001_initial.py
    ├── 0002_add_user_table.py
    └── ...
```

### `migrations/env.py` - 环境配置
```python
# 导入数据库模型
from app.core.database import Base
from app.models.database import RequestTracking

# 设置元数据，用于自动生成迁移
target_metadata = Base.metadata

def get_database_url():
    """从环境变量或配置文件获取数据库URL"""
    return os.getenv("DATABASE_URL") or config.get_main_option("sqlalchemy.url")
```

## 🚀 Alembic 基本使用

### 1. 初始化 Alembic（已完成）
```bash
# 项目中已经初始化，无需重复执行
alembic init migrations
```

### 2. 生成初始迁移
```bash
# 基于当前模型生成初始迁移文件
alembic revision --autogenerate -m "Initial migration"
```

### 3. 执行迁移
```bash
# 升级到最新版本
alembic upgrade head

# 升级到特定版本
alembic upgrade 0001

# 回滚到上一个版本
alembic downgrade -1

# 回滚到特定版本
alembic downgrade 0001
```

### 4. 查看迁移状态
```bash
# 查看当前版本
alembic current

# 查看迁移历史
alembic history

# 查看待执行的迁移
alembic show head
```

## 🔧 常用操作场景

### 📝 添加新表
1. **修改模型文件**
```python
# app/models/database.py
class NewTable(Base):
    __tablename__ = "new_table"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
```

2. **生成迁移文件**
```bash
alembic revision --autogenerate -m "Add new_table"
```

3. **执行迁移**
```bash
alembic upgrade head
```

### 🔄 修改表结构
1. **修改模型**
```python
# 添加新字段
class RequestTracking(Base):
    # ... 现有字段
    new_field = Column(String(100), nullable=True)  # 新增字段
```

2. **生成迁移**
```bash
alembic revision --autogenerate -m "Add new_field to request_tracking"
```

3. **检查生成的迁移文件**
```python
# migrations/versions/xxxx_add_new_field.py
def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('request_tracking', sa.Column('new_field', sa.String(length=100), nullable=True))
    # ### end Alembic commands ###

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('request_tracking', 'new_field')
    # ### end Alembic commands ###
```

4. **执行迁移**
```bash
alembic upgrade head
```

### 🗑️ 删除字段
1. **从模型中删除字段**
2. **生成迁移**
```bash
alembic revision --autogenerate -m "Remove old_field from request_tracking"
```
3. **执行迁移**
```bash
alembic upgrade head
```

## 🐳 Docker 环境中的使用

### 在容器中执行迁移
```bash
# 进入API容器
docker exec -it audit_api_dev bash

# 执行迁移
alembic upgrade head

# 或者直接执行
docker exec audit_api_dev alembic upgrade head
```

### 启动时自动迁移
在 `app/main.py` 的启动过程中：
```python
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行数据库迁移
    try:
        import subprocess
        result = subprocess.run(["alembic", "upgrade", "head"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("数据库迁移完成")
        else:
            logger.error(f"数据库迁移失败: {result.stderr}")
    except Exception as e:
        logger.error(f"执行数据库迁移时出错: {e}")
    
    yield
```

## ⚙️ 配置管理

### 环境变量配置
```bash
# .env 文件
DATABASE_URL=mysql+pymysql://audit_user:audit_password@localhost:3306/audit_db

# 不同环境使用不同的数据库
DATABASE_URL_DEV=mysql+pymysql://audit_user:audit_password@localhost:3306/audit_db_dev
DATABASE_URL_TEST=mysql+pymysql://audit_user:audit_password@localhost:3306/audit_db_test
DATABASE_URL_PROD=mysql+pymysql://audit_user:audit_password@prod-host:3306/audit_db_prod
```

### 动态配置
```python
# migrations/env.py
def get_database_url():
    """根据环境获取数据库URL"""
    env = os.getenv("ENVIRONMENT", "development")
    
    if env == "production":
        return os.getenv("DATABASE_URL_PROD")
    elif env == "test":
        return os.getenv("DATABASE_URL_TEST")
    else:
        return os.getenv("DATABASE_URL_DEV") or config.get_main_option("sqlalchemy.url")
```

## 🔍 迁移文件详解

### 迁移文件结构
```python
"""Add user_id field

Revision ID: 0002
Revises: 0001
Create Date: 2025-08-01 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = '0002'
down_revision = '0001'
branch_labels = None
depends_on = None

def upgrade() -> None:
    """升级操作"""
    op.add_column('request_tracking', 
                  sa.Column('user_id', sa.String(length=255), nullable=True))

def downgrade() -> None:
    """回滚操作"""
    op.drop_column('request_tracking', 'user_id')
```

### 常用操作命令
```python
# 添加列
op.add_column('table_name', sa.Column('column_name', sa.String(255)))

# 删除列
op.drop_column('table_name', 'column_name')

# 修改列
op.alter_column('table_name', 'column_name', 
                type_=sa.String(500),  # 新类型
                nullable=False)        # 新约束

# 创建表
op.create_table('new_table',
    sa.Column('id', sa.Integer, primary_key=True),
    sa.Column('name', sa.String(255), nullable=False)
)

# 删除表
op.drop_table('table_name')

# 创建索引
op.create_index('idx_table_column', 'table_name', ['column_name'])

# 删除索引
op.drop_index('idx_table_column', 'table_name')
```

## 🚨 最佳实践和注意事项

### ✅ 最佳实践
1. **备份数据库**：生产环境迁移前务必备份
2. **测试迁移**：在测试环境先验证迁移脚本
3. **检查生成的迁移**：自动生成的迁移可能需要手动调整
4. **描述性消息**：使用清晰的迁移消息描述变更
5. **小步迁移**：避免一次性大量变更

### ⚠️ 注意事项
1. **数据丢失风险**：删除列或表会导致数据丢失
2. **生产环境谨慎**：生产环境迁移需要特别小心
3. **并发问题**：多人开发时注意迁移冲突
4. **回滚准备**：确保回滚脚本正确可用

### 🔧 故障排除
```bash
# 迁移冲突解决
alembic merge -m "Merge migrations" head1 head2

# 强制设置版本（谨慎使用）
alembic stamp head

# 查看SQL而不执行
alembic upgrade head --sql

# 离线模式生成SQL
alembic upgrade head --sql > migration.sql
```

## 📚 相关命令参考

### 基础命令
```bash
# 查看帮助
alembic --help

# 查看当前版本
alembic current

# 查看历史
alembic history --verbose

# 生成迁移
alembic revision --autogenerate -m "描述信息"

# 执行迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1
```

### 高级命令
```bash
# 创建空迁移文件
alembic revision -m "Custom migration"

# 合并分支
alembic merge -m "Merge branches" head1 head2

# 显示差异
alembic show head

# 生成SQL脚本
alembic upgrade head --sql > upgrade.sql
```

---

通过 Alembic，我们可以安全、可控地管理数据库结构的变更，确保开发、测试和生产环境的数据库结构保持一致。这是现代应用开发中不可或缺的重要工具。

📧 如有疑问，请参考 [开发文档](setup.md) 或联系开发团队。
