# 🗄️ Alembic.ini 文件说明

## 🎯 什么是 alembic.ini

`alembic.ini` 是 **Alembic 数据库迁移工具的主配置文件**，它定义了数据库迁移的各种设置和参数。

## 📋 主要作用

### 1. **数据库连接配置**
```ini
[alembic]
# 数据库连接URL
sqlalchemy.url = mysql+pymysql://audit_user:audit_password@localhost:3307/audit_db
```
- 指定数据库类型、用户名、密码、主机和数据库名
- 支持多种数据库：MySQL、PostgreSQL、SQLite等

### 2. **迁移脚本位置**
```ini
# 迁移脚本存放目录
script_location = migrations
```
- 定义迁移文件的存储位置
- 项目中的 `migrations/` 目录

### 3. **版本管理配置**
```ini
# 版本号格式
version_num_format = %04d
# 版本表名
version_table = alembic_version
```
- 控制迁移版本号的格式
- 指定版本跟踪表的名称

### 4. **日志配置**
```ini
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic
```
- 配置迁移过程中的日志输出
- 控制SQL语句的显示级别

## 🏗️ 项目中的使用场景

### 📊 **数据库结构管理**
当你需要：
- 创建新的数据库表
- 修改现有表结构（添加/删除字段）
- 创建或删除索引
- 修改字段类型或约束

### 🔄 **版本控制**
- **向前迁移**：升级数据库到最新版本
- **向后回滚**：回退到之前的版本
- **分支合并**：处理多人开发的迁移冲突

### 🤝 **团队协作**
- 确保所有开发者的数据库结构一致
- 自动化部署时的数据库更新
- 生产环境的安全数据库升级

## 🚀 基本使用流程

### 1. **修改数据模型**
```python
# app/models/database.py
class RequestTracking(Base):
    __tablename__ = "request_tracking"
    
    # 添加新字段
    new_field = Column(String(255), nullable=True)
```

### 2. **生成迁移文件**
```bash
# 自动检测模型变化并生成迁移
alembic revision --autogenerate -m "Add new_field to request_tracking"
```

### 3. **执行迁移**
```bash
# 升级到最新版本
alembic upgrade head

# 或回滚到上一版本
alembic downgrade -1
```

## 📁 相关文件结构

```
项目根目录/
├── alembic.ini                 # 主配置文件
├── migrations/                 # 迁移目录
│   ├── env.py                 # 环境配置
│   ├── script.py.mako         # 迁移脚本模板
│   └── versions/              # 版本文件目录
│       ├── 001_initial.py     # 初始迁移
│       ├── 002_add_field.py   # 添加字段迁移
│       └── ...
└── app/
    └── models/
        └── database.py         # 数据模型定义
```

## ⚙️ 配置详解

### 数据库URL格式
```ini
# MySQL
sqlalchemy.url = mysql+pymysql://user:password@host:port/database

# PostgreSQL  
sqlalchemy.url = postgresql://user:password@host:port/database

# SQLite
sqlalchemy.url = sqlite:///path/to/database.db
```

### 环境变量支持
```ini
# 可以使用环境变量
sqlalchemy.url = ${DATABASE_URL}
```

在代码中动态设置：
```python
# migrations/env.py
import os
from app.core.config import settings

def get_url():
    return os.getenv("DATABASE_URL") or settings.database_url
```

## 🐳 Docker 环境中的使用

### 容器内执行迁移
```bash
# 进入容器
docker exec -it audit_api_dev bash

# 执行迁移
alembic upgrade head
```

### 自动化迁移
在应用启动时自动执行：
```python
# app/main.py
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时自动执行数据库迁移
    import subprocess
    try:
        subprocess.run(["alembic", "upgrade", "head"], check=True)
        logger.info("数据库迁移完成")
    except subprocess.CalledProcessError as e:
        logger.error(f"数据库迁移失败: {e}")
    
    yield
```

## 🔧 常用命令

```bash
# 查看当前版本
alembic current

# 查看迁移历史
alembic history

# 生成迁移（自动检测）
alembic revision --autogenerate -m "描述信息"

# 创建空迁移文件
alembic revision -m "自定义迁移"

# 升级到最新版本
alembic upgrade head

# 升级到特定版本
alembic upgrade 0001

# 回滚到上一版本
alembic downgrade -1

# 回滚到特定版本
alembic downgrade 0001

# 显示将要执行的SQL（不实际执行）
alembic upgrade head --sql
```

## ⚠️ 注意事项

### 🚨 **生产环境使用**
1. **备份数据库**：迁移前务必备份
2. **测试迁移**：先在测试环境验证
3. **停机维护**：复杂迁移可能需要停机
4. **回滚准备**：确保回滚脚本可用

### 🔍 **开发环境使用**
1. **检查生成的迁移**：自动生成的可能需要调整
2. **描述性消息**：使用清晰的迁移描述
3. **小步迁移**：避免一次性大量变更
4. **团队同步**：及时同步迁移文件

## 🎯 实际应用示例

### 场景1：添加用户ID字段
```python
# 1. 修改模型
class RequestTracking(Base):
    # ... 现有字段
    user_id = Column(String(255), nullable=True)

# 2. 生成迁移
# alembic revision --autogenerate -m "Add user_id field"

# 3. 执行迁移
# alembic upgrade head
```

### 场景2：创建新表
```python
# 1. 定义新模型
class UserProfile(Base):
    __tablename__ = "user_profiles"
    
    id = Column(BigInteger, primary_key=True)
    username = Column(String(255), unique=True)
    email = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)

# 2. 生成迁移
# alembic revision --autogenerate -m "Create user_profiles table"

# 3. 执行迁移
# alembic upgrade head
```

## 📚 相关文档

- **[数据库迁移详细指南](database-migrations.md)** - 完整的Alembic使用教程
- **[开发环境搭建](setup.md)** - 包含Alembic安装和配置
- **[数据模型文档](../api/models.md)** - 数据库模型定义

---

`alembic.ini` 是数据库版本控制的核心配置文件，通过它可以安全、可控地管理数据库结构的变更，确保开发和生产环境的数据库一致性。

📧 如有疑问，请参考 [开发文档](setup.md) 或联系开发团队。
