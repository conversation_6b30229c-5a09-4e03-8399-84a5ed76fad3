# 审计服务文档中心

欢迎来到审计服务项目的文档中心。这里包含了项目的所有技术文档和使用指南。

## 📚 文档目录

### 🚀 部署相关文档
- **[Docker部署体系](deployment/DOCKER_DEPLOYMENT.md)** - 完整的Docker镜像构建与部署体系
- **[Docker部署指南](../docker/README.md)** - Docker部署详细使用指南
- **[生产环境ELK分析](deployment/production-elk-analysis.md)** - 生产环境ELK启动策略和最佳实践

### 👨‍💻 开发文档
- **[代码注释完善](development/DOCUMENTATION_COMMENTS.md)** - 项目注释标准和完善总结
- **[开发环境搭建](development/setup.md)** - 本地开发环境配置
- **[编码规范](development/coding-standards.md)** - 编码规范和最佳实践
- **[数据库迁移](development/database-migrations.md)** - Alembic数据库迁移详细指南
- **[Alembic配置说明](development/alembic-overview.md)** - alembic.ini文件作用和使用

### 📊 日志系统
- **[日志体系完善](logging/LOGGING_SYSTEM_ENHANCEMENT.md)** - 完整的日志系统架构和实现
- **[ELK集成指南](logging/README_ELK.md)** - ELK日志分析系统集成
- **[日志配置管理](logging/configuration.md)** - 日志配置和轮转策略

### 📈 监控系统
- **[Kibana仪表盘指南](monitoring/KIBANA_DASHBOARD_GUIDE.md)** - Kibana使用和配置指南
- **[监控访问指南](monitoring/MONITORING_ACCESS_GUIDE.md)** - 监控系统访问和使用
- **[快速仪表盘设置](monitoring/QUICK_DASHBOARD_SETUP.md)** - 快速配置监控面板

### 🔧 脚本和工具
- **[脚本使用指南](scripts/README_SCRIPTS.md)** - 完整的脚本工具使用说明
- **[启动脚本指南](scripts/STARTUP_SCRIPTS.md)** - start.sh和stop.sh详细使用

### 🧪 测试相关文档
- **[测试体系总结](testing/TESTING_SYSTEM_SUMMARY.md)** - 完整测试体系功能总结
- **[测试使用指南](../tests/README.md)** - 详细的测试使用文档

### 📖 项目文档
- **[项目主文档](../README.md)** - 项目概述和快速开始
- **[API文档](http://localhost:8000/docs)** - 在线API文档（需要启动服务）
- **[文档重组说明](DOCUMENTATION_REORGANIZATION.md)** - 文档结构重组记录

## 🗂️ 文档组织结构

```
docs/
├── README.md                                    # 本文档（文档索引）
├── api/                                        # API接口文档
├── deployment/                                 # 部署相关文档
│   ├── DOCKER_DEPLOYMENT.md                  # Docker部署体系总结
│   └── PORT_CONFIGURATION.md                 # 端口配置说明
├── development/                               # 开发相关文档
│   └── DOCUMENTATION_COMMENTS.md             # 代码注释完善总结
├── logging/                                   # 日志系统文档
│   ├── LOGGING_SYSTEM_ENHANCEMENT.md         # 日志体系完善总结
│   └── README_ELK.md                         # ELK集成指南
├── monitoring/                                # 监控系统文档
│   ├── KIBANA_DASHBOARD_GUIDE.md             # Kibana仪表盘指南
│   ├── MONITORING_ACCESS_GUIDE.md            # 监控访问指南
│   └── QUICK_DASHBOARD_SETUP.md              # 快速仪表盘设置
├── scripts/                                   # 脚本相关文档
│   ├── README_SCRIPTS.md                     # 脚本使用指南
│   └── STARTUP_SCRIPTS.md                    # 启动脚本使用指南
└── testing/                                   # 测试相关文档
    └── TESTING_SYSTEM_SUMMARY.md             # 测试体系总结

# 模块内文档
docker/
└── README.md                                  # Docker部署详细指南

tests/
└── README.md                                  # 测试使用详细指南

scripts/
└── log-management.sh                          # 日志管理脚本
```

## 🎯 快速导航

### 🚀 新用户入门
1. 阅读 [项目主文档](../README.md) 了解项目概述
2. 查看 [Docker部署指南](../docker/README.md) 快速启动项目
3. 参考 [脚本使用指南](scripts/README_SCRIPTS.md) 学习便捷启动方式
4. 访问 [监控面板](http://localhost:5601) 查看日志和监控

### 👨‍💻 开发者指南
1. 阅读 [代码注释完善](development/DOCUMENTATION_COMMENTS.md) 了解代码规范
2. 查看 [测试使用指南](../tests/README.md) 了解如何运行测试
3. 参考 [日志体系完善](logging/LOGGING_SYSTEM_ENHANCEMENT.md) 了解日志系统
4. 学习 [ELK集成指南](logging/README_ELK.md) 掌握日志分析

### 🔧 运维人员指南
1. 阅读 [Docker部署体系](deployment/DOCKER_DEPLOYMENT.md) 了解完整部署方案
2. 查看 [日志体系完善](logging/LOGGING_SYSTEM_ENHANCEMENT.md) 了解日志管理
3. 参考 [监控访问指南](monitoring/MONITORING_ACCESS_GUIDE.md) 配置监控
4. 使用 [脚本工具](scripts/README_SCRIPTS.md) 进行服务管理

### 📊 数据分析师指南
1. 学习 [Kibana仪表盘指南](monitoring/KIBANA_DASHBOARD_GUIDE.md) 使用可视化工具
2. 参考 [快速仪表盘设置](monitoring/QUICK_DASHBOARD_SETUP.md) 配置监控面板
3. 了解 [ELK集成指南](logging/README_ELK.md) 掌握日志查询技巧

## 📝 文档维护

### 文档更新原则
- **及时性**: 代码变更时同步更新相关文档
- **准确性**: 确保文档内容与实际代码一致
- **完整性**: 覆盖所有重要功能和使用场景
- **易读性**: 使用清晰的结构和示例

### 文档贡献
如果您发现文档有误或需要补充，请：
1. 创建Issue描述问题
2. 提交Pull Request修复
3. 遵循现有的文档格式和风格

## 🔗 相关链接

### 📱 服务访问
- **在线API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/v1/health
- **Swagger UI**: http://localhost:8000/docs

### 📊 监控面板
- **Kibana仪表盘**: http://localhost:5601/app/dashboards
- **日志搜索**: http://localhost:5601/app/discover
- **Flower任务监控**: http://localhost:5555
- **Elasticsearch**: http://localhost:9200

### 🛠️ 管理命令
```bash
# 启动所有服务（包含ELK）
./start.sh docker --with-elk

# 检查服务状态
./status.sh --elk

# 停止所有服务
./stop.sh all

# 日志管理
./scripts/log-management.sh analyze
```

## 📞 获取帮助

如果您在使用过程中遇到问题：

1. **查看文档**: 首先查看相关文档是否有解答
2. **检查日志**: 查看应用日志获取错误信息
3. **运行诊断**: 使用健康检查和验证脚本
4. **联系支持**: 创建Issue或联系开发团队

---

**注意**: 本文档会随着项目的发展持续更新，请定期查看最新版本。
