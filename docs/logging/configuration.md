# 📊 日志配置和管理指南

本文档详细介绍审计服务的日志配置、管理策略和最佳实践。

## 🎯 日志配置概述

审计服务采用分层的日志配置架构：
- **应用层**: Python日志配置
- **收集层**: Filebeat日志收集
- **处理层**: Logstash日志处理（可选）
- **存储层**: Elasticsearch日志存储
- **展示层**: Kibana日志可视化

## 🐍 Python日志配置

### 基础配置文件
```python
# app/utils/logging.py
import logging
import logging.config
from pathlib import Path

LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s'
        },
        'json': {
            '()': 'app.utils.elk_logging.JSONFormatter',
            'service_name': 'audit-service'
        }
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'standard'
        },
        'file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/app.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'json'
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/error.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 10,
            'formatter': 'detailed'
        }
    },
    'loggers': {
        'app': {
            'handlers': ['console', 'file', 'error_file'],
            'level': 'DEBUG',
            'propagate': False
        },
        'celery': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False
        },
        'uvicorn': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False
        }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['console']
    }
}
```

### 环境变量配置
```bash
# .env
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5
ENABLE_JSON_LOGGING=true
ENABLE_ELK_LOGGING=true
```

### 动态日志级别
```python
# app/core/config.py
import os
from enum import Enum

class LogLevel(str, Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class Settings(BaseSettings):
    log_level: LogLevel = LogLevel.INFO
    log_file_path: str = "logs/app.log"
    log_max_size: int = 10 * 1024 * 1024  # 10MB
    log_backup_count: int = 5
    enable_json_logging: bool = True
    enable_elk_logging: bool = True
```

## 📁 日志文件结构

### 目录组织
```
logs/
├── app.log                 # 主应用日志
├── app.log.1              # 轮转备份
├── app.log.2
├── celery.log             # Celery任务日志
├── celery.log.1
├── error.log              # 错误日志
├── error.log.1
├── access.log             # 访问日志
├── performance.log        # 性能日志
└── backup/                # 备份目录
    ├── audit-logs-20250801.tar.gz
    └── audit-logs-20250731.tar.gz
```

### 日志分类策略
```python
# 不同类型的日志使用不同的文件
LOGGER_CONFIGS = {
    'app': {
        'file': 'logs/app.log',
        'level': 'DEBUG',
        'format': 'json'
    },
    'celery': {
        'file': 'logs/celery.log',
        'level': 'INFO',
        'format': 'json'
    },
    'access': {
        'file': 'logs/access.log',
        'level': 'INFO',
        'format': 'combined'
    },
    'error': {
        'file': 'logs/error.log',
        'level': 'ERROR',
        'format': 'detailed'
    },
    'performance': {
        'file': 'logs/performance.log',
        'level': 'INFO',
        'format': 'json'
    }
}
```

## 🔄 日志轮转配置

### Python内置轮转
```python
import logging.handlers

# 按大小轮转
rotating_handler = logging.handlers.RotatingFileHandler(
    filename='logs/app.log',
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5,
    encoding='utf-8'
)

# 按时间轮转
timed_handler = logging.handlers.TimedRotatingFileHandler(
    filename='logs/app.log',
    when='midnight',
    interval=1,
    backupCount=30,
    encoding='utf-8'
)
```

### Logrotate配置
```bash
# /etc/logrotate.d/audit-service
/app/logs/*.log {
    daily
    rotate 30
    missingok
    notifempty
    compress
    delaycompress
    create 644 root root
    postrotate
        # 重新加载应用程序
        pkill -USR1 -f "python.*audit" > /dev/null 2>&1 || true
    endscript
}

# 错误日志特殊处理
/app/logs/error*.log {
    hourly
    rotate 168
    size 100M
    missingok
    notifempty
    compress
    delaycompress
    create 644 root root
}
```

### Docker日志轮转
```yaml
# docker-compose.yml
services:
  audit-api:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        compress: "true"
```

## 📊 Filebeat配置

### 基础配置
```yaml
# docker/configs/elk/filebeat/filebeat.yml
filebeat.inputs:
  # 应用日志
  - type: log
    enabled: true
    paths:
      - /app/logs/app.log
    fields:
      service: audit-service
      component: application
      log_type: application
    fields_under_root: true
    multiline.pattern: '^\d{4}-\d{2}-\d{2}'
    multiline.negate: true
    multiline.match: after

  # Celery日志
  - type: log
    enabled: true
    paths:
      - /app/logs/celery.log
    fields:
      service: audit-service
      component: celery
      log_type: task
    fields_under_root: true

  # 错误日志
  - type: log
    enabled: true
    paths:
      - /app/logs/error.log
    fields:
      service: audit-service
      component: error
      log_type: error
      priority: high
    fields_under_root: true

  # Docker容器日志
  - type: container
    enabled: true
    paths:
      - '/var/lib/docker/containers/*/*.log'
    processors:
      - add_docker_metadata:
          host: "unix:///var/run/docker.sock"

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "audit-logs-%{+yyyy.MM.dd}"
  template.settings:
    index.number_of_shards: 1
    index.number_of_replicas: 0

processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
  - add_docker_metadata: ~
  - add_kubernetes_metadata: ~
```

### 高级配置
```yaml
# 多行日志处理
multiline.pattern: '^[0-9]{4}-[0-9]{2}-[0-9]{2}'
multiline.negate: true
multiline.match: after
multiline.max_lines: 500
multiline.timeout: 5s

# 日志过滤
processors:
  - drop_event:
      when:
        regexp:
          message: "^DEBUG"
  - include_fields:
      fields: ["@timestamp", "message", "level", "service", "component"]

# 缓冲配置
queue.mem:
  events: 4096
  flush.min_events: 512
  flush.timeout: 1s

# 输出配置
output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  worker: 2
  bulk_max_size: 2048
  flush_interval: 1s
```

## 🔍 Elasticsearch配置

### 索引模板
```json
{
  "index_patterns": ["audit-logs-*"],
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 0,
    "index.refresh_interval": "5s",
    "index.max_result_window": 50000
  },
  "mappings": {
    "properties": {
      "@timestamp": {"type": "date"},
      "level": {"type": "keyword"},
      "service": {"type": "keyword"},
      "component": {"type": "keyword"},
      "message": {"type": "text", "analyzer": "standard"},
      "task_id": {"type": "keyword"},
      "user_id": {"type": "keyword"},
      "request_id": {"type": "keyword"},
      "duration_ms": {"type": "float"},
      "status_code": {"type": "integer"},
      "exception_type": {"type": "keyword"},
      "business_category": {"type": "keyword"},
      "log_category": {"type": "keyword"}
    }
  }
}
```

### 生命周期管理
```json
{
  "policy": {
    "phases": {
      "hot": {
        "actions": {
          "rollover": {
            "max_size": "1GB",
            "max_age": "1d"
          }
        }
      },
      "warm": {
        "min_age": "7d",
        "actions": {
          "allocate": {
            "number_of_replicas": 0
          }
        }
      },
      "cold": {
        "min_age": "30d",
        "actions": {
          "allocate": {
            "number_of_replicas": 0
          }
        }
      },
      "delete": {
        "min_age": "90d"
      }
    }
  }
}
```

## 🛠️ 日志管理工具

### 配置管理脚本
```bash
#!/bin/bash
# scripts/log-config.sh

# 动态调整日志级别
set_log_level() {
    local level=$1
    echo "设置日志级别为: $level"
    
    # 更新环境变量
    export LOG_LEVEL=$level
    
    # 重新加载配置
    pkill -USR1 -f "python.*audit" || true
}

# 重新加载日志配置
reload_log_config() {
    echo "重新加载日志配置..."
    pkill -USR1 -f "python.*audit" || true
    docker restart audit_filebeat
}

# 检查日志配置
check_log_config() {
    echo "检查日志配置..."
    
    # 检查日志文件
    ls -la logs/
    
    # 检查Filebeat状态
    docker logs audit_filebeat --tail 5
    
    # 检查Elasticsearch连接
    curl -s http://localhost:9200/_cluster/health | jq .
}
```

### 监控脚本
```bash
#!/bin/bash
# scripts/log-monitor.sh

# 监控日志文件大小
monitor_log_size() {
    echo "监控日志文件大小..."
    
    find logs/ -name "*.log" -exec ls -lh {} \; | while read line; do
        size=$(echo $line | awk '{print $5}')
        file=$(echo $line | awk '{print $9}')
        
        # 检查文件大小是否超过阈值
        if [[ $(echo $line | awk '{print $5}' | sed 's/M//g' | sed 's/G//g') -gt 100 ]]; then
            echo "警告: $file 文件过大 ($size)"
        fi
    done
}

# 监控日志错误率
monitor_error_rate() {
    echo "监控日志错误率..."
    
    local total=$(grep -c "" logs/app.log)
    local errors=$(grep -c "ERROR\|CRITICAL" logs/app.log)
    local rate=$((errors * 100 / total))
    
    echo "总日志数: $total"
    echo "错误日志数: $errors"
    echo "错误率: $rate%"
    
    if [[ $rate -gt 5 ]]; then
        echo "警告: 错误率过高 ($rate%)"
    fi
}
```

## 📈 性能优化

### 异步日志
```python
import asyncio
import logging
from concurrent.futures import ThreadPoolExecutor

class AsyncLogHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.loop = asyncio.get_event_loop()
    
    def emit(self, record):
        # 异步写入日志
        self.loop.run_in_executor(
            self.executor,
            self._write_log,
            record
        )
    
    def _write_log(self, record):
        # 实际的日志写入逻辑
        pass
```

### 批量处理
```python
import queue
import threading
import time

class BatchLogHandler(logging.Handler):
    def __init__(self, batch_size=100, flush_interval=5):
        super().__init__()
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.buffer = queue.Queue()
        self.worker_thread = threading.Thread(target=self._worker)
        self.worker_thread.daemon = True
        self.worker_thread.start()
    
    def emit(self, record):
        self.buffer.put(record)
    
    def _worker(self):
        batch = []
        last_flush = time.time()
        
        while True:
            try:
                record = self.buffer.get(timeout=1)
                batch.append(record)
                
                # 批量写入条件
                if (len(batch) >= self.batch_size or 
                    time.time() - last_flush > self.flush_interval):
                    self._flush_batch(batch)
                    batch = []
                    last_flush = time.time()
                    
            except queue.Empty:
                if batch:
                    self._flush_batch(batch)
                    batch = []
                    last_flush = time.time()
    
    def _flush_batch(self, batch):
        # 批量写入日志
        pass
```

## 🚨 故障排除

### 常见问题
1. **日志文件权限问题**
   ```bash
   chmod 644 logs/*.log
   chown app:app logs/*.log
   ```

2. **磁盘空间不足**
   ```bash
   # 清理旧日志
   find logs/ -name "*.log.*" -mtime +7 -delete
   
   # 压缩日志
   gzip logs/*.log.1
   ```

3. **Filebeat连接问题**
   ```bash
   # 检查网络连接
   docker exec audit_filebeat curl -I elasticsearch:9200
   
   # 重启Filebeat
   docker restart audit_filebeat
   ```

### 调试技巧
```python
# 临时启用调试日志
import logging
logging.getLogger('app').setLevel(logging.DEBUG)

# 查看日志配置
import logging
print(logging.getLogger('app').handlers)
print(logging.getLogger('app').level)
```

---

通过合理的日志配置和管理，可以确保系统的可观测性和问题排查能力。定期检查和优化日志配置是维护系统健康的重要环节。
