# 🚀 审计服务 + ELK 一键启动指南

## ⚡ 快速开始（30秒启动）

### 🎯 一键启动（推荐）
```bash
# 启动所有服务（包含ELK日志分析系统）
./start-with-elk.sh
```

**就这么简单！** 脚本会自动：
- ✅ 启动所有基础服务（API、数据库、缓存等）
- ✅ 启动ELK日志分析系统（Elasticsearch、Logstash、Kibana）
- ✅ 自动创建索引模式和仪表盘
- ✅ 生成示例数据用于演示
- ✅ 配置完整的监控可视化

### 🌐 启动后立即可用的地址

| 服务 | 地址 | 说明 |
|------|------|------|
| **🎯 Kibana仪表盘** | **http://localhost:5601/app/dashboards** | **预配置的监控仪表盘** |
| 🌐 API服务 | http://localhost:8000 | 审计API接口 |
| 📚 API文档 | http://localhost:8000/docs | 交互式API文档 |
| 🔍 日志搜索 | http://localhost:5601/app/discover | 日志搜索和分析 |
| 🌸 任务监控 | http://localhost:5555 | Celery任务监控 |

## 🎨 预配置的可视化功能

启动后无需任何配置，直接包含：

### 📊 监控仪表盘
- **API请求时间线**: 实时API调用趋势
- **日志级别分布**: 错误、警告、信息日志统计
- **响应时间分布**: API性能分析
- **用户活动统计**: 最活跃用户排行

### 🔍 日志分析
- **全文搜索**: 支持复杂查询语法
- **时间范围过滤**: 灵活的时间选择
- **字段过滤**: 按用户、端点、状态等过滤
- **实时更新**: 自动刷新最新日志

### 📈 业务指标
- **审计事件分析**: 操作类型统计
- **合规性监控**: 合规状态趋势
- **用户行为分析**: 操作模式识别

## 🔧 管理命令

### 启动服务
```bash
# 一键启动所有服务
./start-with-elk.sh

# 仅启动基础服务
./start.sh docker -e dev

# 仅启动ELK服务
./docker/scripts/elk-manage.sh start
```

### 停止服务
```bash
# 停止所有服务
./stop-all.sh

# 仅停止基础服务
./stop.sh

# 仅停止ELK服务
./docker/scripts/elk-manage.sh stop
```

### 查看状态
```bash
# 查看所有容器状态
docker ps

# 查看ELK服务状态
./docker/scripts/elk-manage.sh status

# 查看服务日志
tail -f logs/app.log
```

## 🎯 使用示例

### 1. 查看预配置的仪表盘
1. 访问：http://localhost:5601/app/dashboards
2. 点击"审计服务监控仪表盘"
3. 查看实时监控数据

### 2. 搜索日志
1. 访问：http://localhost:5601/app/discover
2. 在搜索框输入查询，例如：
   ```
   level:ERROR                    # 查看错误日志
   user_id:"admin"               # 查看特定用户活动
   response_time:>1000           # 查看慢请求
   @timestamp:[now-1h TO now]    # 查看最近1小时日志
   ```

### 3. 测试API
1. 访问：http://localhost:8000/docs
2. 尝试调用API接口
3. 在Kibana中查看生成的日志

## 📊 示例数据

启动后自动包含50条示例日志数据：
- 👥 6个不同用户（admin, user1-3, analyst, auditor）
- 🌐 4个API端点（/api/v1/audit, /users, /reports, /settings）
- 📝 4种日志级别（INFO, WARN, ERROR, DEBUG）
- 🔄 5种操作类型（CREATE, UPDATE, DELETE, VIEW, EXPORT）
- ⏱️ 过去24小时内的随机时间戳

## 🔍 常用查询示例

在Kibana的搜索框中使用：

```bash
# 基础查询
*                                 # 查看所有日志
level:INFO                        # 查看INFO级别日志
service:"audit_api"               # 查看API服务日志

# 用户相关
user_id:"admin"                   # 查看管理员活动
user_id:("admin" OR "analyst")    # 查看多个用户

# 性能分析
response_time:>500                # 查看慢请求
status_code:500                   # 查看服务器错误
method:POST                       # 查看POST请求

# 时间范围
@timestamp:[now-1h TO now]        # 最近1小时
@timestamp:[now-1d TO now]        # 最近1天
@timestamp:[2025-08-01 TO now]    # 指定日期开始

# 组合查询
level:ERROR AND user_id:"admin"   # 管理员的错误日志
endpoint:"/api/v1/audit" AND response_time:>1000  # 审计API的慢请求
```

## 🛠️ 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
lsof -i :5601  # Kibana
lsof -i :9200  # Elasticsearch

# 停止占用端口的服务
./stop-all.sh
```

#### 2. 服务启动失败
```bash
# 查看详细日志
docker logs audit_elasticsearch
docker logs audit_kibana

# 重新启动
./stop-all.sh
./start-with-elk.sh
```

#### 3. Kibana无法访问
```bash
# 检查Kibana状态
curl http://localhost:5601/api/status

# 查看自动配置日志
tail -f /tmp/kibana-auto-setup.log
```

#### 4. 没有数据显示
```bash
# 检查Elasticsearch中的数据
curl http://localhost:9200/audit-logs-*/_search

# 重新生成示例数据
./docker/scripts/auto-setup-kibana.sh
```

## 📚 详细文档

- [完整配置指南](docs/monitoring/KIBANA_DASHBOARD_GUIDE.md)
- [监控访问指南](docs/monitoring/MONITORING_ACCESS_GUIDE.md)
- [快速配置指南](docs/monitoring/QUICK_DASHBOARD_SETUP.md)

## 🎉 总结

通过 `./start-with-elk.sh` 一键启动，您将获得：

✅ **完整的审计服务**：API、数据库、缓存、任务队列
✅ **强大的日志分析**：Elasticsearch + Kibana
✅ **预配置的仪表盘**：无需手动设置
✅ **示例数据**：立即可用的演示数据
✅ **自动化配置**：零手动配置
✅ **实时监控**：30秒刷新的实时数据

**开始使用只需要一个命令！** 🚀
